<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>串口调试平台 - 实时数据监控</title>
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Toast通知系统 -->
    <link rel="stylesheet" href="css/toast-system.css">
    
    <!-- Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>

    <!-- SheetJS库用于处理Excel文件 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

    <!-- API服务模块 -->
    <script src="js/api-service.js"></script>
    <script src="js/auth-api.js"></script>
    <script src="js/auth-guard.js"></script>
    <script src="js/serial-api.js"></script>
    <script src="js/export-api.js"></script>
    <script src="js/stats-api.js"></script>
    <script src="js/websocket-client.js"></script>
    <script src="js/global-error-handler.js"></script>
    <script src="js/loading-manager.js"></script>
    <script src="js/toast-system.js"></script>
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            overflow-x: auto;
            overflow-y: auto;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(147, 197, 253, 0.3) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.25) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(167, 243, 208, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 60% 80%, rgba(254, 202, 202, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 90% 60%, rgba(253, 230, 138, 0.15) 0%, transparent 60%),
                linear-gradient(135deg, #f8fafc 0%, #f1f5f9 20%, #e2e8f0 40%, #f0f9ff 60%, #fce7f3 80%, #fce7f3 100%);
            background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
            color: #1e293b;
            overflow-x: auto;
            overflow-y: auto;
            position: relative;
            animation: backgroundShift 30s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background-position: 0% 0%, 100% 100%, 50% 50%, 25% 75%, 90% 60%, 0% 0%;
            }
            16% {
                background-position: 10% 20%, 90% 80%, 30% 70%, 45% 55%, 80% 40%, 16% 16%;
            }
            33% {
                background-position: 30% 40%, 70% 60%, 60% 30%, 65% 35%, 70% 80%, 33% 33%;
            }
            50% {
                background-position: 60% 80%, 40% 20%, 80% 60%, 25% 75%, 60% 20%, 50% 50%;
            }
            66% {
                background-position: 80% 60%, 20% 40%, 40% 80%, 75% 25%, 50% 60%, 66% 66%;
            }
            83% {
                background-position: 40% 20%, 60% 80%, 20% 40%, 55% 65%, 40% 80%, 83% 83%;
            }
        }

        /* 增强动态背景元素 */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .animated-shape {
            position: absolute;
            border-radius: 50%;
            animation: float 25s ease-in-out infinite;
        }

        .shape1 {
            width: 400px;
            height: 400px;
            top: 5%;
            left: 5%;
            background: radial-gradient(circle, rgba(147, 197, 253, 0.4) 0%, rgba(191, 219, 254, 0.15) 100%);
            animation-delay: 0s;
        }

        .shape2 {
            width: 300px;
            height: 300px;
            top: 50%;
            right: 5%;
            background: radial-gradient(circle, rgba(196, 181, 253, 0.35) 0%, rgba(221, 214, 254, 0.15) 100%);
            animation-delay: 8s;
        }

        .shape3 {
            width: 250px;
            height: 250px;
            bottom: 10%;
            left: 15%;
            background: radial-gradient(circle, rgba(167, 243, 208, 0.4) 0%, rgba(209, 250, 229, 0.15) 100%);
            animation-delay: 16s;
        }

        .shape4 {
            width: 350px;
            height: 350px;
            top: 25%;
            right: 25%;
            background: radial-gradient(circle, rgba(254, 202, 202, 0.35) 0%, rgba(254, 226, 226, 0.15) 100%);
            animation-delay: 12s;
        }

        .shape5 {
            width: 200px;
            height: 200px;
            top: 70%;
            left: 60%;
            background: radial-gradient(circle, rgba(253, 230, 138, 0.4) 0%, rgba(254, 240, 138, 0.15) 100%);
            animation-delay: 4s;
        }

        .shape6 {
            width: 180px;
            height: 180px;
            top: 15%;
            left: 70%;
            background: radial-gradient(circle, rgba(252, 231, 243, 0.35) 0%, rgba(253, 242, 248, 0.15) 100%);
            animation-delay: 20s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.4;
            }
            25% {
                transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.1);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-60px) translateX(-10px) rotate(180deg) scale(0.9);
                opacity: 0.6;
            }
            75% {
                transform: translateY(-20px) translateX(-30px) rotate(270deg) scale(1.05);
                opacity: 0.3;
            }
        }

        /* 粒子效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particleFloat 15s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* 光效 */
        .light-effects {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .light-beam {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 100%);
            animation: lightSweep 12s ease-in-out infinite;
        }

        .light-beam:nth-child(1) { left: 10%; animation-delay: 0s; }
        .light-beam:nth-child(2) { left: 30%; animation-delay: 4s; }
        .light-beam:nth-child(3) { left: 60%; animation-delay: 8s; }
        .light-beam:nth-child(4) { left: 85%; animation-delay: 2s; }

        @keyframes lightSweep {
            0%, 100% {
                opacity: 0;
                transform: scaleY(0);
            }
            50% {
                opacity: 1;
                transform: scaleY(1);
            }
        }

        /* 页面加载动画 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #7dd3fc 75%, #38bdf8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.6s ease, visibility 0.6s ease;
        }

        .page-loader.loaded {
            opacity: 0;
            visibility: hidden;
        }

        .loader-content {
            text-align: center;
            color: #0369a1;
        }

        .spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 4px solid transparent;
            border-top-color: #0369a1;
            animation: spin 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
        }

        .spinner-ring:nth-child(1) {
            animation-delay: 0s;
        }

        .spinner-ring:nth-child(2) {
            width: 80%;
            height: 80%;
            border-top-color: #0284c7;
            animation-delay: 0.15s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 60%;
            height: 60%;
            border-top-color: #0ea5e9;
            animation-delay: 0.3s;
        }

        .spinner-dot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #0369a1;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(3, 105, 161, 0.5);
            animation: spinnerDot 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes spinnerDot {
            0%, 100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .loading-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(3, 105, 161, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #0369a1, #0ea5e9);
            border-radius: 2px;
            animation: loadingProgress 2s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% {
                width: 0%;
                transform: translateX(-100%);
            }
            50% {
                width: 100%;
                transform: translateX(0%);
            }
            100% {
                width: 100%;
                transform: translateX(100%);
            }
        }

        /* 主容器布局 */
        .main-container {
            display: grid;
            grid-template-columns: 260px 1fr;
            min-height: 100vh;
            height: auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            min-width: 1750px;
        }

        /* 固定水平滚动条 */
        .horizontal-scrollbar {
            position: fixed !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 25px !important;
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-top: 2px solid rgba(59, 130, 246, 0.3) !important;
            z-index: 9999 !important;
            overflow-x: auto !important;
            overflow-y: hidden !important;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
            display: block !important;
            visibility: visible !important;
            width: 100% !important;
        }

        .horizontal-scrollbar-content {
            height: 20px;
            width: 3000px;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
            min-width: 3000px;
        }

        /* 确保滚动条可见 */
        .horizontal-scrollbar::-webkit-scrollbar {
            height: 12px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.6);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.8);
        }

        /* 左侧导航栏 */
        .sidebar {
            background: rgba(255, 255, 255, 0.92);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                4px 0 25px rgba(0, 0, 0, 0.06),
                2px 0 15px rgba(59, 130, 246, 0.08);
            overflow-y: auto;
            position: relative;
            padding: 15px 12px;
        }

        .sidebar-header {
            padding: 20px 15px;
            text-align: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-radius: 16px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 导航菜单 */
        .nav-section {
            padding: 25px 20px;
        }

        .nav-category {
            margin-bottom: 25px;
        }

        .category-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .category-header:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .category-icon {
            font-size: 18px;
            color: #3b82f6;
            margin-right: 12px;
            width: 20px;
        }

        .category-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .category-arrow {
            font-size: 12px;
            color: #3b82f6;
            transition: transform 0.3s ease;
        }

        .category-header.expanded .category-arrow {
            transform: rotate(180deg);
        }

        .nav-submenu {
            list-style: none;
            margin-top: 10px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .nav-category.expanded .nav-submenu {
            max-height: 300px;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 45px;
            color: #64748b;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), transparent);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link:hover {
            color: #3b82f6;
            transform: translateX(8px);
        }

        .nav-icon {
            font-size: 16px;
            margin-right: 10px;
            width: 18px;
            color: #94a3b8;
            transition: color 0.3s ease;
        }

        .nav-link:hover .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .nav-link.active .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active::before {
            width: 100%;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* 意见反馈样式 */
        .feedback-section {
            position: absolute;
            bottom: 20px;
            left: 12px;
            right: 12px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 20px;
            color: #3b82f6;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link:hover {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .feedback-icon {
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .feedback-link:hover .feedback-icon {
            transform: scale(1.1);
        }

        .feedback-text {
            font-size: 14px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-dot"></div>
            </div>
            <div class="loading-text">正在加载串口调试平台...</div>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- 增强动态背景元素 -->
    <div class="animated-bg">
        <div class="animated-shape shape1"></div>
        <div class="animated-shape shape2"></div>
        <div class="animated-shape shape3"></div>
        <div class="animated-shape shape4"></div>
        <div class="animated-shape shape5"></div>
        <div class="animated-shape shape6"></div>
    </div>

    <!-- 粒子效果 -->
    <div class="particles" id="particles"></div>

    <!-- 光效 -->
    <div class="light-effects">
        <div class="light-beam"></div>
        <div class="light-beam"></div>
        <div class="light-beam"></div>
        <div class="light-beam"></div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-terminal"></i>
                </div>
                <h1 class="sidebar-title">串口调试平台</h1>
            </div>

            <nav class="nav-section">
                <!-- 信号可视化分类 -->
                <div class="nav-category expanded">
                    <div class="category-header expanded" data-category="signal-visualization">
                        <i class="fas fa-chart-line category-icon"></i>
                        <span class="category-title">信号可视化</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="data.html" class="nav-link" data-page="data">
                                <i class="fas fa-upload nav-icon"></i>
                                <span class="nav-text">数据上传</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="serial-debug.html" class="nav-link active" data-page="serial-debug">
                                <i class="fas fa-terminal nav-icon"></i>
                                <span class="nav-text">串口调试</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号处理分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-processing">
                        <i class="fas fa-wave-square category-icon"></i>
                        <span class="category-title">信号处理</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="signal-denoise.html" class="nav-link" data-page="signal-denoise">
                                <i class="fas fa-magic nav-icon"></i>
                                <span class="nav-text">信号去噪</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号分析分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-analysis">
                        <i class="fas fa-chart-area category-icon"></i>
                        <span class="category-title">信号分析</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="fft-analysis.html" class="nav-link" data-page="fft-analysis">
                                <i class="fas fa-chart-bar nav-icon"></i>
                                <span class="nav-text">频谱分析</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 意见反馈 - 底部固定位置 -->
            <div class="feedback-section">
                <a href="feedback.html" class="feedback-link">
                    <i class="fas fa-comment-dots feedback-icon"></i>
                    <span class="feedback-text">意见反馈</span>
                </a>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <style>
                /* 主内容区域样式 */
                .main-content {
                    padding: 15px 20px 30px 20px;
                    background: rgba(255, 255, 255, 0.02);
                    min-height: 100vh;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    overflow-x: auto;
                    overflow-y: auto;
                }

                /* 内容布局 */
                .content-layout {
                    display: grid;
                    grid-template-columns: 320px 1fr;
                    gap: 25px;
                    min-width: 1750px;
                    width: max-content;
                    align-items: stretch;
                    padding: 20px;
                    padding-bottom: 15px;
                    flex: 1;
                    min-height: 0;
                }

                /* 左侧控制面板 */
                .control-panel {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                    overflow-y: auto;
                    padding-bottom: 10px;
                    padding-right: 5px;
                    /* 高度将通过JavaScript动态设置 */
                    flex-shrink: 0;
                    min-height: 0;
                }

                .control-card {
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(25px);
                    border-radius: 20px;
                    padding: 20px;
                    box-shadow:
                        0 10px 40px rgba(0, 0, 0, 0.06),
                        0 4px 20px rgba(59, 130, 246, 0.08),
                        inset 0 1px 0 rgba(255, 255, 255, 0.8);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: visible;
                    flex-shrink: 0;
                    width: 100%;
                    box-sizing: border-box;
                }

                .control-card:first-child {
                    flex: 0 0 auto;
                    margin-bottom: 0;
                }

                .control-card.serial-connection-card {
                    flex: 0 0 auto;
                    min-height: auto;
                    height: fit-content;
                }

                /* 统计分析卡片特定样式 */
                .control-card:last-child {
                    height: fit-content;
                    min-height: auto;
                    max-height: fit-content;
                    overflow: visible;
                    padding-bottom: 20px !important;
                    background: rgba(255, 255, 255, 0.95) !important;
                    backdrop-filter: blur(25px) !important;
                }

                .control-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
                }

                .control-card:hover {
                    transform: translateY(-10px) scale(1.02);
                    box-shadow:
                        0 25px 70px rgba(0, 0, 0, 0.12),
                        0 8px 30px rgba(59, 130, 246, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.9);
                    border-color: rgba(255, 255, 255, 0.5);
                }

                .control-card:active {
                    transform: translateY(-4px) scale(0.98);
                    transition: all 0.1s ease;
                }

                .control-card h3 {
                    font-size: 18px;
                    font-weight: 600;
                    color: #1e293b;
                    margin-bottom: 20px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .control-card h3 i {
                    color: #3b82f6;
                    font-size: 20px;
                }

                /* 串口连接卡片 */
                .serial-connection-card {
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
                    border: 1px solid rgba(59, 130, 246, 0.2);
                }

                .connection-status {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                    padding: 15px;
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    margin-bottom: 20px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    transition: all 0.3s ease;
                }

                .connection-status.connected {
                    border-color: rgba(34, 197, 94, 0.4);
                    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
                }

                .connection-status.disconnected {
                    border-color: rgba(239, 68, 68, 0.4);
                    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
                }

                .status-indicator {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background: #94a3b8;
                    animation: pulse 2s infinite;
                }

                .status-indicator.connected {
                    background: #22c55e;
                }

                .status-indicator.disconnected {
                    background: #ef4444;
                }

                @keyframes pulse {
                    0%, 100% {
                        opacity: 1;
                        transform: scale(1);
                    }
                    50% {
                        opacity: 0.7;
                        transform: scale(1.1);
                    }
                }

                .status-text {
                    font-size: 14px;
                    font-weight: 600;
                    color: #1e293b;
                }

                /* 串口配置样式 */
                .serial-config {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                    margin-bottom: 20px;
                }

                .config-group {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }

                .config-group label {
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                }

                .custom-select {
                    position: relative;
                    display: block;
                }

                .custom-select select {
                    width: 100%;
                    padding: 12px 45px 12px 16px;
                    border: 2px solid transparent;
                    border-radius: 12px;
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
                    backdrop-filter: blur(20px);
                    color: #1e293b;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    appearance: none;
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    box-shadow:
                        0 4px 15px rgba(0, 0, 0, 0.08),
                        0 2px 8px rgba(59, 130, 246, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.8);
                }

                .custom-select select:hover {
                    border-color: rgba(59, 130, 246, 0.3);
                    box-shadow:
                        0 6px 25px rgba(0, 0, 0, 0.12),
                        0 4px 15px rgba(59, 130, 246, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.9);
                    transform: translateY(-1px);
                }

                .custom-select select:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow:
                        0 8px 30px rgba(0, 0, 0, 0.15),
                        0 4px 20px rgba(59, 130, 246, 0.25),
                        0 0 0 4px rgba(59, 130, 246, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 1);
                    transform: translateY(-2px);
                }

                .custom-select::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
                    border-radius: 12px;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    pointer-events: none;
                    z-index: 1;
                }

                .custom-select:hover::before {
                    opacity: 1;
                }

                .custom-select::after {
                    content: '\f107';
                    font-family: 'Font Awesome 6 Free';
                    font-weight: 900;
                    position: absolute;
                    top: 50%;
                    right: 16px;
                    transform: translateY(-50%);
                    color: #3b82f6;
                    font-size: 16px;
                    pointer-events: none;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    z-index: 2;
                }

                .custom-select:hover::after {
                    transform: translateY(-50%) scale(1.1) rotate(180deg);
                    color: #2563eb;
                }

                /* 选项样式 */
                .custom-select select option {
                    padding: 12px 16px;
                    background: rgba(255, 255, 255, 0.95);
                    color: #1e293b;
                    font-weight: 500;
                    border: none;
                }

                .custom-select select option:hover {
                    background: rgba(59, 130, 246, 0.1);
                }

                .custom-select select option:checked {
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
                    color: #1d4ed8;
                    font-weight: 600;
                }

                /* 连接按钮样式 */
                .connection-buttons {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                    margin-bottom: 20px;
                }

                .btn {
                    padding: 12px 20px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                    text-decoration: none;
                }

                .btn-connect {
                    background: linear-gradient(135deg, #22c55e, #16a34a);
                    color: white;
                    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
                }

                .btn-connect:hover {
                    transform: translateY(-3px) scale(1.05);
                    box-shadow: 0 12px 35px rgba(34, 197, 94, 0.5);
                    background: linear-gradient(135deg, #16a34a, #15803d);
                }

                .btn-disconnect {
                    background: linear-gradient(135deg, #ef4444, #dc2626);
                    color: white;
                    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
                }

                .btn-disconnect:hover {
                    transform: translateY(-3px) scale(1.05);
                    box-shadow: 0 12px 35px rgba(239, 68, 68, 0.5);
                    background: linear-gradient(135deg, #dc2626, #b91c1c);
                }

                .btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                    transform: none !important;
                    box-shadow: none !important;
                }

                /* 监视器状态样式 */
                .monitor-status {
                    margin-left: auto;
                }

                .status-badge {
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .status-badge.receiving {
                    background: linear-gradient(135deg, #22c55e, #16a34a);
                    color: white;
                    animation: pulse 2s infinite;
                }

                .status-badge.paused {
                    background: linear-gradient(135deg, #f59e0b, #d97706);
                    color: white;
                }

                .status-badge.no-data {
                    background: linear-gradient(135deg, #94a3b8, #64748b);
                    color: white;
                }

                /* 监视器容器样式 */
                .monitor-container {
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 12px;
                    overflow: hidden;
                    background: rgba(255, 255, 255, 0.9);
                }

                .monitor-header {
                    padding: 10px 15px;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
                    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .monitor-controls {
                    display: flex;
                    gap: 8px;
                }

                .btn-small {
                    padding: 6px 12px;
                    font-size: 11px;
                    border-radius: 6px;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }

                .btn-clear {
                    background: linear-gradient(135deg, #ef4444, #dc2626);
                    color: white;
                }

                .btn-pause {
                    background: linear-gradient(135deg, #f59e0b, #d97706);
                    color: white;
                }

                .btn-small:hover {
                    transform: translateY(-2px) scale(1.05);
                }

                .monitor-display {
                    height: 150px;
                    padding: 15px;
                    overflow-y: auto;
                    font-family: 'Courier New', monospace;
                    font-size: 11px;
                    line-height: 1.4;
                    background: #1e293b;
                    color: #e2e8f0;
                }

                .monitor-placeholder {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    color: #64748b;
                    text-align: center;
                }

                .monitor-placeholder i {
                    font-size: 24px;
                    margin-bottom: 10px;
                    color: #3b82f6;
                }

                .monitor-line {
                    margin-bottom: 2px;
                    padding: 2px 0;
                    border-left: 3px solid transparent;
                    padding-left: 8px;
                }

                .monitor-line.received {
                    border-left-color: #22c55e;
                    color: #86efac;
                }

                .monitor-line.sent {
                    border-left-color: #3b82f6;
                    color: #93c5fd;
                }

                .monitor-line.error {
                    border-left-color: #ef4444;
                    color: #fca5a5;
                }

                /* 发送容器样式 */
                .send-container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }

                .send-input-group {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }

                .send-input-group label {
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                }

                .send-input-group textarea {
                    width: 100%;
                    padding: 10px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 8px;
                    background: rgba(255, 255, 255, 0.9);
                    color: #1e293b;
                    font-size: 12px;
                    font-family: 'Courier New', monospace;
                    resize: vertical;
                    transition: all 0.3s ease;
                }

                .send-input-group textarea:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }

                .send-options {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                }

                .send-format, .send-interval {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }

                .send-interval input {
                    width: 100%;
                    padding: 10px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 8px;
                    background: rgba(255, 255, 255, 0.9);
                    color: #1e293b;
                    font-size: 12px;
                    transition: all 0.3s ease;
                }

                .send-interval input:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }

                .send-buttons {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                }

                .btn-primary {
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    color: white;
                    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                }

                .btn-primary:hover {
                    transform: translateY(-3px) scale(1.05);
                    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.5);
                    background: linear-gradient(135deg, #2563eb, #0284c7);
                }

                .btn-outline {
                    background: transparent;
                    color: #3b82f6;
                    border: 2px solid #3b82f6;
                    position: relative;
                    overflow: hidden;
                }

                .btn-outline::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                    transition: left 0.5s;
                }

                .btn-outline:hover::before {
                    left: 100%;
                }

                .btn-outline:hover {
                    background: linear-gradient(135deg, #3b82f6, #2563eb);
                    color: white;
                    transform: translateY(-2px) scale(1.05);
                    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
                }

                /* 快速发送样式 */
                .quick-send {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }

                .quick-send label {
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                }

                .quick-send-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                }

                .quick-send-btn {
                    padding: 8px 12px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 6px;
                    background: rgba(255, 255, 255, 0.9);
                    color: #3b82f6;
                    font-size: 11px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .quick-send-btn:hover {
                    background: rgba(59, 130, 246, 0.1);
                    border-color: rgba(59, 130, 246, 0.4);
                    transform: translateY(-2px);
                }

                /* 协议解析样式 */
                .protocol-container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }

                .protocol-selector {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }

                .protocol-selector label {
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748b;
                }

                .protocol-config {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    padding: 15px;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
                    border-radius: 8px;
                    border: 1px solid rgba(59, 130, 246, 0.2);
                }

                .config-item {
                    display: flex;
                    flex-direction: column;
                    gap: 6px;
                }

                .config-item label {
                    font-size: 11px;
                    font-weight: 500;
                    color: #64748b;
                }

                .config-item input {
                    width: 100%;
                    padding: 8px 10px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 6px;
                    background: rgba(255, 255, 255, 0.9);
                    color: #1e293b;
                    font-size: 11px;
                    transition: all 0.3s ease;
                }

                .config-item input:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
                }

                .protocol-status {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    padding: 12px;
                    background: rgba(255, 255, 255, 0.8);
                    border-radius: 8px;
                    border: 1px solid rgba(59, 130, 246, 0.1);
                }

                .status-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 11px;
                }

                .status-item label {
                    color: #64748b;
                    font-weight: 500;
                }

                .status-item span {
                    color: #1e293b;
                    font-weight: 600;
                    font-family: 'Courier New', monospace;
                }

                /* 统计信息样式 */
                .statistics-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin-bottom: 15px;
                }

                .stat-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    background: rgba(59, 130, 246, 0.05);
                    border-radius: 6px;
                    border: 1px solid rgba(59, 130, 246, 0.1);
                    font-size: 11px;
                }

                .stat-item label {
                    color: #64748b;
                    font-weight: 500;
                }

                .stat-item span {
                    color: #1e293b;
                    font-weight: 600;
                    font-family: 'Courier New', monospace;
                }

                .statistics-actions {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                }

                /* 自定义滚动条样式 */
                .control-panel::-webkit-scrollbar {
                    width: 8px;
                }

                .control-panel::-webkit-scrollbar-track {
                    background: rgba(148, 163, 184, 0.1);
                    border-radius: 4px;
                }

                .control-panel::-webkit-scrollbar-thumb {
                    background: rgba(59, 130, 246, 0.3);
                    border-radius: 4px;
                    transition: background 0.3s ease;
                }

                .control-panel::-webkit-scrollbar-thumb:hover {
                    background: rgba(59, 130, 246, 0.5);
                }

                .monitor-display::-webkit-scrollbar {
                    width: 6px;
                }

                .monitor-display::-webkit-scrollbar-track {
                    background: rgba(148, 163, 184, 0.2);
                    border-radius: 3px;
                }

                .monitor-display::-webkit-scrollbar-thumb {
                    background: rgba(59, 130, 246, 0.4);
                    border-radius: 3px;
                }

                .monitor-display::-webkit-scrollbar-thumb:hover {
                    background: rgba(59, 130, 246, 0.6);
                }
            </style>

            <div class="content-layout">
                <!-- 左侧控制面板 -->
                <div class="control-panel">
                    <!-- 串口连接卡片 -->
                    <div class="control-card serial-connection-card">
                        <h3>
                            <i class="fas fa-plug"></i>
                            串口连接
                        </h3>

                        <!-- 连接状态 -->
                        <div class="connection-status disconnected" id="connectionStatus">
                            <div class="status-indicator disconnected" id="statusIndicator"></div>
                            <span class="status-text" id="statusText">未连接</span>
                        </div>

                        <!-- 串口配置 -->
                        <div class="serial-config">
                            <div class="config-group" style="grid-column: 1 / -1;">
                                <label>串口选择</label>
                                <div style="display: flex; gap: 8px;">
                                    <div class="custom-select" style="flex: 1;">
                                        <select id="portSelect">
                                            <option value="">请选择串口</option>
                                        </select>
                                    </div>
                                    <button class="btn btn-outline" id="selectPortBtn" style="padding: 8px 12px; font-size: 12px;">
                                        <i class="fas fa-search"></i>
                                        选择串口
                                    </button>
                                </div>
                            </div>
                            <div class="config-group">
                                <label>波特率</label>
                                <div class="custom-select">
                                    <select id="baudRateSelect">
                                        <option value="19200">19200</option>
                                        <option value="38400">38400</option>
                                        <option value="43000">43000</option>
                                        <option value="57600">57600</option>
                                        <option value="76800">76800</option>
                                        <option value="115200" selected>115200</option>
                                        <option value="128000">128000</option>
                                        <option value="230400">230400</option>
                                        <option value="256000">256000</option>
                                        <option value="460800">460800</option>
                                        <option value="921600">921600</option>
                                        <option value="1000000">1000000</option>
                                        <option value="2000000">2000000</option>
                                        <option value="3000000">3000000</option>
                                    </select>
                                </div>
                            </div>
                            <div class="config-group">
                                <label>数据位</label>
                                <div class="custom-select">
                                    <select id="dataBitsSelect">
                                        <option value="5">5位</option>
                                        <option value="6">6位</option>
                                        <option value="7">7位</option>
                                        <option value="8" selected>8位</option>
                                    </select>
                                </div>
                            </div>
                            <div class="config-group">
                                <label>停止位</label>
                                <div class="custom-select">
                                    <select id="stopBitsSelect">
                                        <option value="1" selected>1位</option>
                                        <option value="1.5">1.5位</option>
                                        <option value="2">2位</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 连接按钮 -->
                        <div class="connection-buttons">
                            <button class="btn btn-connect" id="connectBtn">
                                <i class="fas fa-play"></i>
                                连接
                            </button>
                            <button class="btn btn-disconnect" id="disconnectBtn" disabled>
                                <i class="fas fa-stop"></i>
                                断开
                            </button>
                        </div>
                    </div>

                    <!-- 串口监视器卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-eye"></i>
                            串口监视器
                            <div class="monitor-status" id="monitorStatus">
                                <span class="status-badge receiving">接收中</span>
                            </div>
                        </h3>

                        <div class="monitor-container">
                            <div class="monitor-header">
                                <div class="monitor-controls">
                                    <button class="btn-small btn-clear" id="clearMonitorBtn">
                                        <i class="fas fa-trash"></i>
                                        清空
                                    </button>
                                    <button class="btn-small btn-pause" id="pauseMonitorBtn">
                                        <i class="fas fa-pause"></i>
                                        暂停
                                    </button>
                                </div>
                            </div>
                            <div class="monitor-display" id="monitorDisplay">
                                <div class="monitor-placeholder">
                                    <i class="fas fa-terminal"></i>
                                    <p>等待串口数据...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据发送卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-paper-plane"></i>
                            数据发送
                        </h3>

                        <div class="send-container">
                            <div class="send-input-group">
                                <label>发送数据 (HEX格式)</label>
                                <textarea id="sendDataInput" placeholder="例如: D0 D1 D2 D3 D4 D5 D6 D7" rows="3"></textarea>
                            </div>

                            <div class="send-options">
                                <div class="send-format">
                                    <label>发送格式</label>
                                    <div class="custom-select">
                                        <select id="sendFormatSelect">
                                            <option value="hex" selected>HEX</option>
                                            <option value="ascii">ASCII</option>
                                            <option value="decimal">十进制</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="send-interval">
                                    <label>发送间隔 (ms)</label>
                                    <input type="number" id="sendIntervalInput" value="1000" min="10" max="10000">
                                </div>
                            </div>

                            <div class="send-buttons">
                                <button class="btn btn-primary" id="sendOnceBtn">
                                    <i class="fas fa-paper-plane"></i>
                                    发送一次
                                </button>
                                <button class="btn btn-outline" id="sendRepeatBtn">
                                    <i class="fas fa-repeat"></i>
                                    循环发送
                                </button>
                            </div>

                            <!-- 快速发送预设 -->
                            <div class="quick-send">
                                <label>快速发送</label>
                                <div class="quick-send-grid">
                                    <button class="quick-send-btn" data-value="AA BB CC DD">测试数据1</button>
                                    <button class="quick-send-btn" data-value="01 02 03 04">测试数据2</button>
                                    <button class="quick-send-btn" data-value="FF FE FD FC">测试数据3</button>
                                    <button class="quick-send-btn" data-value="00 11 22 33">测试数据4</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 协议解析卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-code"></i>
                            协议解析
                        </h3>

                        <div class="protocol-container">
                            <div class="protocol-selector">
                                <label>协议类型</label>
                                <div class="custom-select">
                                    <select id="protocolSelect">
                                        <option value="none">无协议</option>
                                        <option value="modbus">Modbus RTU</option>
                                        <option value="custom">自定义协议</option>
                                    </select>
                                </div>
                            </div>

                            <div class="protocol-config" id="protocolConfig" style="display: none;">
                                <div class="config-item">
                                    <label>帧头</label>
                                    <input type="text" id="frameHeaderInput" placeholder="例如: AA BB">
                                </div>
                                <div class="config-item">
                                    <label>帧尾</label>
                                    <input type="text" id="frameFooterInput" placeholder="例如: 0D 0A">
                                </div>
                                <div class="config-item">
                                    <label>数据长度位置</label>
                                    <input type="number" id="lengthPositionInput" placeholder="字节位置" min="0">
                                </div>
                            </div>

                            <div class="protocol-status">
                                <div class="status-item">
                                    <label>已解析帧数:</label>
                                    <span id="parsedFrameCount">0</span>
                                </div>
                                <div class="status-item">
                                    <label>错误帧数:</label>
                                    <span id="errorFrameCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-chart-bar"></i>
                            统计信息
                        </h3>

                        <div class="statistics-grid">
                            <div class="stat-item">
                                <label>接收字节数:</label>
                                <span id="receivedBytesCount">0</span>
                            </div>
                            <div class="stat-item">
                                <label>发送字节数:</label>
                                <span id="sentBytesCount">0</span>
                            </div>
                            <div class="stat-item">
                                <label>数据包数:</label>
                                <span id="packetCount">0</span>
                            </div>
                            <div class="stat-item">
                                <label>连接时长:</label>
                                <span id="connectionTime">00:00:00</span>
                            </div>
                            <div class="stat-item">
                                <label>传输速率:</label>
                                <span id="transferRate">0 B/s</span>
                            </div>
                            <div class="stat-item">
                                <label>错误率:</label>
                                <span id="errorRate">0%</span>
                            </div>
                        </div>

                        <div class="statistics-actions">
                            <button class="btn btn-outline btn-small" id="resetStatsBtn">
                                <i class="fas fa-redo"></i>
                                重置统计
                            </button>
                            <button class="btn btn-primary btn-small" id="exportStatsBtn">
                                <i class="fas fa-download"></i>
                                导出报告
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 右侧可视化区域 -->
                <div class="visualization-area">
                    <style>
                        /* 右侧可视化区域样式 - 与data页面完全一致 */
                        .visualization-area {
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(25px);
                            border-radius: 20px;
                            padding: 25px;
                            box-shadow:
                                0 12px 45px rgba(0, 0, 0, 0.06),
                                0 6px 25px rgba(59, 130, 246, 0.08),
                                inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            border: 1px solid rgba(255, 255, 255, 0.3);
                            overflow: visible;
                            display: flex;
                            flex-direction: column;
                            position: relative;
                            min-width: 1400px;
                            min-height: calc(100vh - 40px);
                            height: calc(100vh - 40px);
                        }

                        .visualization-area::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 1px;
                            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
                        }

                        .visualization-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 10px;
                            padding-bottom: 8px;
                            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
                            flex-shrink: 0;
                            height: 50px;
                            min-height: 50px;
                        }

                        .visualization-title {
                            font-size: 20px;
                            font-weight: 700;
                            color: #1e293b;
                            margin: 0;
                            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                        }

                        .visualization-actions {
                            display: flex;
                            gap: 12px;
                        }

                        .btn-small {
                            padding: 8px 16px;
                            font-size: 12px;
                            width: auto;
                        }

                        /* 图表网格样式 - 上下两张图布局 */
                        .charts-grid {
                            display: grid;
                            grid-template-columns: 1fr;
                            grid-template-rows: 1fr 1fr;
                            gap: 20px;
                            width: 100%;
                            min-width: 1400px;
                            padding: 5px 15px 30px 15px;
                            align-items: stretch;
                            justify-items: stretch;
                            min-height: calc(100vh - 180px);
                            height: calc(100vh - 180px);
                        }

                        .chart-card {
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 16px;
                            padding: 20px;
                            box-shadow:
                                0 8px 30px rgba(0, 0, 0, 0.06),
                                0 4px 15px rgba(59, 130, 246, 0.08),
                                inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            border: 2px solid rgba(255, 255, 255, 0.8);
                            transition: all 0.3s ease;
                            position: relative;
                            overflow: hidden;
                            display: flex;
                            flex-direction: column;
                            width: 100%;
                            height: 100%;
                            min-height: 420px;
                        }

                        .chart-card::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 1px;
                            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
                        }

                        .chart-card:hover {
                            transform: translateY(-8px) scale(1.02);
                            box-shadow:
                                0 20px 50px rgba(0, 0, 0, 0.08),
                                0 6px 20px rgba(59, 130, 246, 0.12),
                                inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            border-color: rgba(255, 255, 255, 0.6);
                        }

                        .chart-card:active {
                            transform: translateY(-3px) scale(0.99);
                            transition: all 0.1s ease;
                        }

                        .chart-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 6px;
                            padding-bottom: 4px;
                            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
                            flex-shrink: 0;
                            height: 40px;
                            min-height: 40px;
                        }

                        .chart-title {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            font-size: 14px;
                            font-weight: 600;
                            color: #1e293b;
                            margin: 0;
                        }

                        .chart-title i {
                            color: #3b82f6;
                            font-size: 16px;
                        }

                        .chart-actions {
                            display: flex;
                            gap: 6px;
                            flex-wrap: wrap;
                        }

                        .chart-btn {
                            width: 28px;
                            height: 28px;
                            border: none;
                            background: rgba(59, 130, 246, 0.1);
                            color: #3b82f6;
                            border-radius: 6px;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.3s ease;
                            font-size: 11px;
                        }

                        .chart-btn:hover {
                            background: rgba(59, 130, 246, 0.2);
                            transform: scale(1.1);
                        }

                        .operation-select {
                            padding: 8px 30px 8px 12px;
                            border: 2px solid transparent;
                            border-radius: 10px;
                            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
                            backdrop-filter: blur(15px);
                            color: #1e293b;
                            font-size: 12px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                            margin-right: 8px;
                            box-shadow:
                                0 3px 12px rgba(0, 0, 0, 0.08),
                                0 1px 6px rgba(59, 130, 246, 0.1),
                                inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            position: relative;
                            appearance: none;
                            -webkit-appearance: none;
                            -moz-appearance: none;
                            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
                            background-repeat: no-repeat;
                            background-position: right 8px center;
                            background-size: 14px;
                        }

                        .operation-select:hover {
                            border-color: rgba(59, 130, 246, 0.3);
                            box-shadow:
                                0 5px 20px rgba(0, 0, 0, 0.12),
                                0 3px 12px rgba(59, 130, 246, 0.15),
                                inset 0 1px 0 rgba(255, 255, 255, 0.9);
                            transform: translateY(-1px);
                            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232563eb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
                        }

                        .operation-select:focus {
                            outline: none;
                            border-color: #3b82f6;
                            box-shadow:
                                0 6px 25px rgba(0, 0, 0, 0.15),
                                0 3px 15px rgba(59, 130, 246, 0.25),
                                0 0 0 3px rgba(59, 130, 246, 0.1),
                                inset 0 1px 0 rgba(255, 255, 255, 1);
                            transform: translateY(-2px);
                            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%231d4ed8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
                        }

                        /* 选项样式 */
                        .operation-select option {
                            padding: 10px 12px;
                            background: rgba(255, 255, 255, 0.95);
                            color: #1e293b;
                            font-weight: 500;
                            border: none;
                        }

                        .operation-select option:hover {
                            background: rgba(59, 130, 246, 0.1);
                        }

                        .operation-select option:checked {
                            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
                            color: #1d4ed8;
                            font-weight: 600;
                        }

                        .chart-body {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(14, 165, 233, 0.08) 100%);
                            border-radius: 12px;
                            position: relative;
                            width: 100%;
                            min-height: 300px;
                            overflow: hidden;
                            padding: 0;
                            box-sizing: border-box;
                            margin: 4px;
                            border: 1px solid rgba(59, 130, 246, 0.15);
                        }

                        .chart-placeholder {
                            text-align: center;
                            color: #64748b;
                            padding: 40px 20px;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            z-index: 1;
                        }

                        .chart-placeholder i {
                            font-size: 48px;
                            color: #3b82f6;
                            margin-bottom: 15px;
                            display: block;
                        }

                        .chart-placeholder h4 {
                            font-size: 16px;
                            font-weight: 600;
                            color: #1e293b;
                            margin: 0 0 8px 0;
                        }

                        .chart-placeholder p {
                            font-size: 14px;
                            color: #64748b;
                            margin: 0 0 8px 0;
                        }

                        .chart-placeholder small {
                            font-size: 12px;
                            color: #94a3b8;
                            font-style: italic;
                        }

                        .chart-canvas {
                            width: 100% !important;
                            height: 100% !important;
                            max-width: 100% !important;
                            max-height: 100% !important;
                            object-fit: contain;
                            display: block;
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                        }

                        .chart-canvas {
                            width: 100% !important;
                            height: 100% !important;
                            max-width: calc(100% - 20px) !important;
                            max-height: calc(100% - 20px) !important;
                            object-fit: contain;
                            display: block;
                        }
                    </style>

                    <div class="visualization-header">
                        <h2 class="visualization-title">实时数据可视化</h2>
                        <div class="visualization-actions">
                            <button class="btn btn-outline btn-small" id="exportAllBtn">
                                <i class="fas fa-download"></i>
                                导出全部
                            </button>
                            <button class="btn btn-primary btn-small" id="refreshChartsBtn">
                                <i class="fas fa-sync-alt"></i>
                                刷新图表
                            </button>
                        </div>
                    </div>

                    <!-- 图表网格 -->
                    <div class="charts-grid">
                        <!-- 上方图表卡片 -->
                        <div class="chart-card" id="topChartCard">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-wave-square" id="topChartIcon"></i>
                                    <span id="topChartTitle">时域波形</span>
                                </h3>
                                <div class="chart-actions">
                                    <!-- 图表类型选择器 -->
                                    <select class="operation-select" id="topChartTypeSelect">
                                        <option value="time">时域波形</option>
                                        <option value="frequency">频域分析</option>
                                        <option value="envelope">包络线图</option>
                                        <option value="phase">瞬时相位</option>
                                        <option value="cdf">累积分布函数</option>
                                        <option value="psd">功率谱密度</option>
                                        <option value="vector">矢量图</option>
                                        <option value="polar">极坐标图</option>
                                        <option value="wavelet">小波变换图</option>
                                    </select>

                                    <!-- 操作按钮 -->
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="top" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="top" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="top" title="纵轴放大">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="top" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="reset" data-chart="top" title="重置">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-png" data-chart="top" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="top" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="top" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="top" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-placeholder" id="topChartPlaceholder">
                                    <i class="fas fa-wave-square"></i>
                                    <h4>等待串口数据</h4>
                                    <p>连接串口后将显示时域波形图</p>
                                    <small>支持实时数据更新和多种分析功能</small>
                                </div>
                                <canvas id="topChartCanvas" class="chart-canvas" style="display: none;"></canvas>
                            </div>
                        </div>

                        <!-- 下方图表卡片 -->
                        <div class="chart-card" id="bottomChartCard">
                            <div class="chart-header">
                                <h3 class="chart-title">
                                    <i class="fas fa-chart-area" id="bottomChartIcon"></i>
                                    <span id="bottomChartTitle">频域分析</span>
                                </h3>
                                <div class="chart-actions">
                                    <!-- 图表类型选择器 -->
                                    <select class="operation-select" id="bottomChartTypeSelect">
                                        <option value="frequency" selected>频域分析</option>
                                        <option value="time">时域波形</option>
                                        <option value="envelope">包络线图</option>
                                        <option value="phase">瞬时相位</option>
                                        <option value="cdf">累积分布函数</option>
                                        <option value="psd">功率谱密度</option>
                                        <option value="vector">矢量图</option>
                                        <option value="polar">极坐标图</option>
                                        <option value="wavelet">小波变换图</option>
                                    </select>

                                    <!-- 操作按钮 -->
                                    <button class="chart-btn" data-action="zoom-x-in" data-chart="bottom" title="横轴放大">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-x-out" data-chart="bottom" title="横轴缩小">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-in" data-chart="bottom" title="纵轴放大">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="zoom-y-out" data-chart="bottom" title="纵轴缩小">
                                        <i class="fas fa-compress-arrows-alt"></i>
                                    </button>
                                    <button class="chart-btn" data-action="reset" data-chart="bottom" title="重置">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-png" data-chart="bottom" title="导出PNG">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-jpg" data-chart="bottom" title="导出JPG">
                                        <i class="fas fa-file-image"></i>
                                    </button>
                                    <button class="chart-btn" data-action="export-excel" data-chart="bottom" title="导出Excel">
                                        <i class="fas fa-file-excel"></i>
                                    </button>
                                    <button class="chart-btn" data-action="fullscreen" data-chart="bottom" title="全屏">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-placeholder" id="bottomChartPlaceholder">
                                    <i class="fas fa-chart-area"></i>
                                    <h4>等待串口数据</h4>
                                    <p>连接串口后将显示频域分析图</p>
                                    <small>支持FFT分析和频谱可视化</small>
                                </div>
                                <canvas id="bottomChartCanvas" class="chart-canvas" style="display: none;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 固定水平滚动条 -->
    <div class="horizontal-scrollbar">
        <div class="horizontal-scrollbar-content"></div>
    </div>

    <script>
        // 检查浏览器是否支持Web Serial API
        if ("serial" in navigator) {
            console.log("浏览器支持 Web Serial API");
        } else {
            alert("您的浏览器不支持 Web Serial API，请使用Chrome、Edge或Opera浏览器");
        }

        // 全局变量
        let port = null;
        let reader = null;
        let writer = null;
        let reading = false;
        let connected = false;
        let receivedData = [];
        let topChart = null;
        let bottomChart = null;
        let currentTopChartType = 'time';
        let currentBottomChartType = 'frequency';
        let statistics = {
            receivedBytes: 0,
            sentBytes: 0,
            packets: 0,
            connectionStartTime: null,
            errors: 0
        };
        let sendInterval = null;

        // DOM元素
        const elements = {
            // 连接相关
            portSelect: document.getElementById('portSelect'),
            baudRateSelect: document.getElementById('baudRateSelect'),
            dataBitsSelect: document.getElementById('dataBitsSelect'),
            stopBitsSelect: document.getElementById('stopBitsSelect'),
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            connectionStatus: document.getElementById('connectionStatus'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),

            // 监视器相关
            monitorDisplay: document.getElementById('monitorDisplay'),
            monitorStatus: document.getElementById('monitorStatus'),
            clearMonitorBtn: document.getElementById('clearMonitorBtn'),
            pauseMonitorBtn: document.getElementById('pauseMonitorBtn'),

            // 发送相关
            sendDataInput: document.getElementById('sendDataInput'),
            sendFormatSelect: document.getElementById('sendFormatSelect'),
            sendIntervalInput: document.getElementById('sendIntervalInput'),
            sendOnceBtn: document.getElementById('sendOnceBtn'),
            sendRepeatBtn: document.getElementById('sendRepeatBtn'),

            // 协议相关
            protocolSelect: document.getElementById('protocolSelect'),
            protocolConfig: document.getElementById('protocolConfig'),
            frameHeaderInput: document.getElementById('frameHeaderInput'),
            frameFooterInput: document.getElementById('frameFooterInput'),
            lengthPositionInput: document.getElementById('lengthPositionInput'),
            parsedFrameCount: document.getElementById('parsedFrameCount'),
            errorFrameCount: document.getElementById('errorFrameCount'),

            // 统计相关
            receivedBytesCount: document.getElementById('receivedBytesCount'),
            sentBytesCount: document.getElementById('sentBytesCount'),
            packetCount: document.getElementById('packetCount'),
            connectionTime: document.getElementById('connectionTime'),
            transferRate: document.getElementById('transferRate'),
            errorRate: document.getElementById('errorRate'),
            resetStatsBtn: document.getElementById('resetStatsBtn'),
            exportStatsBtn: document.getElementById('exportStatsBtn'),

            // 图表相关
            topChartPlaceholder: document.getElementById('topChartPlaceholder'),
            bottomChartPlaceholder: document.getElementById('bottomChartPlaceholder'),
            topChartCanvas: document.getElementById('topChartCanvas'),
            bottomChartCanvas: document.getElementById('bottomChartCanvas'),
            topChartTitle: document.getElementById('topChartTitle'),
            bottomChartTitle: document.getElementById('bottomChartTitle'),
            topChartIcon: document.getElementById('topChartIcon'),
            bottomChartIcon: document.getElementById('bottomChartIcon'),
            topChartTypeSelect: document.getElementById('topChartTypeSelect'),
            bottomChartTypeSelect: document.getElementById('bottomChartTypeSelect'),
            exportAllBtn: document.getElementById('exportAllBtn'),
            refreshChartsBtn: document.getElementById('refreshChartsBtn')
        };

        // 串口设备插入与拔出事件
        navigator.serial.onconnect = (event) => {
            console.log("串口设备已连接: ", event.target);
            updatePortList();
        };

        navigator.serial.ondisconnect = (event) => {
            console.log("串口设备已断开: ", event.target);
            updatePortList();
            if (connected && event.target === port) {
                handleDisconnection();
            }
        };

        // 更新串口列表
        async function updatePortList() {
            try {
                const ports = await navigator.serial.getPorts();
                elements.portSelect.innerHTML = '<option value="">请选择串口</option>';

                ports.forEach((port, index) => {
                    const info = port.getInfo();
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = `串口 ${index + 1} (VID: ${info.usbVendorId || 'N/A'}, PID: ${info.usbProductId || 'N/A'})`;
                    elements.portSelect.appendChild(option);
                });
            } catch (error) {
                console.error('获取串口列表失败:', error);
            }
        }

        // 选择串口
        async function selectPort() {
            try {
                port = await navigator.serial.requestPort();
                updatePortList();

                // 自动选择刚刚授权的串口
                const ports = await navigator.serial.getPorts();
                const portIndex = ports.indexOf(port);
                if (portIndex !== -1) {
                    elements.portSelect.value = portIndex;
                }
            } catch (error) {
                console.log('用户取消了串口选择');
            }
        }

        // 连接串口 - 集成后端API
        async function connectSerial() {
            const selectedPortName = elements.portSelect.value;
            if (!selectedPortName) {
                alert('请先选择串口');
                return;
            }

            try {
                const baudRate = parseInt(elements.baudRateSelect.value);
                const dataBits = parseInt(elements.dataBitsSelect.value);
                const stopBits = parseFloat(elements.stopBitsSelect.value);
                const parity = elements.paritySelect?.value || 'none';

                const config = {
                    port: selectedPortName,
                    baudRate: baudRate,
                    dataBits: dataBits,
                    stopBits: stopBits,
                    parity: parity,
                    flowControl: 'none'
                };

                let useLocalSerial = false;
                let sessionId = null;

                // 尝试使用后端API连接
                if (window.SerialAPI) {
                    try {
                        showNotification('正在连接串口...', 'info');

                        const apiResult = await window.SerialAPI.connectPort(config);

                        if (apiResult.success) {
                            sessionId = apiResult.data.sessionId;

                            // WebSocket功能已禁用，使用轮询方式获取数据
                            console.warn('WebSocket功能已禁用，串口实时数据传输不可用');
                            showNotification('实时数据传输功能暂时不可用', 'warning');

                            connected = true;
                            window.currentSessionId = sessionId;
                            showNotification('串口连接成功', 'success');
                        }
                    } catch (apiError) {
                        console.warn('后端API连接失败，尝试本地连接:', apiError);
                        useLocalSerial = true;
                    }
                }

                // 如果API失败，使用本地Web Serial API作为备用方案
                if (!connected || useLocalSerial) {
                    if (!navigator.serial) {
                        throw new Error('浏览器不支持Web Serial API');
                    }

                    if (!port) {
                        const selectedIndex = parseInt(selectedPortName);
                        const ports = await navigator.serial.getPorts();
                        port = ports[selectedIndex];

                        if (!port) {
                            throw new Error('未找到指定的串口设备');
                        }
                    }

                    await port.open({
                        baudRate: baudRate,
                        dataBits: dataBits,
                        stopBits: stopBits,
                        parity: parity,
                        flowControl: 'none'
                    });

                    connected = true;
                    startReading();
                    showNotification('使用本地串口连接成功', 'success');
                }

                statistics.connectionStartTime = Date.now();
                updateConnectionStatus(true);

                // 保存当前配置
                saveCurrentConfig();

                console.log('串口连接成功, 会话ID:', sessionId);
            } catch (error) {
                console.error('串口连接失败:', error);
                showNotification('串口连接失败: ' + error.message, 'error');
            }
        }

        // 断开串口 - 集成后端API
        async function disconnectSerial() {
            if (!connected) return;

            try {
                // 如果使用后端API，先断开API连接
                if (window.SerialAPI && window.currentSessionId) {
                    try {
                        await window.SerialAPI.disconnectPort(window.currentSessionId);
                        // WebSocket功能已禁用
                        window.currentSessionId = null;
                        showNotification('后端串口连接已断开', 'success');
                    } catch (apiError) {
                        console.warn('后端API断开失败:', apiError);
                    }
                }

                // 如果使用本地串口，断开本地连接
                if (port) {
                    reading = false;
                    if (reader) {
                        await reader.cancel();
                    }
                    if (writer) {
                        writer.releaseLock();
                    }
                    await port.close();
                    showNotification('本地串口连接已断开', 'success');
                }

                handleDisconnection();
                console.log('串口已断开');
            } catch (error) {
                console.error('断开串口失败:', error);
                showNotification('断开串口失败: ' + error.message, 'error');
            }
        }

        // 处理断开连接
        function handleDisconnection() {
            connected = false;
            reading = false;
            port = null;
            reader = null;
            writer = null;
            statistics.connectionStartTime = null; // 重置连接时长
            updateConnectionStatus(false);
            stopSendInterval();
        }

        // 更新连接状态
        function updateConnectionStatus(isConnected) {
            if (isConnected) {
                elements.connectionStatus.className = 'connection-status connected';
                elements.statusIndicator.className = 'status-indicator connected';
                elements.statusText.textContent = '已连接';
                elements.connectBtn.disabled = true;
                elements.disconnectBtn.disabled = false;
                elements.sendOnceBtn.disabled = false;
                elements.sendRepeatBtn.disabled = false;
            } else {
                elements.connectionStatus.className = 'connection-status disconnected';
                elements.statusIndicator.className = 'status-indicator disconnected';
                elements.statusText.textContent = '未连接';
                elements.connectBtn.disabled = false;
                elements.disconnectBtn.disabled = true;
                elements.sendOnceBtn.disabled = true;
                elements.sendRepeatBtn.disabled = true;
            }
        }

        // 开始读取数据
        async function startReading() {
            reading = true;

            while (port.readable && reading) {
                reader = port.readable.getReader();
                try {
                    while (reading) {
                        const { value, done } = await reader.read();
                        if (done) break;

                        handleReceivedData(value);
                    }
                } catch (error) {
                    console.error('读取数据错误:', error);
                    statistics.errors++;
                } finally {
                    reader.releaseLock();
                }
            }
        }

        // 处理接收到的数据
        function handleReceivedData(data) {
            statistics.receivedBytes += data.length;
            statistics.packets++;

            // 转换为十六进制字符串显示
            const hexString = Array.from(data)
                .map(byte => byte.toString(16).toUpperCase().padStart(2, '0'))
                .join(' ');

            // 显示在监视器中
            displayInMonitor(hexString, 'received');

            // 解析数据为数值（假设是浮点数据）
            const values = parseDataToNumbers(data);
            if (values.length > 0) {
                receivedData.push(...values);
                // 限制数据长度，保持最新的1000个点
                if (receivedData.length > 1000) {
                    receivedData = receivedData.slice(-1000);
                }
                updateCharts();
            }

            updateStatistics();
        }

        // 将字节数据解析为数值
        function parseDataToNumbers(data) {
            const values = [];

            // 假设数据是以4字节浮点数传输
            for (let i = 0; i < data.length - 3; i += 4) {
                const bytes = new Uint8Array([data[i], data[i+1], data[i+2], data[i+3]]);
                const view = new DataView(bytes.buffer);
                try {
                    const value = view.getFloat32(0, true); // 小端序
                    if (!isNaN(value) && isFinite(value)) {
                        values.push(value);
                    }
                } catch (e) {
                    // 如果不是浮点数，尝试解析为整数
                    try {
                        const value = view.getInt32(0, true);
                        values.push(value);
                    } catch (e2) {
                        // 最后尝试直接使用字节值
                        values.push(data[i]);
                    }
                }
            }

            return values;
        }

        // 在监视器中显示数据
        function displayInMonitor(data, type = 'received') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = `monitor-line ${type}`;
            line.innerHTML = `<span style="color: #64748b;">[${timestamp}]</span> ${type === 'received' ? 'RX:' : 'TX:'} ${data}`;

            // 移除占位符
            const placeholder = elements.monitorDisplay.querySelector('.monitor-placeholder');
            if (placeholder) {
                placeholder.remove();
            }

            elements.monitorDisplay.appendChild(line);
            elements.monitorDisplay.scrollTop = elements.monitorDisplay.scrollHeight;

            // 限制显示行数
            const lines = elements.monitorDisplay.querySelectorAll('.monitor-line');
            if (lines.length > 100) {
                lines[0].remove();
            }
        }

        // 发送数据
        async function sendData(data, format = 'hex') {
            if (!connected || !port) {
                alert('请先连接串口');
                return;
            }

            try {
                let bytes;

                switch (format) {
                    case 'hex':
                        bytes = hexStringToBytes(data);
                        break;
                    case 'ascii':
                        bytes = new TextEncoder().encode(data);
                        break;
                    case 'decimal':
                        bytes = decimalStringToBytes(data);
                        break;
                    default:
                        bytes = hexStringToBytes(data);
                }

                if (!writer) {
                    writer = port.writable.getWriter();
                }

                await writer.write(bytes);

                statistics.sentBytes += bytes.length;

                // 显示在监视器中
                const hexString = Array.from(bytes)
                    .map(byte => byte.toString(16).toUpperCase().padStart(2, '0'))
                    .join(' ');
                displayInMonitor(hexString, 'sent');

                updateStatistics();

            } catch (error) {
                console.error('发送数据失败:', error);
                alert('发送数据失败: ' + error.message);
                statistics.errors++;
            }
        }

        // 十六进制字符串转字节数组
        function hexStringToBytes(hexString) {
            const cleanHex = hexString.replace(/\s+/g, '');
            if (cleanHex.length % 2 !== 0 || !/^[0-9a-fA-F]+$/.test(cleanHex)) {
                throw new Error('无效的十六进制格式');
            }

            const bytes = new Uint8Array(cleanHex.length / 2);
            for (let i = 0; i < cleanHex.length; i += 2) {
                bytes[i / 2] = parseInt(cleanHex.substr(i, 2), 16);
            }
            return bytes;
        }

        // 十进制字符串转字节数组
        function decimalStringToBytes(decString) {
            const numbers = decString.split(/\s+/).map(s => parseInt(s.trim()));
            return new Uint8Array(numbers.filter(n => !isNaN(n) && n >= 0 && n <= 255));
        }

        // 更新统计信息
        function updateStatistics() {
            elements.receivedBytesCount.textContent = statistics.receivedBytes.toLocaleString();
            elements.sentBytesCount.textContent = statistics.sentBytes.toLocaleString();
            elements.packetCount.textContent = statistics.packets.toLocaleString();

            if (statistics.connectionStartTime && connected) {
                const duration = Date.now() - statistics.connectionStartTime;
                const hours = Math.floor(duration / 3600000);
                const minutes = Math.floor((duration % 3600000) / 60000);
                const seconds = Math.floor((duration % 60000) / 1000);
                elements.connectionTime.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                const rate = (statistics.receivedBytes + statistics.sentBytes) / (duration / 1000);
                elements.transferRate.textContent = `${rate.toFixed(1)} B/s`;
            } else {
                elements.connectionTime.textContent = '00:00:00';
                elements.transferRate.textContent = '0.0 B/s';
            }

            const errorRate = statistics.packets > 0 ? (statistics.errors / statistics.packets * 100) : 0;
            elements.errorRate.textContent = `${errorRate.toFixed(2)}%`;
        }

        // 停止发送间隔
        function stopSendInterval() {
            if (sendInterval) {
                clearInterval(sendInterval);
                sendInterval = null;
                elements.sendRepeatBtn.innerHTML = '<i class="fas fa-repeat"></i> 循环发送';
            }
        }

        // 开始读取数据
        async function startReading() {
            reading = true;

            while (port.readable && reading) {
                reader = port.readable.getReader();
                try {
                    while (reading) {
                        const { value, done } = await reader.read();
                        if (done) break;

                        handleReceivedData(value);
                    }
                } catch (error) {
                    console.error('读取数据错误:', error);
                    statistics.errors++;
                } finally {
                    reader.releaseLock();
                }
            }
        }

        // 处理接收到的数据
        function handleReceivedData(data) {
            statistics.receivedBytes += data.length;
            statistics.packets++;

            // 转换为十六进制字符串显示
            const hexString = Array.from(data)
                .map(byte => byte.toString(16).toUpperCase().padStart(2, '0'))
                .join(' ');

            // 显示在监视器中
            displayInMonitor(hexString, 'received');

            // 解析数据为数值（假设是浮点数据）
            const values = parseDataToNumbers(data);
            if (values.length > 0) {
                receivedData.push(...values);
                // 限制数据长度，保持最新的1000个点
                if (receivedData.length > 1000) {
                    receivedData = receivedData.slice(-1000);
                }
                updateCharts();
            }

            updateStatistics();
        }

        // 将字节数据解析为数值
        function parseDataToNumbers(data) {
            const values = [];

            // 假设数据是以4字节浮点数传输
            for (let i = 0; i < data.length - 3; i += 4) {
                const bytes = new Uint8Array([data[i], data[i+1], data[i+2], data[i+3]]);
                const view = new DataView(bytes.buffer);
                try {
                    const value = view.getFloat32(0, true); // 小端序
                    if (!isNaN(value) && isFinite(value)) {
                        values.push(value);
                    }
                } catch (e) {
                    // 如果不是浮点数，尝试解析为整数
                    try {
                        const value = view.getInt32(0, true);
                        values.push(value);
                    } catch (e2) {
                        // 最后尝试直接使用字节值
                        values.push(data[i]);
                    }
                }
            }

            return values;
        }

        // 在监视器中显示数据
        function displayInMonitor(data, type = 'received') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = `monitor-line ${type}`;
            line.innerHTML = `<span style="color: #64748b;">[${timestamp}]</span> ${type === 'received' ? 'RX:' : 'TX:'} ${data}`;

            // 移除占位符
            const placeholder = elements.monitorDisplay.querySelector('.monitor-placeholder');
            if (placeholder) {
                placeholder.remove();
            }

            elements.monitorDisplay.appendChild(line);
            elements.monitorDisplay.scrollTop = elements.monitorDisplay.scrollHeight;

            // 限制显示行数
            const lines = elements.monitorDisplay.querySelectorAll('.monitor-line');
            if (lines.length > 100) {
                lines[0].remove();
            }
        }

        // 发送数据 - 集成后端API
        async function sendData(data, format = 'hex') {
            if (!connected) {
                alert('请先连接串口');
                return;
            }

            try {
                let bytes;
                let sendSuccess = false;

                // 数据格式转换
                switch (format) {
                    case 'hex':
                        bytes = hexStringToBytes(data);
                        break;
                    case 'ascii':
                        bytes = new TextEncoder().encode(data);
                        break;
                    case 'decimal':
                        bytes = decimalStringToBytes(data);
                        break;
                    default:
                        bytes = hexStringToBytes(data);
                }

                // 尝试使用后端API发送
                if (window.SerialAPI && window.currentSessionId) {
                    try {
                        const sendOptions = {
                            format: format,
                            encoding: 'utf8',
                            addNewline: false
                        };

                        let sendData;
                        if (format === 'hex') {
                            sendData = Array.from(bytes)
                                .map(byte => byte.toString(16).toUpperCase().padStart(2, '0'))
                                .join(' ');
                        } else if (format === 'ascii') {
                            sendData = data;
                        } else if (format === 'decimal') {
                            sendData = Array.from(bytes).join(' ');
                        }

                        const apiResult = await window.SerialAPI.sendData(
                            window.currentSessionId,
                            sendData,
                            sendOptions
                        );

                        if (apiResult.success) {
                            sendSuccess = true;
                            console.log('后端API发送成功');
                        }
                    } catch (apiError) {
                        console.warn('后端API发送失败，使用本地发送:', apiError);
                    }
                }

                // 如果API失败，使用本地串口发送
                if (!sendSuccess && port) {
                    if (!writer) {
                        writer = port.writable.getWriter();
                    }

                    await writer.write(bytes);
                    sendSuccess = true;
                    console.log('本地串口发送成功');
                }

                if (sendSuccess) {
                    statistics.sentBytes += bytes.length;

                    // 显示在监视器中
                    const hexString = Array.from(bytes)
                        .map(byte => byte.toString(16).toUpperCase().padStart(2, '0'))
                        .join(' ');
                    displayInMonitor(hexString, 'sent');

                    updateStatistics();
                } else {
                    throw new Error('所有发送方式都失败');
                }

            } catch (error) {
                console.error('发送数据失败:', error);
                showNotification('发送数据失败: ' + error.message, 'error');
                statistics.errors++;
                updateStatistics();
            }
        }

        // 十六进制字符串转字节数组
        function hexStringToBytes(hexString) {
            const cleanHex = hexString.replace(/\s+/g, '');
            if (cleanHex.length % 2 !== 0 || !/^[0-9a-fA-F]+$/.test(cleanHex)) {
                throw new Error('无效的十六进制格式');
            }

            const bytes = new Uint8Array(cleanHex.length / 2);
            for (let i = 0; i < cleanHex.length; i += 2) {
                bytes[i / 2] = parseInt(cleanHex.substr(i, 2), 16);
            }
            return bytes;
        }

        // 十进制字符串转字节数组
        function decimalStringToBytes(decString) {
            const numbers = decString.split(/\s+/).map(s => parseInt(s.trim()));
            return new Uint8Array(numbers.filter(n => !isNaN(n) && n >= 0 && n <= 255));
        }



        // 停止发送间隔
        function stopSendInterval() {
            if (sendInterval) {
                clearInterval(sendInterval);
                sendInterval = null;
                elements.sendRepeatBtn.innerHTML = '<i class="fas fa-repeat"></i> 循环发送';
            }
        }

        // 初始化图表
        function initCharts() {
            // 初始化上方图表
            const topCtx = elements.topChartCanvas.getContext('2d');
            topChart = new Chart(topCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '时域波形',
                        data: [],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: { duration: 0 },
                    layout: {
                        padding: {
                            top: 10,
                            right: 10,
                            bottom: 10,
                            left: 10
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '时间 (秒)',
                                font: { size: 12, weight: 'bold' }
                            },
                            type: 'linear',
                            ticks: {
                                maxTicksLimit: 10,
                                callback: function(value) {
                                    return value.toFixed(2) + 's';
                                }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '幅值',
                                font: { size: 12, weight: 'bold' }
                            },
                            ticks: {
                                maxTicksLimit: 8,
                                callback: function(value) {
                                    return value.toFixed(1);
                                }
                            }
                        }
                    },
                    plugins: {
                        zoom: {
                            zoom: { wheel: { enabled: true }, pinch: { enabled: true }, mode: 'xy' },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });

            // 初始化下方图表
            const bottomCtx = elements.bottomChartCanvas.getContext('2d');
            bottomChart = new Chart(bottomCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '频域分析',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: { duration: 0 },
                    layout: {
                        padding: {
                            top: 10,
                            right: 10,
                            bottom: 10,
                            left: 10
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '频率 (Hz)',
                                font: { size: 12, weight: 'bold' }
                            },
                            type: 'linear',
                            ticks: {
                                maxTicksLimit: 10,
                                callback: function(value) {
                                    return value + ' Hz';
                                }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '幅值',
                                font: { size: 12, weight: 'bold' }
                            },
                            ticks: {
                                maxTicksLimit: 8,
                                callback: function(value) {
                                    return value.toFixed(2);
                                }
                            }
                        }
                    },
                    plugins: {
                        zoom: {
                            zoom: { wheel: { enabled: true }, pinch: { enabled: true }, mode: 'xy' },
                            pan: { enabled: true, mode: 'xy' }
                        }
                    }
                }
            });

            // 监听窗口大小变化，调整图表大小
            window.addEventListener('resize', () => {
                setTimeout(() => {
                    if (topChart) {
                        topChart.resize();
                    }
                    if (bottomChart) {
                        bottomChart.resize();
                    }
                }, 100);
            });

            // 使用ResizeObserver监听图表容器大小变化
            if (window.ResizeObserver) {
                const resizeObserver = new ResizeObserver(entries => {
                    for (let entry of entries) {
                        setTimeout(() => {
                            if (entry.target.id === 'topChartCanvas' && topChart) {
                                topChart.resize();
                            } else if (entry.target.id === 'bottomChartCanvas' && bottomChart) {
                                bottomChart.resize();
                            }
                        }, 50);
                    }
                });

                if (elements.topChartCanvas) {
                    resizeObserver.observe(elements.topChartCanvas);
                }
                if (elements.bottomChartCanvas) {
                    resizeObserver.observe(elements.bottomChartCanvas);
                }
            }
        }

        // 更新图表
        function updateCharts() {
            if (receivedData.length === 0) return;

            // 隐藏占位符，显示图表
            if (elements.topChartPlaceholder && elements.bottomChartPlaceholder) {
                elements.topChartPlaceholder.style.display = 'none';
                elements.bottomChartPlaceholder.style.display = 'none';
                elements.topChartCanvas.style.display = 'block';
                elements.bottomChartCanvas.style.display = 'block';
            }

            // 更新上方图表
            updateTopChart();

            // 更新下方图表
            updateBottomChart();
        }

        // 更新上方图表
        function updateTopChart() {
            if (!topChart) {
                console.log('上方图表不存在，跳过更新');
                return;
            }

            console.log('更新上方图表，类型:', currentTopChartType, '数据长度:', receivedData.length);

            switch (currentTopChartType) {
                case 'time':
                    updateTimeChart(topChart, receivedData);
                    break;
                case 'frequency':
                    updateFrequencyChart(topChart, receivedData);
                    break;
                case 'envelope':
                    updateEnvelopeChart(topChart, receivedData);
                    break;
                case 'phase':
                    updatePhaseChart(topChart, receivedData);
                    break;
                case 'cdf':
                    updateCDFChart(topChart, receivedData);
                    break;
                case 'psd':
                    updatePSDChart(topChart, receivedData);
                    break;
                case 'vector':
                    updateVectorChart(topChart, receivedData);
                    break;
                case 'polar':
                    updatePolarChart(topChart, receivedData);
                    break;
                case 'wavelet':
                    updateWaveletChart(topChart, receivedData);
                    break;
            }
        }

        // 更新下方图表
        function updateBottomChart() {
            if (!bottomChart) {
                console.log('下方图表不存在，跳过更新');
                return;
            }

            console.log('更新下方图表，类型:', currentBottomChartType, '数据长度:', receivedData.length);

            switch (currentBottomChartType) {
                case 'time':
                    updateTimeChart(bottomChart, receivedData);
                    break;
                case 'frequency':
                    updateFrequencyChart(bottomChart, receivedData);
                    break;
                case 'envelope':
                    updateEnvelopeChart(bottomChart, receivedData);
                    break;
                case 'phase':
                    updatePhaseChart(bottomChart, receivedData);
                    break;
                case 'cdf':
                    updateCDFChart(bottomChart, receivedData);
                    break;
                case 'psd':
                    updatePSDChart(bottomChart, receivedData);
                    break;
                case 'vector':
                    updateVectorChart(bottomChart, receivedData);
                    break;
                case 'polar':
                    updatePolarChart(bottomChart, receivedData);
                    break;
                case 'wavelet':
                    updateWaveletChart(bottomChart, receivedData);
                    break;
            }
        }

        // 更新时域图表
        function updateTimeChart(chart, data) {
            const sampleRate = 100; // Hz
            const timeData = data.map((value, index) => ({
                x: (index / sampleRate).toFixed(3), // 时间轴，保留3位小数
                y: value
            }));

            chart.data.datasets[0].data = timeData;
            chart.data.datasets[0].label = '时域波形';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = '时间 (秒)';
            chart.options.scales.y.title.text = '幅值';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value.toFixed(2) + 's';
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat(value.toFixed(3));
                }
            };

            chart.update('none');
        }

        // 更新频域图表
        function updateFrequencyChart(chart, data) {
            const fftData = performSimpleFFT(data);
            const sampleRate = 1000; // Hz
            const freqData = fftData.map((value, index) => ({
                x: (index * sampleRate / fftData.length).toFixed(1),
                y: value
            }));

            chart.data.datasets[0].data = freqData;
            chart.data.datasets[0].label = '频域分析';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = '频率 (Hz)';
            chart.options.scales.y.title.text = '幅值';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value + ' Hz';
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat(value.toFixed(3));
                }
            };

            chart.update('none');
        }

        // 简单FFT近似
        function performSimpleFFT(data) {
            const N = Math.min(data.length, 256);
            const result = new Array(N / 2);

            for (let k = 0; k < N / 2; k++) {
                let real = 0, imag = 0;
                for (let n = 0; n < N; n++) {
                    const angle = -2 * Math.PI * k * n / N;
                    real += data[n] * Math.cos(angle);
                    imag += data[n] * Math.sin(angle);
                }
                result[k] = Math.sqrt(real * real + imag * imag);
            }

            return result;
        }

        // 更新包络图表
        function updateEnvelopeChart(chart, data) {
            const envelopeData = calculateEnvelope(data);
            const sampleRate = 100; // Hz
            const timeData = envelopeData.map((value, index) => ({
                x: (index / sampleRate).toFixed(3),
                y: value
            }));

            chart.data.datasets[0].data = timeData;
            chart.data.datasets[0].label = '包络线图';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = '时间 (秒)';
            chart.options.scales.y.title.text = '包络值';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value.toFixed(2) + 's';
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat(value.toFixed(3));
                }
            };

            chart.update('none');
        }

        // 更新瞬时相位图表
        function updatePhaseChart(chart, data) {
            const phaseData = calculateInstantaneousPhase(data);
            const sampleRate = 100; // Hz
            const timeData = phaseData.map((value, index) => ({
                x: (index / sampleRate).toFixed(3),
                y: value
            }));

            chart.data.datasets[0].data = timeData;
            chart.data.datasets[0].label = '瞬时相位';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = '时间 (秒)';
            chart.options.scales.y.title.text = '相位 (弧度)';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value.toFixed(2) + 's';
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat(value.toFixed(3)) + 'π';
                }
            };

            chart.update('none');
        }

        // 更新累积分布函数图表
        function updateCDFChart(chart, data) {
            const cdfData = calculateCDF(data);

            chart.data.datasets[0].data = cdfData;
            chart.data.datasets[0].label = '累积分布函数';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = '数值';
            chart.options.scales.y.title.text = '累积概率';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value.toFixed(1);
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat((value * 100).toFixed(3)) + '%';
                }
            };

            chart.update('none');
        }

        // 更新功率谱密度图表
        function updatePSDChart(chart, data) {
            const psdData = calculatePSD(data);

            chart.data.datasets[0].data = psdData;
            chart.data.datasets[0].label = '功率谱密度';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = '频率 (Hz)';
            chart.options.scales.y.title.text = '功率密度 (dB/Hz)';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value + ' Hz';
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat(value.toFixed(3)) + ' dB';
                }
            };

            chart.update('none');
        }

        // 计算包络
        function calculateEnvelope(data) {
            const windowSize = Math.max(5, Math.floor(data.length / 100));
            const envelope = [];

            for (let i = 0; i < data.length; i++) {
                let max = -Infinity;
                const start = Math.max(0, i - windowSize / 2);
                const end = Math.min(data.length, i + windowSize / 2);

                for (let j = start; j < end; j++) {
                    max = Math.max(max, Math.abs(data[j]));
                }
                envelope.push(max);
            }

            return envelope;
        }

        // 计算瞬时相位
        function calculateInstantaneousPhase(data) {
            const phaseData = [];

            for (let i = 1; i < data.length; i++) {
                const dy = data[i] - data[i-1];
                const dx = 1; // 假设采样间隔为1
                const phase = Math.atan2(dy, dx);
                phaseData.push(phase);
            }

            return phaseData;
        }

        // 计算累积分布函数
        function calculateCDF(data) {
            const sortedData = [...data].sort((a, b) => a - b);
            const cdfData = [];

            sortedData.forEach((value, index) => {
                cdfData.push({
                    x: value,
                    y: (index + 1) / sortedData.length
                });
            });

            return cdfData;
        }

        // 计算功率谱密度
        function calculatePSD(data) {
            const fftData = performSimpleFFT(data);
            const psdData = fftData.map((value, index) => ({
                x: index * 1000 / fftData.length,
                y: value * value / data.length // 简化的PSD计算
            }));

            return psdData;
        }

        // 更新矢量图表
        function updateVectorChart(chart, data) {
            const vectorData = calculateVectorData(data);

            chart.data.datasets[0].data = vectorData;
            chart.data.datasets[0].label = '矢量图';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = 'X 分量';
            chart.options.scales.y.title.text = 'Y 分量';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value.toFixed(1);
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat(value.toFixed(3));
                }
            };

            chart.update('none');
        }

        // 更新极坐标图表
        function updatePolarChart(chart, data) {
            if (!chart || chart.config.type !== 'polarArea') return chart;

            const polarData = calculatePolarData(data);
            console.log('极坐标数据:', polarData); // 调试信息

            chart.data.labels = polarData.labels;
            chart.data.datasets[0].data = polarData.values;
            chart.data.datasets[0].backgroundColor = polarData.colors;
            chart.data.datasets[0].borderColor = polarData.colors.map(color => color.replace('0.6', '1'));
            chart.update('none');

            return chart;
        }

        // 更新小波变换图表
        function updateWaveletChart(chart, data) {
            const waveletData = calculateWaveletData(data);

            chart.data.datasets[0].data = waveletData;
            chart.data.datasets[0].label = '小波变换';

            // 更新坐标轴配置
            chart.options.scales.x.title.text = '时间 (秒)';
            chart.options.scales.y.title.text = '小波系数';
            chart.options.scales.x.type = 'linear';
            chart.options.scales.x.ticks = {
                maxTicksLimit: 10,
                callback: function(value) {
                    return value.toFixed(2) + 's';
                }
            };
            chart.options.scales.y.ticks = {
                maxTicksLimit: 8,
                callback: function(value) {
                    return parseFloat(value.toFixed(3));
                }
            };

            chart.update('none');
        }

        // 计算矢量数据
        function calculateVectorData(data) {
            const vectorData = [];
            const step = Math.max(1, Math.floor(data.length / 100)); // 限制点数

            for (let i = 0; i < data.length - 1; i += step) {
                const x = data[i];
                const y = data[i + 1] || 0;
                vectorData.push({ x: x, y: y });
            }

            return vectorData;
        }

        // 计算极坐标数据
        function calculatePolarData(data) {
            const bins = 16; // 极坐标分段数
            const values = new Array(bins).fill(0);
            const labels = [];
            const colors = [];

            // 生成标签和颜色
            for (let i = 0; i < bins; i++) {
                const angle = (i * 360 / bins).toFixed(0);
                labels.push(`${angle}°`);
                const hue = i * 360 / bins;
                colors.push(`hsla(${hue}, 70%, 60%, 0.6)`);
            }

            // 将数据映射到极坐标
            data.forEach(value => {
                const normalized = (value + 1) / 2; // 归一化到[0,1]
                const binIndex = Math.floor(normalized * bins) % bins;
                values[binIndex] += Math.abs(value);
            });

            return { labels, values, colors };
        }

        // 计算小波变换数据（简化版）
        function calculateWaveletData(data) {
            const waveletData = [];
            const windowSize = 16; // 小波窗口大小

            for (let i = 0; i < data.length - windowSize; i++) {
                let sum = 0;
                for (let j = 0; j < windowSize; j++) {
                    // 简化的Haar小波变换
                    const weight = j < windowSize / 2 ? 1 : -1;
                    sum += data[i + j] * weight;
                }

                waveletData.push({
                    x: i * 0.01,
                    y: sum / windowSize
                });
            }

            return waveletData;
        }

        // 切换上方图表类型
        function switchTopChartType(type) {
            console.log('切换上方图表类型到:', type);
            currentTopChartType = type;

            // 更新图表标题和图标
            const titles = {
                time: '时域波形',
                frequency: '频域分析',
                envelope: '包络线图',
                phase: '瞬时相位',
                cdf: '累积分布函数',
                psd: '功率谱密度',
                vector: '矢量图',
                polar: '极坐标图',
                wavelet: '小波变换图'
            };

            const icons = {
                time: 'fas fa-wave-square',
                frequency: 'fas fa-chart-area',
                envelope: 'fas fa-mountain',
                phase: 'fas fa-sync-alt',
                cdf: 'fas fa-chart-line',
                psd: 'fas fa-chart-bar',
                vector: 'fas fa-arrows-alt',
                polar: 'fas fa-circle-notch',
                wavelet: 'fas fa-water'
            };

            if (elements.topChartTitle) {
                elements.topChartTitle.textContent = titles[type];
            }
            if (elements.topChartIcon) {
                elements.topChartIcon.className = icons[type];
            }

            // 重新创建图表
            recreateTopChart();
        }

        // 切换下方图表类型
        function switchBottomChartType(type) {
            console.log('切换下方图表类型到:', type);
            currentBottomChartType = type;

            // 更新图表标题和图标
            const titles = {
                time: '时域波形',
                frequency: '频域分析',
                envelope: '包络线图',
                phase: '瞬时相位',
                cdf: '累积分布函数',
                psd: '功率谱密度',
                vector: '矢量图',
                polar: '极坐标图',
                wavelet: '小波变换图'
            };

            const icons = {
                time: 'fas fa-wave-square',
                frequency: 'fas fa-chart-area',
                envelope: 'fas fa-mountain',
                phase: 'fas fa-sync-alt',
                cdf: 'fas fa-chart-line',
                psd: 'fas fa-chart-bar',
                vector: 'fas fa-arrows-alt',
                polar: 'fas fa-circle-notch',
                wavelet: 'fas fa-water'
            };

            if (elements.bottomChartTitle) {
                elements.bottomChartTitle.textContent = titles[type];
            }
            if (elements.bottomChartIcon) {
                elements.bottomChartIcon.className = icons[type];
            }

            // 重新创建图表
            recreateBottomChart();
        }

        // 重新创建上方图表
        function recreateTopChart() {
            console.log('开始重新创建上方图表，类型:', currentTopChartType);

            // 销毁现有图表
            if (topChart) {
                console.log('销毁现有上方图表');
                topChart.destroy();
                topChart = null;
            }

            const ctx = elements.topChartCanvas.getContext('2d');
            if (!ctx) {
                console.error('无法获取上方图表canvas context');
                return;
            }

            // 根据图表类型创建相应的图表
            if (currentTopChartType === 'polar') {
                topChart = new Chart(ctx, {
                    type: 'polarArea',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '极坐标图',
                            data: [],
                            backgroundColor: [],
                            borderColor: [],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: { duration: 0 }
                    }
                });
            } else {
                topChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '数据',
                            data: [],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: { duration: 0 },
                        scales: {
                            x: { title: { display: true, text: '时间 (秒)' }, type: 'linear' },
                            y: { title: { display: true, text: '幅值' } }
                        }
                    }
                });
            }

            // 立即更新图表数据
            console.log('上方图表已重新创建，类型:', currentTopChartType);
            updateTopChart();
        }

        // 重新创建下方图表
        function recreateBottomChart() {
            console.log('开始重新创建下方图表，类型:', currentBottomChartType);

            // 销毁现有图表
            if (bottomChart) {
                console.log('销毁现有下方图表');
                bottomChart.destroy();
                bottomChart = null;
            }

            const ctx = elements.bottomChartCanvas.getContext('2d');
            if (!ctx) {
                console.error('无法获取下方图表canvas context');
                return;
            }

            // 根据图表类型创建相应的图表
            if (currentBottomChartType === 'polar') {
                bottomChart = new Chart(ctx, {
                    type: 'polarArea',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '极坐标图',
                            data: [],
                            backgroundColor: [],
                            borderColor: [],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: { duration: 0 }
                    }
                });
            } else {
                bottomChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '数据',
                            data: [],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: { duration: 0 },
                        scales: {
                            x: { title: { display: true, text: '频率 (Hz)' }, type: 'linear' },
                            y: { title: { display: true, text: '幅值' } }
                        }
                    }
                });
            }

            // 立即更新图表数据
            console.log('下方图表已重新创建，类型:', currentBottomChartType);
            updateBottomChart();
        }

        // 显示消息提示
        function showMessage(message, type = 'info') {
            // 创建消息元素
            const messageEl = document.createElement('div');
            messageEl.className = `message-toast message-${type}`;
            messageEl.textContent = message;

            // 添加样式
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;

            document.body.appendChild(messageEl);

            // 显示动画
            setTimeout(() => {
                messageEl.style.opacity = '1';
                messageEl.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                messageEl.style.opacity = '0';
                messageEl.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 300);
            }, 3000);
        }

        // 事件监听器
        function setupEventListeners() {
            // 串口连接事件
            elements.connectBtn.addEventListener('click', connectSerial);
            elements.disconnectBtn.addEventListener('click', disconnectSerial);

            // 串口选择事件
            const selectPortBtn = document.getElementById('selectPortBtn');
            if (selectPortBtn) {
                selectPortBtn.addEventListener('click', selectPort);
            }

            elements.portSelect.addEventListener('change', async (e) => {
                if (e.target.value === '') {
                    await selectPort();
                }
            });

            // 监视器控制事件
            elements.clearMonitorBtn.addEventListener('click', () => {
                elements.monitorDisplay.innerHTML = '<div class="monitor-placeholder"><i class="fas fa-terminal"></i><p>监视器已清空</p></div>';
            });

            elements.pauseMonitorBtn.addEventListener('click', (e) => {
                reading = !reading;
                if (reading) {
                    e.target.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                    elements.monitorStatus.innerHTML = '<span class="status-badge receiving">接收中</span>';
                    if (connected) startReading();
                } else {
                    e.target.innerHTML = '<i class="fas fa-play"></i> 继续';
                    elements.monitorStatus.innerHTML = '<span class="status-badge paused">已暂停</span>';
                }
            });

            // 数据发送事件
            elements.sendOnceBtn.addEventListener('click', async () => {
                const data = elements.sendDataInput.value.trim();
                const format = elements.sendFormatSelect.value;
                if (data) {
                    await sendData(data, format);
                }
            });

            elements.sendRepeatBtn.addEventListener('click', () => {
                const data = elements.sendDataInput.value.trim();
                const format = elements.sendFormatSelect.value;
                const interval = parseInt(elements.sendIntervalInput.value);

                if (!data) {
                    alert('请输入要发送的数据');
                    return;
                }

                if (sendInterval) {
                    stopSendInterval();
                } else {
                    sendInterval = setInterval(async () => {
                        await sendData(data, format);
                    }, interval);
                    elements.sendRepeatBtn.innerHTML = '<i class="fas fa-stop"></i> 停止发送';
                }
            });

            // 快速发送按钮事件
            document.querySelectorAll('.quick-send-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    elements.sendDataInput.value = btn.dataset.value;
                });
            });

            // 协议选择事件
            elements.protocolSelect.addEventListener('change', (e) => {
                if (e.target.value === 'custom') {
                    elements.protocolConfig.style.display = 'block';
                } else {
                    elements.protocolConfig.style.display = 'none';
                }
            });

            // 统计重置事件
            elements.resetStatsBtn.addEventListener('click', () => {
                statistics = {
                    receivedBytes: 0,
                    sentBytes: 0,
                    packets: 0,
                    connectionStartTime: connected ? Date.now() : null,
                    errors: 0
                };
                updateStatistics();
            });

            // 导出统计报告事件
            elements.exportStatsBtn.addEventListener('click', () => {
                const timestamp = new Date().toLocaleString('zh-CN');
                const reportText = `串口调试统计报告
=====================================

生成时间: ${timestamp}

连接信息:
---------
波特率: ${elements.baudRateSelect.value}
数据位: ${elements.dataBitsSelect.value}
停止位: ${elements.stopBitsSelect.value}

统计数据:
---------
数据包总数: ${statistics.packets}
发送字节数: ${statistics.sentBytes}
接收字节数: ${statistics.receivedBytes}
错误数量: ${statistics.errors}
连接时长: ${statistics.connectionStartTime ? Math.floor((Date.now() - statistics.connectionStartTime) / 1000) : 0}秒
传输速率: ${statistics.connectionStartTime ? ((statistics.sentBytes + statistics.receivedBytes) / ((Date.now() - statistics.connectionStartTime) / 1000)).toFixed(2) : 0} bytes/s
错误率: ${statistics.packets > 0 ? (statistics.errors / statistics.packets * 100).toFixed(2) : 0}%

报告结束
=====================================`;

                const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `串口调试报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                a.click();
                URL.revokeObjectURL(url);
            });

            // 图表类型切换事件
            if (elements.topChartTypeSelect) {
                elements.topChartTypeSelect.addEventListener('change', (e) => {
                    switchTopChartType(e.target.value);
                });
            }

            if (elements.bottomChartTypeSelect) {
                elements.bottomChartTypeSelect.addEventListener('change', (e) => {
                    switchBottomChartType(e.target.value);
                });
            }

            // 图表操作事件
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const action = btn.dataset.action;
                    const chartType = btn.dataset.chart;
                    handleChartAction(action, chartType);
                });
            });

            // 刷新图表事件
            if (elements.refreshChartsBtn) {
                elements.refreshChartsBtn.addEventListener('click', () => {
                    // 重新初始化图表
                    if (topChart) {
                        topChart.destroy();
                        topChart = null;
                    }
                    if (bottomChart) {
                        bottomChart.destroy();
                        bottomChart = null;
                    }

                    // 清空数据
                    receivedData = [];

                    // 重新创建图表
                    initCharts();

                    // 显示成功消息
                    showMessage('图表已刷新', 'success');
                });
            }

            // 导出全部事件
            if (elements.exportAllBtn) {
                elements.exportAllBtn.addEventListener('click', () => {
                    // 导出上方图表
                    if (topChart) {
                        exportChart('png', 'top');
                        setTimeout(() => exportChartData('top'), 500);
                    }

                    // 导出下方图表
                    if (bottomChart) {
                        setTimeout(() => {
                            exportChart('png', 'bottom');
                            setTimeout(() => exportChartData('bottom'), 500);
                        }, 1000);
                    }

                    // 显示成功消息
                    showMessage('正在导出所有图表数据...', 'info');
                });
            }

            // 导航菜单事件
            document.querySelectorAll('.category-header').forEach(header => {
                header.addEventListener('click', () => {
                    const category = header.closest('.nav-category');
                    category.classList.toggle('expanded');
                    header.classList.toggle('expanded');
                });
            });
        }

        // 处理图表操作
        function handleChartAction(action, chartType) {
            const chart = chartType === 'top' ? topChart : bottomChart;
            if (!chart) return;

            switch (action) {
                case 'zoom-x-in':
                    chart.zoom({ x: 1.1 });
                    break;
                case 'zoom-x-out':
                    chart.zoom({ x: 0.9 });
                    break;
                case 'zoom-y-in':
                    chart.zoom({ y: 1.1 });
                    break;
                case 'zoom-y-out':
                    chart.zoom({ y: 0.9 });
                    break;
                case 'reset':
                    chart.resetZoom();
                    break;
                case 'export-png':
                    exportChart('png', chartType);
                    break;
                case 'export-jpg':
                    exportChart('jpg', chartType);
                    break;
                case 'export-excel':
                    exportChartData(chartType);
                    break;
                case 'fullscreen':
                    toggleFullscreen(chartType);
                    break;
            }
        }

        // 导出图表
        function exportChart(format, chartType) {
            const chart = chartType === 'top' ? topChart : bottomChart;
            const chartTypeName = chartType === 'top' ? currentTopChartType : currentBottomChartType;

            if (!chart) return;

            const canvas = chart.canvas;
            const url = canvas.toDataURL(`image/${format}`);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chart_${chartType}_${chartTypeName}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.${format}`;
            a.click();
        }

        // 导出图表数据
        function exportChartData(chartType) {
            if (receivedData.length === 0) {
                alert('没有数据可导出');
                return;
            }

            const chartTypeName = chartType === 'top' ? currentTopChartType : currentBottomChartType;
            const wb = XLSX.utils.book_new();

            let sheetData = [['时间点', '数值']];

            // 根据图表类型导出相应数据
            switch (chartTypeName) {
                case 'time':
                    sheetData = [['时间(s)', '幅值'], ...receivedData.map((value, index) => [index * 0.01, value])];
                    break;
                case 'frequency':
                    const fftData = performSimpleFFT(receivedData);
                    sheetData = [['频率(Hz)', '幅值'], ...fftData.map((value, index) => [index * 1000 / fftData.length, value])];
                    break;
                case 'envelope':
                    const envelopeData = calculateEnvelope(receivedData);
                    sheetData = [['时间(s)', '包络值'], ...envelopeData.map((value, index) => [index * 0.01, value])];
                    break;
                case 'phase':
                    const phaseData = calculateInstantaneousPhase(receivedData);
                    sheetData = [['时间(s)', '相位(弧度)'], ...phaseData.map((value, index) => [index * 0.01, value])];
                    break;
                case 'cdf':
                    const cdfData = calculateCDF(receivedData);
                    sheetData = [['数值', '累积概率'], ...cdfData.map(point => [point.x, point.y])];
                    break;
                case 'psd':
                    const psdData = calculatePSD(receivedData);
                    sheetData = [['频率(Hz)', 'PSD'], ...psdData.map(point => [point.x, point.y])];
                    break;
                case 'vector':
                    const vectorData = calculateVectorData(receivedData);
                    sheetData = [['X分量', 'Y分量'], ...vectorData.map(point => [point.x, point.y])];
                    break;
                case 'polar':
                    const polarData = calculatePolarData(receivedData);
                    sheetData = [['角度', '幅值'], ...polarData.labels.map((label, index) => [label, polarData.values[index]])];
                    break;
                case 'wavelet':
                    const waveletData = calculateWaveletData(receivedData);
                    sheetData = [['时间(s)', '小波系数'], ...waveletData.map(point => [point.x, point.y])];
                    break;
                default:
                    sheetData = [['时间点', '数值'], ...receivedData.map((value, index) => [index, value])];
            }

            const ws = XLSX.utils.aoa_to_sheet(sheetData);
            XLSX.utils.book_append_sheet(wb, ws, `${chartType}_${chartTypeName}`);
            XLSX.writeFile(wb, `serial_${chartType}_${chartTypeName}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`);
        }

        // 切换全屏
        function toggleFullscreen(chartType) {
            const chartContainer = chartType === 'top' ?
                document.getElementById('topChartCard') :
                document.getElementById('bottomChartCard');

            if (!document.fullscreenElement) {
                chartContainer.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 创建粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 同步控制面板高度
        function syncPanelHeights() {
            const visualizationArea = document.querySelector('.visualization-area');
            const controlPanel = document.querySelector('.control-panel');

            if (visualizationArea && controlPanel) {
                const visualHeight = visualizationArea.offsetHeight;
                controlPanel.style.height = visualHeight + 'px';
                controlPanel.style.maxHeight = visualHeight + 'px';
            }
        }

        // 页面初始化
        function initializePage() {
            // 创建粒子效果
            createParticles();

            // 设置事件监听器
            setupEventListeners();

            // 初始化图表
            initCharts();

            // 更新串口列表
            updatePortList();

            // 显示图表占位符
            elements.topChartPlaceholder.style.display = 'block';
            elements.bottomChartPlaceholder.style.display = 'block';
            elements.topChartCanvas.style.display = 'none';
            elements.bottomChartCanvas.style.display = 'none';

            // 定时更新统计信息（仅在连接时更新时长）
            setInterval(() => {
                if (connected && statistics.connectionStartTime) {
                    updateStatistics();
                }
            }, 1000);

            // 添加测试数据按钮（仅用于演示）
            addTestDataButton();

            // 同步面板高度
            setTimeout(() => {
                syncPanelHeights();
                // 监听窗口大小变化
                window.addEventListener('resize', syncPanelHeights);
            }, 100);

            console.log('串口调试平台初始化完成');
        }

        // 添加测试数据按钮（用于演示）
        function addTestDataButton() {
            const testBtn = document.createElement('button');
            testBtn.className = 'btn btn-outline btn-small';
            testBtn.innerHTML = '<i class="fas fa-vial"></i> 生成测试数据';
            testBtn.style.marginLeft = '10px';

            testBtn.addEventListener('click', () => {
                generateTestData();
            });

            const visualizationActions = document.querySelector('.visualization-actions');
            if (visualizationActions) {
                visualizationActions.appendChild(testBtn);
            }
        }

        // 生成测试数据
        function generateTestData() {
            // 停止任何正在运行的模拟连接
            if (connected) {
                connected = false;
                updateConnectionStatus(false);
            }

            // 清空现有数据
            receivedData = [];

            // 确保图表已初始化
            if (!topChart || !bottomChart) {
                initCharts();
            }

            // 生成模拟的正弦波数据
            const testData = [];
            const frequency = 2; // Hz
            const amplitude = 100;
            const sampleRate = 100; // samples per second
            const duration = 3; // seconds
            const maxDataPoints = 300; // 限制最大数据点数

            for (let i = 0; i < Math.min(duration * sampleRate, maxDataPoints); i++) {
                const t = i / sampleRate;
                const value = amplitude * Math.sin(2 * Math.PI * frequency * t) +
                             20 * Math.sin(2 * Math.PI * frequency * 5 * t) +
                             Math.random() * 10 - 5; // 添加一些噪声
                testData.push(value);
            }

            // 模拟接收数据
            receivedData = testData.slice(0, maxDataPoints);
            statistics.receivedBytes += receivedData.length * 4;
            statistics.packets += receivedData.length;

            // 显示在监视器中
            const hexData = testData.slice(0, 10).map(v =>
                Math.round(v).toString(16).toUpperCase().padStart(2, '0')
            ).join(' ') + '...';
            displayInMonitor(hexData, 'received');

            // 显示图表
            if (elements.topChartPlaceholder) {
                elements.topChartPlaceholder.style.display = 'none';
            }
            if (elements.bottomChartPlaceholder) {
                elements.bottomChartPlaceholder.style.display = 'none';
            }
            if (elements.topChartCanvas) {
                elements.topChartCanvas.style.display = 'block';
            }
            if (elements.bottomChartCanvas) {
                elements.bottomChartCanvas.style.display = 'block';
            }

            // 延迟更新图表，确保DOM已更新
            setTimeout(() => {
                // 强制重新计算图表大小
                if (topChart) {
                    topChart.resize();
                    topChart.update('none');
                }
                if (bottomChart) {
                    bottomChart.resize();
                    bottomChart.update('none');
                }
                updateCharts();
                updateStatistics();
            }, 200);

            console.log('测试数据已生成:', testData.length, '个数据点');
        }

        // 模拟连接状态（用于演示）
        function simulateConnection() {
            if (!connected) {
                connected = true;
                statistics.connectionStartTime = Date.now();
                updateConnectionStatus(true);

                // 模拟定期接收数据
                const dataInterval = setInterval(() => {
                    if (!connected) {
                        clearInterval(dataInterval);
                        return;
                    }

                    // 生成随机数据点
                    const newValue = 50 + 30 * Math.sin(Date.now() / 1000) + Math.random() * 20 - 10;
                    receivedData.push(newValue);

                    if (receivedData.length > 1000) {
                        receivedData = receivedData.slice(-1000);
                    }

                    statistics.receivedBytes += 4;
                    statistics.packets++;

                    // 显示图表
                    if (elements.topChartPlaceholder.style.display !== 'none') {
                        elements.topChartPlaceholder.style.display = 'none';
                        elements.bottomChartPlaceholder.style.display = 'none';
                        elements.topChartCanvas.style.display = 'block';
                        elements.bottomChartCanvas.style.display = 'block';
                    }

                    updateCharts();
                }, 100); // 每100ms更新一次
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 延迟一点时间确保所有资源加载完成
            setTimeout(() => {
                initializePage();

                // 隐藏加载动画
                const loader = document.getElementById('pageLoader');
                loader.classList.add('loaded');

                // 完全移除加载器
                setTimeout(() => {
                    loader.remove();
                }, 600);
            }, 1000);
        });

        // WebSocket消息处理 - 已禁用
        function handleWebSocketMessage(data) {
            console.warn('WebSocket消息处理已禁用');
        }

        // 保存当前配置
        async function saveCurrentConfig() {
            if (!window.SerialAPI) return;

            try {
                const config = {
                    port: elements.portSelect.value,
                    baudRate: parseInt(elements.baudRateSelect.value),
                    dataBits: parseInt(elements.dataBitsSelect.value),
                    stopBits: parseFloat(elements.stopBitsSelect.value),
                    parity: elements.paritySelect?.value || 'none',
                    flowControl: 'none',
                    description: `配置 ${new Date().toLocaleString()}`
                };

                const configName = `Config_${Date.now()}`;

                const result = await window.SerialAPI.saveConfig(config, configName);

                if (result.success) {
                    console.log('配置保存成功:', result.data);
                }
            } catch (error) {
                console.warn('配置保存失败:', error);
            }
        }

        // 加载保存的配置（优化版本）
        async function loadSavedConfigs() {
            if (!window.SerialAPI) return;

            try {
                showNotification('正在加载配置...', 'info');

                // 使用优化后的配置获取方法
                const result = await window.SerialAPI.getSavedConfigs();

                if (result.success && result.data) {
                    displaySavedConfigs(result.data);

                    if (result.data.length === 0) {
                        showNotification('暂无保存的配置，请创建新配置', 'warning');
                    } else {
                        showNotification(`加载了 ${result.data.length} 个配置`, 'success');
                    }
                } else {
                    displaySavedConfigs([]);
                    showNotification('配置加载失败', 'error');
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                showNotification('加载配置失败，请检查网络连接或重试', 'error');
                displaySavedConfigs([]);
            }
        }

        // 显示配置列表
        function displaySavedConfigs(configs) {
            // 查找配置列表容器
            let configList = document.getElementById('saved-configs-list');

            // 如果不存在，创建一个简单的显示区域
            if (!configList) {
                const container = document.querySelector('.config-section') || document.body;
                configList = document.createElement('div');
                configList.id = 'saved-configs-list';
                configList.style.cssText = `
                    margin-top: 20px;
                    padding: 15px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    background: #f9f9f9;
                `;
                container.appendChild(configList);
            }

            if (configs.length === 0) {
                configList.innerHTML = `
                    <div class="no-configs" style="text-align: center; color: #666;">
                        <p>暂无保存的配置</p>
                        <button onclick="showConfigDialog()" style="
                            padding: 8px 16px;
                            background: #007bff;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                        ">创建第一个配置</button>
                    </div>
                `;
                return;
            }

            configList.innerHTML = `
                <h4 style="margin-top: 0;">保存的配置 (${configs.length})</h4>
                ${configs.map(config => `
                    <div class="config-item" data-config-id="${config.configId}" style="
                        margin-bottom: 10px;
                        padding: 12px;
                        border: 1px solid #ccc;
                        border-radius: 4px;
                        background: white;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <div class="config-info">
                            <h5 style="margin: 0 0 5px 0;">${config.configName}</h5>
                            <div class="config-details" style="font-size: 12px; color: #666;">
                                <span>波特率: ${config.baudRate}</span> |
                                <span>数据位: ${config.dataBits}</span> |
                                <span>停止位: ${config.stopBits}</span> |
                                <span>校验: ${config.parity}</span>
                            </div>
                            <small style="color: #999;">创建时间: ${new Date(config.createdAt).toLocaleString()}</small>
                        </div>
                        <div class="config-actions">
                            <button onclick="loadConfig('${config.configId}')" style="
                                margin-right: 5px;
                                padding: 4px 8px;
                                background: #28a745;
                                color: white;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 12px;
                            ">加载</button>
                            <button onclick="deleteConfig('${config.configId}')" style="
                                padding: 4px 8px;
                                background: #dc3545;
                                color: white;
                                border: none;
                                border-radius: 3px;
                                cursor: pointer;
                                font-size: 12px;
                            ">删除</button>
                        </div>
                    </div>
                `).join('')}
            `;
        }

        // 删除配置
        async function deleteConfig(configId) {
            if (!confirm('确定要删除这个配置吗？')) return;

            try {
                await window.SerialAPI.deleteConfig(configId);
                await loadSavedConfigs(); // 重新加载列表
                showNotification('配置删除成功', 'success');
            } catch (error) {
                console.error('删除配置失败:', error);
                showNotification('删除配置失败', 'error');
            }
        }

        // 加载配置到当前表单
        function loadConfig(configId) {
            if (!window.SerialAPI) return;

            const configs = window.SerialAPI.getLocalConfigs();
            const config = configs.find(c => c.configId === configId);

            if (!config) {
                showNotification('配置不存在', 'error');
                return;
            }

            // 填充表单
            const baudRateSelect = document.getElementById('baud-rate');
            const dataBitsSelect = document.getElementById('data-bits');
            const stopBitsSelect = document.getElementById('stop-bits');
            const paritySelect = document.getElementById('parity');

            if (baudRateSelect) baudRateSelect.value = config.baudRate;
            if (dataBitsSelect) dataBitsSelect.value = config.dataBits;
            if (stopBitsSelect) stopBitsSelect.value = config.stopBits;
            if (paritySelect) paritySelect.value = config.parity;

            showNotification(`已加载配置: ${config.configName}`, 'success');
        }

        // 显示配置对话框（简单版本）
        function showConfigDialog() {
            const configName = prompt('请输入配置名称:');
            if (!configName) return;

            // 获取当前表单值
            const baudRate = document.getElementById('baud-rate')?.value || '9600';
            const dataBits = document.getElementById('data-bits')?.value || '8';
            const stopBits = document.getElementById('stop-bits')?.value || '1';
            const parity = document.getElementById('parity')?.value || 'none';

            const config = {
                baudRate: parseInt(baudRate),
                dataBits: parseInt(dataBits),
                stopBits: parseInt(stopBits),
                parity: parity,
                flowControl: 'none'
            };

            saveCurrentConfigWithName(config, configName);
        }

        // 保存配置（带名称）
        async function saveCurrentConfigWithName(config, name) {
            if (!window.SerialAPI) return;

            try {
                const result = await window.SerialAPI.saveConfig(config, name);
                if (result.success) {
                    showNotification('配置保存成功', 'success');
                    await loadSavedConfigs(); // 重新加载列表
                }
            } catch (error) {
                console.error('配置保存失败:', error);
                showNotification('配置保存失败', 'error');
            }
        }

        // 获取会话历史
        async function loadSessionHistory() {
            if (!window.SerialAPI) return;

            try {
                const result = await window.SerialAPI.getSessionHistory({
                    page: 1,
                    limit: 10
                });

                if (result.success && result.data.sessions) {
                    console.log('会话历史:', result.data.sessions);
                    // 可以在UI中显示会话历史
                }
            } catch (error) {
                console.warn('加载会话历史失败:', error);
            }
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;

            // 添加样式
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                word-wrap: break-word;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            `;

            // 根据类型设置背景色
            switch (type) {
                case 'success':
                    notification.style.backgroundColor = '#10b981';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#ef4444';
                    break;
                case 'warning':
                    notification.style.backgroundColor = '#f59e0b';
                    break;
                case 'info':
                default:
                    notification.style.backgroundColor = '#3b82f6';
                    break;
            }

            // 添加到页面
            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 页面加载完成后加载配置和历史
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                loadSavedConfigs();
                loadSessionHistory();
            }, 1000);
        });
    </script>
</body>
</html>
