/**
 * 页面性能优化器
 * 提高页面加载速度和运行性能
 */
class PageOptimizer {
    constructor() {
        this.loadStartTime = performance.now();
        this.criticalResources = new Set();
        this.deferredTasks = [];
        
        this.init();
    }
    
    init() {
        // 立即执行的优化
        this.immediateOptimizations();
        
        // DOM加载完成后的优化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.domOptimizations());
        } else {
            this.domOptimizations();
        }
        
        // 页面完全加载后的优化
        window.addEventListener('load', () => this.postLoadOptimizations());
    }
    
    /**
     * 立即执行的优化
     */
    immediateOptimizations() {
        // 预连接到重要域名
        this.preconnectDomains([
            'fonts.googleapis.com',
            'fonts.gstatic.com'
        ]);
        
        // 预加载关键资源
        this.preloadCriticalResources();
        
        // 优化CSS加载
        this.optimizeCSSLoading();
    }
    
    /**
     * DOM加载完成后的优化
     */
    domOptimizations() {
        // 延迟加载非关键图片
        this.lazyLoadImages();
        
        // 优化脚本加载
        this.optimizeScriptLoading();
        
        // 减少重排重绘
        this.optimizeRendering();
        
        // 执行延迟任务
        this.executeDeferredTasks();
    }
    
    /**
     * 页面完全加载后的优化
     */
    postLoadOptimizations() {
        // 预加载下一页可能需要的资源
        this.prefetchNextPageResources();
        
        // 清理不需要的资源
        this.cleanupResources();
        
        // 记录性能指标
        this.recordPerformanceMetrics();
    }
    
    /**
     * 预连接到重要域名
     */
    preconnectDomains(domains) {
        domains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'preconnect';
            link.href = `https://${domain}`;
            link.crossOrigin = 'anonymous';
            document.head.appendChild(link);
        });
    }
    
    /**
     * 预加载关键资源
     */
    preloadCriticalResources() {
        const criticalResources = [
            { href: 'libs/css/font-awesome.min.css', as: 'style' },
            { href: 'css/style.css', as: 'style' },
            { href: 'css/toast-system.css', as: 'style' },
            { href: 'libs/js/chart.min.js', as: 'script' },
            { href: 'js/toast-system.js', as: 'script' }
        ];
        
        criticalResources.forEach(resource => {
            if (!document.querySelector(`link[href="${resource.href}"]`)) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource.href;
                link.as = resource.as;
                if (resource.as === 'style') {
                    link.onload = () => {
                        link.rel = 'stylesheet';
                    };
                }
                document.head.appendChild(link);
                this.criticalResources.add(resource.href);
            }
        });
    }
    
    /**
     * 优化CSS加载
     */
    optimizeCSSLoading() {
        // 内联关键CSS
        const criticalCSS = `
            body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
            .page-loader { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #fff; z-index: 9999; }
            .loaded { opacity: 0; pointer-events: none; transition: opacity 0.3s ease; }
        `;
        
        const style = document.createElement('style');
        style.textContent = criticalCSS;
        document.head.insertBefore(style, document.head.firstChild);
    }
    
    /**
     * 延迟加载图片
     */
    lazyLoadImages() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        } else {
            // 降级方案
            images.forEach(img => {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            });
        }
    }
    
    /**
     * 优化脚本加载
     */
    optimizeScriptLoading() {
        const scripts = document.querySelectorAll('script[src]:not([async]):not([defer])');
        scripts.forEach(script => {
            if (!this.criticalResources.has(script.src)) {
                script.defer = true;
            }
        });
    }
    
    /**
     * 优化渲染性能
     */
    optimizeRendering() {
        // 批量DOM操作
        const fragment = document.createDocumentFragment();
        const elementsToMove = document.querySelectorAll('[data-defer-render]');
        
        elementsToMove.forEach(el => {
            fragment.appendChild(el);
        });
        
        if (fragment.children.length > 0) {
            requestAnimationFrame(() => {
                document.body.appendChild(fragment);
            });
        }
    }
    
    /**
     * 执行延迟任务
     */
    executeDeferredTasks() {
        this.deferredTasks.forEach(task => {
            try {
                task();
            } catch (error) {
                console.error('延迟任务执行失败:', error);
            }
        });
        this.deferredTasks = [];
    }
    
    /**
     * 预加载下一页资源
     */
    prefetchNextPageResources() {
        const nextPageLinks = document.querySelectorAll('a[href]');
        const prefetchedUrls = new Set();
        
        nextPageLinks.forEach(link => {
            if (!prefetchedUrls.has(link.href) && link.href.includes(location.origin)) {
                const prefetchLink = document.createElement('link');
                prefetchLink.rel = 'prefetch';
                prefetchLink.href = link.href;
                document.head.appendChild(prefetchLink);
                prefetchedUrls.add(link.href);
            }
        });
    }
    
    /**
     * 清理资源
     */
    cleanupResources() {
        // 移除不需要的预加载链接
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        preloadLinks.forEach(link => {
            if (this.criticalResources.has(link.href)) {
                link.remove();
            }
        });
    }
    
    /**
     * 记录性能指标
     */
    recordPerformanceMetrics() {
        const loadTime = performance.now() - this.loadStartTime;
        console.log(`页面加载完成，总耗时: ${loadTime.toFixed(2)}ms`);
        
        // 记录关键性能指标
        if ('getEntriesByType' in performance) {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                console.log('性能指标:', {
                    DNS查询: `${navigation.domainLookupEnd - navigation.domainLookupStart}ms`,
                    TCP连接: `${navigation.connectEnd - navigation.connectStart}ms`,
                    请求响应: `${navigation.responseEnd - navigation.requestStart}ms`,
                    DOM解析: `${navigation.domContentLoadedEventEnd - navigation.domLoading}ms`,
                    资源加载: `${navigation.loadEventEnd - navigation.domContentLoadedEventEnd}ms`
                });
            }
        }
    }
    
    /**
     * 添加延迟任务
     */
    addDeferredTask(task) {
        this.deferredTasks.push(task);
    }
}

// 自动初始化页面优化器
if (typeof window !== 'undefined') {
    window.pageOptimizer = new PageOptimizer();
}
