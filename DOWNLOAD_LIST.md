# 📦 依赖下载清单

## 需要手动下载的文件：

### JavaScript库
1. **Chart.js插件** (已有Chart.js主库)
   - URL: https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js
   - 保存为: libs/js/chartjs-plugin-zoom.min.js

2. **XLSX (Excel处理)**
   - URL: https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js
   - 保存为: libs/js/xlsx.full.min.js

3. **Particles.js (粒子动画)**
   - URL: https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js
   - 保存为: libs/js/particles.min.js

### CSS库
1. **Font Awesome (图标)**
   - URL: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css
   - 保存为: libs/css/font-awesome.min.css

2. **Animate.css (动画)**
   - URL: https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css
   - 保存为: libs/css/animate.min.css

## 使用方法：
1. 运行此脚本: `node setup-local-libs.js`
2. 手动下载上述文件到对应目录
3. 使用生成的 *-local.html 文件替代原文件
4. 打开 dependency-status.html 检查依赖状态

## 注意事项：
- Google字体仍使用CDN，如需本地化请单独处理
- 某些CDN可能需要翻墙访问
- 建议保留原始HTML文件作为备份
