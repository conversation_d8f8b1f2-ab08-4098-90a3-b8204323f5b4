{"name": "chartjs-plugin-zoom", "homepage": "https://www.chartjs.org/chartjs-plugin-zoom/", "description": "Plugin that enables zoom and pan functionality in Chart.js charts.", "version": "2.2.0", "license": "MIT", "jsdelivr": "dist/chartjs-plugin-zoom.min.js", "unpkg": "dist/chartjs-plugin-zoom.min.js", "main": "dist/chartjs-plugin-zoom.js", "module": "dist/chartjs-plugin-zoom.esm.js", "types": "types/index.d.ts", "repository": {"type": "git", "url": "https://github.com/chartjs/chartjs-plugin-zoom.git"}, "scripts": {"autobuild": "rollup -c -w", "build": "rollup -c", "dev": "karma start --auto-watch --no-single-run --browsers chrome", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox", "docs": "npm run build && vuepress build docs --no-cache", "docs:dev": "concurrently \"npm run autobuild\" \"vuepress dev docs --no-cache\"", "lint-js": "eslint \"samples/**/*.html\" \"test/**/*.js\" \"src/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "lint-tsc": "tsc", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/", "lint": "concurrently \"npm:lint-*\"", "test": "cross-env NODE_ENV=test concurrently \"npm:test-*\"", "test-karma": "karma start --auto-watch --single-run --coverage", "test-types": "tsc -p types/tests/"}, "files": ["dist/*.js", "types/*.d.ts"], "devDependencies": {"@babel/core": "^7.20.2", "@babel/preset-env": "^7.20.2", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@simonbrunel/vuepress-plugin-versions": "^0.2.0", "@types/linkify-it": "^3.0.5", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "babel-loader": "^8.3.0", "chart.js": "^4.3.2", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-test-utils": "^0.5.0", "concurrently": "^9.1.0", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "date-fns": "^2.21.1", "eslint": "^8.2.0", "eslint-config-chartjs": "^0.3.0", "eslint-plugin-html": "^8.1.2", "eslint-plugin-markdown": "^2.0.1", "hammer-simulator": "^0.0.1", "jasmine": "^5.4.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-firefox-launcher": "^2.1.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "karma-rollup-preprocessor": "7.0.7", "karma-spec-reporter": "0.0.36", "ng-hammerjs": "^2.0.8", "pixelmatch": "^6.0.0", "rollup": "^4.12.1", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-istanbul": "^5.0.0", "typedoc": "^0.26.11", "typedoc-plugin-markdown": "^4.2.10", "typescript": "^5.6.3", "vuepress": "^1.8.2", "vuepress-plugin-flexsearch": "^0.3.0", "vuepress-plugin-redirect": "^1.2.5", "vuepress-plugin-typedoc": "^0.9.2", "vuepress-theme-chartjs": "^0.2.0"}, "peerDependencies": {"chart.js": ">=3.2.0"}, "dependencies": {"@types/hammerjs": "^2.0.45", "hammerjs": "^2.0.8"}}