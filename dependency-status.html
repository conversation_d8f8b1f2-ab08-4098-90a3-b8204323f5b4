<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>依赖状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .ok { background: #d4edda; color: #155724; }
        .missing { background: #f8d7da; color: #721c24; }
        .test-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <h1>📦 依赖状态检查</h1>
    <div id="status"></div>
    <button class="test-btn" onclick="testDependencies()">测试依赖</button>
    <button class="test-btn" onclick="testChart()">测试Chart.js</button>
    
    <script src="libs/js/chart.umd.js"></script>
    <script>
        function checkFile(path) {
            return fetch(path, { method: 'HEAD' })
                .then(response => response.ok)
                .catch(() => false);
        }
        
        async function testDependencies() {
            const dependencies = [
                'libs/js/chart.umd.js',
                'libs/js/chartjs-plugin-zoom.min.js',
                'libs/js/xlsx.full.min.js',
                'libs/js/particles.min.js',
                'libs/css/font-awesome.min.css',
                'libs/css/animate.min.css'
            ];
            
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<h3>检查中...</h3>';
            
            let html = '<h3>依赖状态:</h3>';
            
            for (const dep of dependencies) {
                const exists = await checkFile(dep);
                const status = exists ? 'ok' : 'missing';
                const icon = exists ? '✅' : '❌';
                html += `<div class="status ${status}">${icon} ${dep}</div>`;
            }
            
            statusDiv.innerHTML = html;
        }
        
        function testChart() {
            if (typeof Chart !== 'undefined') {
                alert('✅ Chart.js 加载成功！版本: ' + (Chart.version || '未知'));
            } else {
                alert('❌ Chart.js 未加载');
            }
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', testDependencies);
    </script>
</body>
</html>