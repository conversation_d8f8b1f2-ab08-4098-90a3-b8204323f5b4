<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 性能优化 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 禁用不必要的功能 -->
    <meta name="format-detection" content="telephone=no">
    <meta name="format-detection" content="email=no">
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析平台 - 注册</title>

    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/register-enhanced.css">
    <link rel="stylesheet" href="css/background-effects.css">
    <link rel="stylesheet" href="css/toast-system.css">

    <!-- 添加动画库 -->
    <link rel="stylesheet" href="libs/css/animate.min.css"/>
    <!-- 添加Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- 添加动画库 -->
    <link rel="stylesheet" href="libs/css/animate.min.css"/>

    <!-- 修复页面加载器问题 -->
    <style>
        /* 完全禁用页面加载器 */
        .page-loader {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            pointer-events: none !important;
            z-index: -1 !important;
        }

        /* 确保页面内容正常显示 - 完全禁用滚动 */
        html, body {
            overflow: hidden !important;
            pointer-events: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
        }

        .login-container, .register-container {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 100 !important;
        }

        /* 确保背景元素正常显示 */
        .particles-container,
        .animated-bg,
        .decoration-elements,
        .geometric-bg,
        .floating-circle,
        .floating-square,
        .floating-triangle,
        .floating-dots,
        .gradient-orb,
        .particle,
        .enhanced-background,
        .data-visualization,
        .geometric-decorations,
        .gradient-orbs,
        .connection-network,
        .digital-rain,
        .wave-decorations,
        .starfield {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: none !important;
        }

        /* 确保页面布局正确 */
        .login-page {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            height: 100vh !important;
            overflow: hidden !important;
        }

        /* 强制显示注册窗口 */
        .register-container {
            display: flex !important;
            flex-direction: column !important;
            opacity: 1 !important;
            visibility: visible !important;
            position: relative !important;
            z-index: 1000 !important;
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 20px !important;
            padding: 30px !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
            width: 400px !important;
            max-width: 90vw !important;
            transform: none !important;
        }

        /* 确保windows-container正确显示 */
        .windows-container {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100vw !important;
            height: 100vh !important;
            position: relative !important;
        }
    </style>
</head>
<body class="login-page" style="overflow: visible !important; pointer-events: auto !important;">
    <!-- 页面加载器已完全移除 -->

    <div class="particles-container" id="particles-js"></div>

    <!-- 主容器包装两个窗口 -->
    <div class="windows-container">

    <!-- 直接的粒子效果 -->
    <div class="direct-particles">
        <div class="particle-dot p1"></div>
        <div class="particle-dot p2"></div>
        <div class="particle-dot p3"></div>
        <div class="particle-dot p4"></div>
        <div class="particle-dot p5"></div>
        <div class="particle-dot p6"></div>
        <div class="particle-dot p7"></div>
        <div class="particle-dot p8"></div>
        <div class="particle-dot p9"></div>
        <div class="particle-dot p10"></div>
        <div class="particle-dot p11"></div>
        <div class="particle-dot p12"></div>
        <div class="particle-dot p13"></div>
        <div class="particle-dot p14"></div>
        <div class="particle-dot p15"></div>
        <div class="particle-dot p16"></div>
        <div class="particle-dot p17"></div>
        <div class="particle-dot p18"></div>
        <div class="particle-dot p19"></div>
        <div class="particle-dot p20"></div>
    </div>

    <!-- 简单背景装饰 -->
    <div class="simple-decorations">
        <!-- 浮动圆圈 -->
        <div class="float-circle c1"></div>
        <div class="float-circle c2"></div>
        <div class="float-circle c3"></div>
        <div class="float-circle c4"></div>
        <div class="float-circle c5"></div>

        <!-- 渐变光球 -->
        <div class="glow-orb g1"></div>
        <div class="glow-orb g2"></div>
        <div class="glow-orb g3"></div>

        <!-- 数据线条 -->
        <div class="data-line d1"></div>
        <div class="data-line d2"></div>
        <div class="data-line d3"></div>
        <div class="data-line d4"></div>

        <!-- 几何形状 -->
        <div class="geo-shape triangle t1"></div>
        <div class="geo-shape triangle t2"></div>
        <div class="geo-shape square s1"></div>
        <div class="geo-shape square s2"></div>
    </div>

    <!-- 星光效果 -->
    <div class="starlight-effects">
        <div class="star-twinkle st1"></div>
        <div class="star-twinkle st2"></div>
        <div class="star-twinkle st3"></div>
        <div class="star-twinkle st4"></div>
        <div class="star-twinkle st5"></div>
        <div class="star-twinkle st6"></div>
        <div class="star-twinkle st7"></div>
        <div class="star-twinkle st8"></div>
    </div>

    <!-- 背景渐变层 -->
    <div class="background-gradient-layer"></div>

    <!-- 增强的背景装饰系统 -->
    <div class="enhanced-background">
        <!-- 主要动态背景元素 -->
        <div class="animated-bg">
            <div class="animated-shape shape1"></div>
            <div class="animated-shape shape2"></div>
            <div class="animated-shape shape3"></div>
            <div class="animated-shape shape4"></div>
            <div class="animated-shape shape5"></div>
            <div class="animated-shape shape6"></div>
        </div>

        <!-- 数据可视化装饰元素 -->
        <div class="data-visualization">
            <div class="chart-line chart1"></div>
            <div class="chart-line chart2"></div>
            <div class="chart-line chart3"></div>
            <div class="data-node node1"></div>
            <div class="data-node node2"></div>
            <div class="data-node node3"></div>
            <div class="data-node node4"></div>
            <div class="data-node node5"></div>
        </div>

        <!-- 几何图形装饰 -->
        <div class="geometric-decorations">
            <div class="floating-circle circle1"></div>
            <div class="floating-circle circle2"></div>
            <div class="floating-circle circle3"></div>
            <div class="floating-circle circle4"></div>
            <div class="floating-square square1"></div>
            <div class="floating-square square2"></div>
            <div class="floating-triangle triangle1"></div>
            <div class="floating-triangle triangle2"></div>
            <div class="floating-triangle triangle3"></div>
            <div class="floating-hexagon hex1"></div>
            <div class="floating-hexagon hex2"></div>
        </div>

        <!-- 渐变光球装饰 -->
        <div class="gradient-orbs">
            <div class="gradient-orb orb1"></div>
            <div class="gradient-orb orb2"></div>
            <div class="gradient-orb orb3"></div>
            <div class="gradient-orb orb4"></div>
            <div class="gradient-orb orb5"></div>
        </div>

        <!-- 连接线网络 -->
        <div class="connection-network">
            <div class="connection-line line1"></div>
            <div class="connection-line line2"></div>
            <div class="connection-line line3"></div>
            <div class="connection-line line4"></div>
            <div class="connection-line line5"></div>
            <div class="connection-dot dot1"></div>
            <div class="connection-dot dot2"></div>
            <div class="connection-dot dot3"></div>
            <div class="connection-dot dot4"></div>
            <div class="connection-dot dot5"></div>
            <div class="connection-dot dot6"></div>
        </div>

        <!-- 数字雨效果 -->
        <div class="digital-rain">
            <div class="rain-column column1"></div>
            <div class="rain-column column2"></div>
            <div class="rain-column column3"></div>
            <div class="rain-column column4"></div>
            <div class="rain-column column5"></div>
        </div>

        <!-- 波浪装饰 -->
        <div class="wave-decorations">
            <div class="wave wave1"></div>
            <div class="wave wave2"></div>
            <div class="wave wave3"></div>
        </div>

        <!-- 鼠标跟随光效 -->
        <div class="mouse-follower" id="mouseFollower"></div>

        <!-- 星空效果 -->
        <div class="starfield">
            <div class="star star1"></div>
            <div class="star star2"></div>
            <div class="star star3"></div>
            <div class="star star4"></div>
            <div class="star star5"></div>
            <div class="star star6"></div>
            <div class="star star7"></div>
            <div class="star star8"></div>
            <div class="star star9"></div>
            <div class="star star10"></div>
        </div>
    </div>

    <!-- 提示弹窗容器 -->
    <div class="toast-container" id="toastContainer"></div>
    


    <div class="login-container register-container animate__animated animate__fadeIn" id="registerContainer">
        <div class="login-content">
            <div class="login-header">
                <div class="register-icon animate__animated animate__bounceIn">
                    <i data-feather="user-plus"></i>
                </div>
                <h1 class="animate__animated animate__fadeInUp">创建新账号</h1>
                <p class="login-subtitle animate__animated animate__fadeIn animate__delay-1s">
                    加入我们，开启专业数据分析之旅
                </p>

                <!-- 手写艺术字 -->
                <div class="handwriting-container">
                    <svg class="handwriting-svg" viewBox="0 0 400 35" xmlns="http://www.w3.org/2000/svg">
                        <!-- We are on the way (正确字母，缩小间距) -->
                        <!-- W -->
                        <path id="letter-w" d="M8 10 L12 30 L16 15 L20 30 L24 10" stroke="url(#gradient1)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- e -->
                        <path id="letter-e1" d="M32 30 L32 10 L45 10 M32 20 L42 20 M32 30 L45 30" stroke="url(#gradient1)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- (空格) -->

                        <!-- a -->
                        <path id="letter-a1" d="M58 30 L62 10 L66 30 M60 22 L64 22" stroke="url(#gradient2)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- r -->
                        <path id="letter-r1" d="M74 30 L74 10 L84 10 Q88 10 88 16 Q88 20 84 20 L74 20 L84 30" stroke="url(#gradient2)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- e -->
                        <path id="letter-e2" d="M96 30 L96 10 L109 10 M96 20 L106 20 M96 30 L109 30" stroke="url(#gradient2)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- (空格) -->

                        <!-- o -->
                        <path id="letter-o1" d="M122 20 Q122 10 132 10 Q142 10 142 20 Q142 30 132 30 Q122 30 122 20" stroke="url(#gradient3)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- n -->
                        <path id="letter-n1" d="M150 30 L150 10 L150 20 L160 10 L160 30" stroke="url(#gradient3)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- (空格) -->

                        <!-- t -->
                        <path id="letter-t1" d="M168 15 L180 15 M174 15 L174 30" stroke="url(#gradient4)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- h -->
                        <path id="letter-h1" d="M188 8 L188 30 M188 20 L198 20 L198 8 L198 30" stroke="url(#gradient4)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- e -->
                        <path id="letter-e3" d="M206 30 L206 10 L219 10 M206 20 L216 20 M206 30 L219 30" stroke="url(#gradient4)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- (空格) -->

                        <!-- w -->
                        <path id="letter-w2" d="M232 10 L236 30 L240 15 L244 30 L248 10" stroke="url(#gradient5)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- a -->
                        <path id="letter-a2" d="M256 30 L260 10 L264 30 M258 22 L262 22" stroke="url(#gradient5)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        <!-- y -->
                        <path id="letter-y1" d="M272 10 L276 22 L280 10 M276 22 L276 32" stroke="url(#gradient5)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- 箭头 (从字母末尾指向爱心) -->
                        <path id="arrow" d="M290 20 Q310 18 330 20 M325 16 L330 20 L325 24" stroke="url(#gradientArrow)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- 装饰性心形 (右侧) -->
                        <path id="heart" d="M340 17 Q340 13 343 13 Q346 13 346 17 Q346 13 349 13 Q352 13 352 17 Q352 23 346 27 Q343 23 340 17" stroke="url(#gradientHeart)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- 渐变定义 -->
                        <defs>
                            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#fa709a;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#fee140;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradientHeart" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#ff8e8e;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradientArrow" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
            </div>
            <div class="login-form animate__animated animate__fadeIn animate__delay-1s">
                <form id="registerForm">
                    <div class="input-group">
                        <i class="input-icon" data-feather="user"></i>
                        <input type="text" id="username" placeholder="用户名" required>
                        <span class="input-focus-border"></span>
                    </div>
                    <div class="input-group">
                        <i class="input-icon" data-feather="mail"></i>
                        <input type="email" id="email" placeholder="电子邮箱" required>
                        <span class="input-focus-border"></span>
                    </div>
                    <div class="input-group">
                        <i class="input-icon" data-feather="lock"></i>
                        <input type="password" id="password" placeholder="密码" required>
                        <span class="input-focus-border"></span>
                    </div>
                    <div class="input-group">
                        <i class="input-icon" data-feather="lock"></i>
                        <input type="password" id="confirm_password" placeholder="确认密码" required>
                        <span class="input-focus-border"></span>
                    </div>

                    <!-- 验证码输入框 -->
                    <div class="input-group captcha-group">
                        <i class="input-icon" data-feather="shield"></i>
                        <input type="text" id="captcha" placeholder="请输入验证码" required maxlength="4">
                        <span class="input-focus-border"></span>
                        <div class="captcha-container">
                            <canvas id="captcha-canvas" width="90" height="36"></canvas>
                            <button type="button" class="captcha-refresh" title="刷新验证码">
                                <i data-feather="refresh-cw"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <div class="agreement">
                            <input type="checkbox" id="agreement" required>
                            <label for="agreement">我已阅读并同意服务条款</label>
                        </div>
                    </div>
                    <button type="submit" class="btn login-btn">
                        <span class="btn-text">创建账号</span>
                        <span class="btn-loader">
                            <span class="loader-dot"></span>
                            <span class="loader-dot"></span>
                            <span class="loader-dot"></span>
                        </span>
                    </button>
                </form>
                <div class="register-link">
                    已有账号？<a href="index.html">返回登录</a>
                </div>
                

            </div>
        </div>
    </div>



    </div> <!-- 关闭 windows-container -->



    <script src="https://unpkg.com/feather-icons"></script>
    <script src="libs/js/particles.min.js" defer></script>
    <script src="js/api-service.js"></script>
    <script src="js/auth-api.js"></script>
    <script src="js/global-error-handler.js"></script>
    <script src="js/loading-manager.js"></script>
    <script src="js/toast-system.js"></script>
    <script src="js/register-main.js"></script>


    <!-- 页面加载器控制脚本已完全移除 -->

    <script>
        // 快速加载优化
        (function() {
            // 禁用控制台输出以提高性能
            if (!window.location.href.includes('debug')) {
                console.log = console.warn = console.error = function() {};
            }
            
            // 预加载关键资源
            const criticalResources = [
                'js/api-service.js',
                'js/auth-guard.js',
                'js/global-error-handler.js'
            ];
            
            criticalResources.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource;
                link.as = 'script';
                document.head.appendChild(link);
            });
            
            // 快速DOM就绪检测
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initFastMode);
            } else {
                initFastMode();
            }
            
            function initFastMode() {
                // 移除加载动画
                const loaders = document.querySelectorAll('.page-loader, .loader, .loading');
                loaders.forEach(loader => {
                    loader.style.display = 'none';
                });
                
                // 显示主要内容
                document.body.style.visibility = 'visible';
                document.body.style.opacity = '1';
            }
        })();
    </script>
</body>
</html>
