/**
 * 🧪 API连接测试脚本
 * 在浏览器控制台中运行此脚本来测试API连接
 */

(async function testApiConnection() {
    console.log('🧪 开始API连接测试...');
    console.log('=' .repeat(50));
    
    // 1. 显示当前配置
    console.log('📋 当前API配置:');
    console.log('   BASE_URL:', window.API_CONFIG?.BASE_URL || '未设置');
    console.log('   DEMO_MODE:', window.API_CONFIG?.DEMO_MODE || '未设置');
    console.log('   ENABLE_CORS_FALLBACK:', window.API_CONFIG?.ENABLE_CORS_FALLBACK || '未设置');
    
    // 2. 测试基础连接
    const testURL = 'https://cugzcfwwhuiq.sealoshzh.site/v1';
    console.log('\n🌐 测试基础连接:', testURL);
    
    try {
        const response = await fetch(testURL, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            mode: 'cors'
        });
        
        console.log('✅ 基础连接状态:', response.status, response.statusText);
        
        if (response.ok) {
            try {
                const data = await response.json();
                console.log('✅ 服务器响应:', data);
            } catch (e) {
                console.log('✅ 服务器响应: (非JSON格式，但连接正常)');
            }
        }
        
    } catch (error) {
        console.error('❌ 基础连接失败:', error.name, error.message);
        
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
            console.error('💡 可能原因: CORS错误、网络问题或服务器未运行');
        }
    }
    
    // 3. 测试登录端点
    const loginURL = 'https://cugzcfwwhuiq.sealoshzh.site/v1/auth/login';
    console.log('\n🔐 测试登录端点:', loginURL);
    
    try {
        const response = await fetch(loginURL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                username: 'test',
                password: 'test'
            }),
            mode: 'cors'
        });
        
        console.log('✅ 登录端点状态:', response.status, response.statusText);
        
        if (response.status === 401) {
            console.log('✅ 登录端点正常 (401认证失败是预期的)');
        } else if (response.status === 400) {
            console.log('✅ 登录端点正常 (400请求错误是预期的)');
        } else if (response.status === 200) {
            console.log('✅ 登录端点正常 (意外的成功响应)');
        }
        
    } catch (error) {
        console.error('❌ 登录端点测试失败:', error.name, error.message);
    }
    
    // 4. 检查HttpClient实例
    console.log('\n🔧 检查HttpClient实例:');
    if (window.httpClient) {
        console.log('✅ httpClient存在');
        console.log('   baseURL:', window.httpClient.baseURL);
    } else {
        console.log('❌ httpClient不存在');
    }
    
    // 5. 提供修复建议
    console.log('\n💡 如果测试失败，请尝试:');
    console.log('1. 检查网络连接');
    console.log('2. 确认服务器地址正确');
    console.log('3. 联系管理员检查服务器状态');
    console.log('4. 运行修复脚本: 复制 fix-api-config.js 内容到控制台');
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎯 测试完成');
    
    return {
        timestamp: new Date().toISOString(),
        testURL: testURL,
        loginURL: loginURL
    };
})();
