/**
 * 加载状态管理模块
 */

/**
 * 加载管理器类
 */
class LoadingManager {
    constructor() {
        this.loadingStates = new Map();
        this.loadingOverlay = null;
        this.progressBar = null;
        this.loadingText = null;
        this.currentProgress = 0;
        
        this.init();
    }

    /**
     * 初始化加载管理器
     */
    init() {
        // 性能优化：预加载关键资源
        this.preloadCriticalResources();

        // 确保DOM已加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
            return;
        }

        // 确保body存在
        if (!document.body) {
            console.error('Loading管理器初始化失败：document.body不存在');
            return;
        }

        this.createLoadingOverlay();
        this.createProgressBar();

        // 优化页面加载
        this.optimizePageLoad();
    }

    /**
     * 预加载关键资源
     */
    preloadCriticalResources() {
        // 预加载关键CSS
        const criticalCSS = [
            'css/style.css',
            'css/toast-system.css',
            'libs/css/font-awesome.min.css'
        ];

        criticalCSS.forEach(href => {
            if (!document.querySelector(`link[href="${href}"]`)) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'style';
                link.href = href;
                document.head.appendChild(link);
            }
        });
    }

    /**
     * 优化页面加载
     */
    optimizePageLoad() {
        // 延迟加载非关键资源
        this.deferNonCriticalResources();

        // 优化图片加载
        this.optimizeImageLoading();

        // 减少重排和重绘
        this.optimizeRendering();
    }

    /**
     * 延迟加载非关键资源
     */
    deferNonCriticalResources() {
        // 延迟加载非关键脚本
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
            if (!script.hasAttribute('defer') && !script.hasAttribute('async')) {
                script.defer = true;
            }
        });
    }

    /**
     * 优化图片加载
     */
    optimizeImageLoading() {
        // 添加图片懒加载
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.loading = 'lazy';
            }
        });
    }

    /**
     * 优化渲染性能
     */
    optimizeRendering() {
        // 使用requestAnimationFrame优化动画
        if (window.requestAnimationFrame) {
            const optimizeFrame = () => {
                // 批量处理DOM操作
                this.batchDOMOperations();
                requestAnimationFrame(optimizeFrame);
            };
            requestAnimationFrame(optimizeFrame);
        }
    }

    /**
     * 批量处理DOM操作
     */
    batchDOMOperations() {
        // 减少重排和重绘的DOM操作
        const elements = document.querySelectorAll('[data-optimize]');
        if (elements.length > 0) {
            // 使用DocumentFragment批量操作
            const fragment = document.createDocumentFragment();
            elements.forEach(el => {
                if (el.parentNode) {
                    fragment.appendChild(el);
                }
            });
            if (fragment.children.length > 0) {
                document.body.appendChild(fragment);
            }
        }
    }

    /**
     * 创建加载遮罩层
     */
    createLoadingOverlay() {
        // 检查是否已存在
        if (document.getElementById('globalLoadingOverlay')) {
            this.loadingOverlay = document.getElementById('globalLoadingOverlay');
            return;
        }

        this.loadingOverlay = document.createElement('div');
        this.loadingOverlay.id = 'globalLoadingOverlay';
        this.loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(2px);
        `;

        const loadingContent = document.createElement('div');
        loadingContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            min-width: 300px;
        `;

        const spinner = document.createElement('div');
        spinner.style.cssText = `
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        `;

        this.loadingText = document.createElement('div');
        this.loadingText.style.cssText = `
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
        `;
        this.loadingText.textContent = '加载中...';

        loadingContent.appendChild(spinner);
        loadingContent.appendChild(this.loadingText);
        this.loadingOverlay.appendChild(loadingContent);

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(this.loadingOverlay);
    }

    /**
     * 创建进度条
     */
    createProgressBar() {
        // 检查是否已存在
        if (document.getElementById('globalProgressBar')) {
            this.progressBar = document.getElementById('globalProgressBar');
            return;
        }

        this.progressBar = document.createElement('div');
        this.progressBar.id = 'globalProgressBar';
        this.progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            z-index: 10000;
            transition: width 0.3s ease;
            display: none;
        `;

        document.body.appendChild(this.progressBar);
    }

    /**
     * 显示加载状态
     * @param {string} key - 加载状态键
     * @param {string} message - 加载消息
     * @param {boolean} showOverlay - 是否显示遮罩层
     */
    showLoading(key, message = '加载中...', showOverlay = false) {
        this.loadingStates.set(key, {
            message: message,
            showOverlay: showOverlay,
            startTime: Date.now()
        });

        if (showOverlay) {
            this.loadingText.textContent = message;
            this.loadingOverlay.style.display = 'flex';
        } else {
            this.showProgressBar();
        }

        this.updateLoadingState();
    }

    /**
     * 隐藏加载状态
     * @param {string} key - 加载状态键
     */
    hideLoading(key) {
        if (this.loadingStates.has(key)) {
            const loadingState = this.loadingStates.get(key);
            const duration = Date.now() - loadingState.startTime;
            
            // 记录加载时间统计
            if (window.StatsAPI) {
                window.StatsAPI.trackUserAction({
                    action: 'loading_completed',
                    feature: 'performance',
                    metadata: {
                        loadingKey: key,
                        duration: duration,
                        message: loadingState.message
                    }
                }).catch(error => console.warn('统计记录失败:', error));
            }

            this.loadingStates.delete(key);
        }

        this.updateLoadingState();
    }

    /**
     * 更新进度
     * @param {string} key - 加载状态键
     * @param {number} progress - 进度百分比 (0-100)
     * @param {string} message - 进度消息
     */
    updateProgress(key, progress, message) {
        if (this.loadingStates.has(key)) {
            const loadingState = this.loadingStates.get(key);
            loadingState.progress = progress;
            if (message) {
                loadingState.message = message;
            }

            this.currentProgress = Math.max(this.currentProgress, progress);
            this.progressBar.style.width = `${this.currentProgress}%`;

            if (loadingState.showOverlay) {
                this.loadingText.textContent = `${message} (${progress}%)`;
            }
        }
    }

    /**
     * 更新加载状态
     */
    updateLoadingState() {
        const hasOverlayLoading = Array.from(this.loadingStates.values())
            .some(state => state.showOverlay);

        if (hasOverlayLoading) {
            // 显示遮罩层
            this.loadingOverlay.style.display = 'flex';
            this.hideProgressBar();
        } else if (this.loadingStates.size > 0) {
            // 显示进度条
            this.loadingOverlay.style.display = 'none';
            this.showProgressBar();
        } else {
            // 隐藏所有加载状态
            this.loadingOverlay.style.display = 'none';
            this.hideProgressBar();
        }
    }

    /**
     * 显示进度条
     */
    showProgressBar() {
        this.progressBar.style.display = 'block';
        this.currentProgress = 0;
        this.progressBar.style.width = '0%';
        
        // 模拟进度增长
        this.animateProgress();
    }

    /**
     * 隐藏进度条
     */
    hideProgressBar() {
        this.progressBar.style.width = '100%';
        setTimeout(() => {
            this.progressBar.style.display = 'none';
            this.currentProgress = 0;
        }, 300);
    }

    /**
     * 动画进度条
     */
    animateProgress() {
        if (this.loadingStates.size === 0) {
            return;
        }

        const increment = Math.random() * 10 + 5;
        this.currentProgress = Math.min(this.currentProgress + increment, 90);
        this.progressBar.style.width = `${this.currentProgress}%`;

        if (this.currentProgress < 90) {
            setTimeout(() => this.animateProgress(), 200 + Math.random() * 300);
        }
    }

    /**
     * 显示按钮加载状态
     * @param {HTMLElement} button - 按钮元素
     * @param {string} loadingText - 加载文本
     */
    showButtonLoading(button, loadingText = '处理中...') {
        if (!button) return;

        button.dataset.originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            ${loadingText}
        `;
        button.style.opacity = '0.7';
    }

    /**
     * 隐藏按钮加载状态
     * @param {HTMLElement} button - 按钮元素
     */
    hideButtonLoading(button) {
        if (!button) return;

        button.disabled = false;
        button.innerHTML = button.dataset.originalText || button.innerHTML;
        button.style.opacity = '1';
        delete button.dataset.originalText;
    }

    /**
     * 显示表单加载状态
     * @param {HTMLElement} form - 表单元素
     * @param {string} message - 加载消息
     */
    showFormLoading(form, message = '提交中...') {
        if (!form) return;

        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => {
            input.disabled = true;
        });

        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
            this.showButtonLoading(submitButton, message);
        }

        form.style.opacity = '0.7';
    }

    /**
     * 隐藏表单加载状态
     * @param {HTMLElement} form - 表单元素
     */
    hideFormLoading(form) {
        if (!form) return;

        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => {
            input.disabled = false;
        });

        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
            this.hideButtonLoading(submitButton);
        }

        form.style.opacity = '1';
    }

    /**
     * 创建内联加载指示器
     * @param {HTMLElement} container - 容器元素
     * @param {string} message - 加载消息
     * @returns {HTMLElement} 加载指示器元素
     */
    createInlineLoader(container, message = '加载中...') {
        const loader = document.createElement('div');
        loader.className = 'inline-loader';
        loader.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #666;
        `;

        loader.innerHTML = `
            <div style="
                width: 20px;
                height: 20px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-right: 10px;
            "></div>
            ${message}
        `;

        container.appendChild(loader);
        return loader;
    }

    /**
     * 移除内联加载指示器
     * @param {HTMLElement} loader - 加载指示器元素
     */
    removeInlineLoader(loader) {
        if (loader && loader.parentNode) {
            loader.parentNode.removeChild(loader);
        }
    }

    /**
     * 获取当前加载状态
     * @returns {Object} 加载状态信息
     */
    getLoadingStatus() {
        return {
            activeLoaders: this.loadingStates.size,
            states: Array.from(this.loadingStates.entries()).map(([key, state]) => ({
                key: key,
                message: state.message,
                showOverlay: state.showOverlay,
                duration: Date.now() - state.startTime
            }))
        };
    }

    /**
     * 清除所有加载状态
     */
    clearAllLoading() {
        this.loadingStates.clear();
        this.updateLoadingState();
    }
}

// 创建全局加载管理器实例
if (typeof window !== 'undefined') {
    window.LoadingManager = LoadingManager;
    window.loadingManager = new LoadingManager();
}
