@echo off
echo 📦 下载剩余依赖文件...
echo ================================================

:: 创建目录
if not exist "libs\js" mkdir "libs\js"
if not exist "libs\css" mkdir "libs\css"

:: 使用PowerShell下载文件
echo 📥 下载 Chart.js 插件...
powershell -Command "try { Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js' -OutFile 'libs\js\chartjs-plugin-zoom.min.js'; Write-Host '✅ Chart.js插件下载完成' } catch { Write-Host '❌ Chart.js插件下载失败:' $_.Exception.Message }"

echo 📥 下载 XLSX 库...
powershell -Command "try { Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js' -OutFile 'libs\js\xlsx.full.min.js'; Write-Host '✅ XLSX库下载完成' } catch { Write-Host '❌ XLSX库下载失败:' $_.Exception.Message }"

echo 📥 下载 Particles.js...
powershell -Command "try { Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js' -OutFile 'libs\js\particles.min.js'; Write-Host '✅ Particles.js下载完成' } catch { Write-Host '❌ Particles.js下载失败:' $_.Exception.Message }"

echo 📥 下载 Font Awesome...
powershell -Command "try { Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' -OutFile 'libs\css\font-awesome.min.css'; Write-Host '✅ Font Awesome下载完成' } catch { Write-Host '❌ Font Awesome下载失败:' $_.Exception.Message }"

echo 📥 下载 Animate.css...
powershell -Command "try { Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css' -OutFile 'libs\css\animate.min.css'; Write-Host '✅ Animate.css下载完成' } catch { Write-Host '❌ Animate.css下载失败:' $_.Exception.Message }"

echo.
echo ✅ 下载完成！
echo 📁 文件保存在 libs\ 目录中
echo 🔍 打开 dependency-status.html 检查依赖状态
echo.
pause
