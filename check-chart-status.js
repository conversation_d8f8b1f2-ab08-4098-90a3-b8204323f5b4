/**
 * 📊 Chart.js状态检测脚本
 * 在浏览器控制台中运行此脚本来检查Chart.js状态
 */

(function checkChartStatus() {
    console.log('📊 Chart.js状态检测开始...');
    console.log('=' .repeat(50));
    
    // 1. 检查Chart.js是否已加载
    console.log('🔍 检查Chart.js加载状态:');
    if (typeof Chart !== 'undefined') {
        console.log('✅ Chart.js已加载');
        console.log('📋 版本信息:', Chart.version || '未知版本');
        
        // 检查Chart.js功能
        try {
            console.log('🧪 测试Chart.js基础功能...');
            
            // 创建一个临时canvas测试
            const testCanvas = document.createElement('canvas');
            testCanvas.width = 100;
            testCanvas.height = 100;
            const testCtx = testCanvas.getContext('2d');
            
            const testChart = new Chart(testCtx, {
                type: 'line',
                data: {
                    labels: ['1', '2'],
                    datasets: [{
                        label: 'Test',
                        data: [1, 2]
                    }]
                },
                options: {
                    responsive: false,
                    animation: false
                }
            });
            
            testChart.destroy();
            console.log('✅ Chart.js功能测试通过');
            
        } catch (error) {
            console.error('❌ Chart.js功能测试失败:', error.message);
        }
        
    } else {
        console.error('❌ Chart.js未加载');
        
        // 检查是否有加载脚本
        const chartScripts = Array.from(document.scripts).filter(script => 
            script.src.includes('chart') || script.src.includes('Chart')
        );
        
        if (chartScripts.length > 0) {
            console.log('🔍 发现Chart.js脚本标签:');
            chartScripts.forEach((script, index) => {
                console.log(`   ${index + 1}. ${script.src}`);
            });
        } else {
            console.log('❌ 未发现Chart.js脚本标签');
        }
    }
    
    // 2. 检查页面中的图表画布
    console.log('\n🎨 检查图表画布元素:');
    const canvasElements = [
        'timeChartCanvas',
        'frequencyChartCanvas', 
        'barChartCanvas',
        'envelopeChartCanvas'
    ];
    
    canvasElements.forEach(canvasId => {
        const canvas = document.getElementById(canvasId);
        if (canvas) {
            console.log(`✅ ${canvasId}: 存在`);
        } else {
            console.log(`❌ ${canvasId}: 不存在`);
        }
    });
    
    // 3. 检查全局charts对象
    console.log('\n📈 检查图表实例:');
    if (typeof charts !== 'undefined') {
        console.log('✅ charts对象存在');
        const chartTypes = ['timeChart', 'frequencyChart', 'barChart', 'envelopeChart'];
        chartTypes.forEach(chartType => {
            if (charts[chartType]) {
                console.log(`✅ ${chartType}: 已创建`);
            } else {
                console.log(`⚪ ${chartType}: 未创建`);
            }
        });
    } else {
        console.log('❌ charts对象不存在');
    }
    
    // 4. 网络连接测试
    console.log('\n🌐 测试CDN连接:');
    const cdnUrls = [
        'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js',
        'https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js',
        'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js'
    ];
    
    cdnUrls.forEach((url, index) => {
        fetch(url, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    console.log(`✅ CDN ${index + 1}: 可访问 (${url})`);
                } else {
                    console.log(`❌ CDN ${index + 1}: 不可访问 (${response.status}) (${url})`);
                }
            })
            .catch(error => {
                console.log(`❌ CDN ${index + 1}: 连接失败 (${url})`);
            });
    });
    
    // 5. 提供修复建议
    console.log('\n💡 修复建议:');
    
    if (typeof Chart === 'undefined') {
        console.log('🔧 Chart.js未加载的解决方案:');
        console.log('   1. 刷新页面重新加载');
        console.log('   2. 检查网络连接');
        console.log('   3. 尝试手动加载:');
        console.log('      const script = document.createElement("script");');
        console.log('      script.src = "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js";');
        console.log('      script.onload = () => console.log("Chart.js手动加载成功");');
        console.log('      document.head.appendChild(script);');
    } else {
        console.log('✅ Chart.js状态正常，可以正常使用图表功能');
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎯 检测完成');
    
    return {
        chartLoaded: typeof Chart !== 'undefined',
        chartVersion: typeof Chart !== 'undefined' ? (Chart.version || 'unknown') : null,
        timestamp: new Date().toISOString()
    };
})();
