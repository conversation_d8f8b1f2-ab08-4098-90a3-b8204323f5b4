/**
 * 串口调试相关API接口
 */

/**
 * 串口调试API服务类
 */
class SerialAPI {
    constructor(httpClient) {
        this.client = httpClient;
        this.websocket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 1;
        this.reconnectDelay = 1000;
    }

    /**
     * 获取可用串口列表
     * @returns {Promise<Object>} 串口列表
     */
    async getAvailablePorts() {
        try {
            // 使用快速超时请求
            const response = await this.client.fastRequest('/serial/ports', {
                method: 'GET'
            });

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data.ports || []
                };
            }

            throw new APIError('获取串口列表失败', 400);
        } catch (error) {
            if (error.code === 'FAST_TIMEOUT') {
                console.warn('API响应超时(>500ms)，串口列表不可用:', error.message);
                // 返回空列表，让用户手动输入串口
                return {
                    success: true,
                    data: { ports: [] },
                    message: '串口列表获取超时，请手动输入串口名称',
                    source: 'local'
                };
            }

            console.error('获取串口列表失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError('获取串口列表失败', 0);
        }
    }

    /**
     * 连接串口
     * @param {Object} config - 串口配置
     * @returns {Promise<Object>} 连接结果
     */
    async connectPort(config) {
        try {
            const requestData = {
                port: config.port,
                baudRate: config.baudRate || 9600,
                dataBits: config.dataBits || 8,
                stopBits: config.stopBits || 1,
                parity: config.parity || 'none',
                flowControl: config.flowControl || 'none'
            };

            // 使用快速超时请求
            const response = await this.client.fastRequest('/serial/connect', {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '串口连接成功',
                    data: {
                        sessionId: response.data.sessionId,
                        config: response.data.config
                    }
                };
            }

            throw new APIError('串口连接失败', 400);
        } catch (error) {
            if (error.code === 'FAST_TIMEOUT') {
                console.warn('API响应超时(>500ms)，串口连接不可用:', error.message);
                return {
                    success: false,
                    useLocal: true,
                    message: '串口连接超时，请使用本地串口功能',
                    error: error
                };
            }

            console.error('串口连接失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError('串口连接失败', 0);
        }
    }

    /**
     * 断开串口连接
     * @param {string} sessionId - 会话ID
     * @returns {Promise<Object>} 断开结果
     */
    async disconnectPort(sessionId) {
        try {
            const response = await this.client.post('/serial/disconnect', { sessionId });

            if (response.success) {
                return {
                    success: true,
                    message: '串口断开成功'
                };
            }

            throw new APIError('串口断开失败', 400);
        } catch (error) {
            console.error('串口断开失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('串口断开失败', 0);
        }
    }

    /**
     * 发送数据到串口
     * @param {string} sessionId - 会话ID
     * @param {string} data - 要发送的数据
     * @param {Object} options - 发送选项
     * @returns {Promise<Object>} 发送结果
     */
    async sendData(sessionId, data, options = {}) {
        try {
            const requestData = {
                sessionId: sessionId,
                data: data,
                format: options.format || 'text',
                encoding: options.encoding || 'utf8',
                addNewline: options.addNewline !== false
            };

            const response = await this.client.post('/serial/send', requestData);

            if (response.success) {
                return {
                    success: true,
                    message: '数据发送成功',
                    data: response.data
                };
            }

            throw new APIError('数据发送失败', 400);
        } catch (error) {
            console.error('数据发送失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('数据发送失败', 0);
        }
    }

    /**
     * 获取串口数据记录
     * @param {string} sessionId - 会话ID
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 数据记录
     */
    async getDataRecords(sessionId, params = {}) {
        try {
            const queryParams = {
                sessionId: sessionId,
                page: params.page || 1,
                limit: params.limit || 100,
                startTime: params.startTime,
                endTime: params.endTime,
                direction: params.direction // 'send' | 'receive' | 'all'
            };

            const response = await this.client.get('/serial/records', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data
                };
            }

            throw new APIError('获取数据记录失败', 400);
        } catch (error) {
            console.error('获取数据记录失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取数据记录失败', 0);
        }
    }

    /**
     * 保存串口配置（优先本地保存）
     * @param {Object} config - 串口配置
     * @param {string} name - 配置名称
     * @returns {Promise<Object>} 保存结果
     */
    async saveConfig(config, name) {
        try {
            // 优先本地保存
            const configData = {
                configName: name,
                baudRate: config.baudRate,
                dataBits: config.dataBits,
                stopBits: config.stopBits,
                parity: config.parity,
                flowControl: config.flowControl || 'none',
                description: config.description || ''
            };

            const savedConfig = this.addLocalConfig(configData);

            // 可选：异步同步到服务器
            this.syncConfigToServer(configData).catch(error => {
                console.warn('服务器同步失败，配置已保存到本地:', error);
            });

            return {
                success: true,
                message: '配置保存成功',
                data: {
                    configId: savedConfig.configId,
                    name: savedConfig.configName
                }
            };
        } catch (error) {
            console.error('配置保存失败:', error);
            throw new APIError('配置保存失败: ' + error.message, 0);
        }
    }

    /**
     * 异步同步到服务器（可选）
     * @param {Object} configData - 配置数据
     * @returns {Promise<Object>} 同步结果
     */
    async syncConfigToServer(configData) {
        try {
            const requestData = {
                name: configData.configName,
                config: configData,
                description: configData.description || ''
            };

            const response = await this.client.post('/serial/configs', requestData);
            console.log('配置已同步到服务器:', response);
            return response;
        } catch (error) {
            console.warn('服务器同步失败:', error);
            throw error;
        }
    }

    /**
     * 获取保存的配置列表（优先本地存储）
     * @returns {Promise<Object>} 配置列表
     */
    async getSavedConfigs() {
        try {
            // 优先使用本地存储，避免网络请求
            const localConfigs = this.getLocalConfigs();
            if (localConfigs.length > 0) {
                return {
                    success: true,
                    data: localConfigs
                };
            }

            // 如果本地没有配置，尝试从服务器获取（可选）
            try {
                const response = await this.client.get('/serial/configs');
                const serverConfigs = response.data?.configs || [];

                // 将服务器配置同步到本地
                if (serverConfigs.length > 0) {
                    this.saveLocalConfigs(serverConfigs);
                }

                return {
                    success: true,
                    data: serverConfigs
                };
            } catch (serverError) {
                console.warn('服务器配置获取失败，使用本地配置:', serverError);
                return {
                    success: true,
                    data: localConfigs
                };
            }
        } catch (error) {
            console.error('获取配置列表失败:', error);
            return {
                success: true,
                data: this.getLocalConfigs() // 返回本地配置作为备选
            };
        }
    }

    /**
     * 删除保存的配置（优先本地删除）
     * @param {string} configId - 配置ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteConfig(configId) {
        try {
            // 优先删除本地配置
            this.deleteLocalConfig(configId);

            // 可选：异步删除服务器配置
            if (!configId.startsWith('local_')) {
                this.deleteServerConfig(configId).catch(error => {
                    console.warn('服务器配置删除失败，本地配置已删除:', error);
                });
            }

            return {
                success: true,
                message: '配置删除成功'
            };
        } catch (error) {
            console.error('配置删除失败:', error);
            throw new APIError('配置删除失败: ' + error.message, 0);
        }
    }

    /**
     * 删除服务器配置（可选）
     * @param {string} configId - 配置ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteServerConfig(configId) {
        try {
            const response = await this.client.delete(`/serial/configs/${configId}`);
            console.log('服务器配置已删除:', response);
            return response;
        } catch (error) {
            console.warn('服务器配置删除失败:', error);
            throw error;
        }
    }

    /**
     * 获取会话历史
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 会话历史
     */
    async getSessionHistory(params = {}) {
        try {
            const queryParams = {
                page: params.page || 1,
                limit: params.limit || 20,
                startDate: params.startDate,
                endDate: params.endDate,
                port: params.port
            };

            const response = await this.client.get('/serial/sessions', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data
                };
            }

            throw new APIError('获取会话历史失败', 400);
        } catch (error) {
            console.error('获取会话历史失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError('获取会话历史失败', 0);
        }
    }

    /**
     * 建立WebSocket连接用于实时数据传输
     * @param {string} sessionId - 会话ID
     * @param {Object} callbacks - 回调函数
     * @returns {Promise<void>}
     */
    async connectWebSocket(sessionId, callbacks = {}) {
        try {
            const token = this.client.tokenManager?.getToken();
            if (!token) {
                throw new Error('未找到认证令牌');
            }

            const wsUrl = `${this.client.baseURL.replace('http', 'ws')}/serial/ws/${sessionId}?token=${token}`;
            
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                if (callbacks.onOpen) {
                    callbacks.onOpen();
                }
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    
                    if (callbacks.onMessage) {
                        callbacks.onMessage(data);
                    }
                } catch (error) {
                    console.error('WebSocket消息解析失败:', error);
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.isConnected = false;
                
                if (callbacks.onClose) {
                    callbacks.onClose();
                }

                // 自动重连
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    setTimeout(() => {
                        this.reconnectAttempts++;
                        console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                        this.connectWebSocket(sessionId, callbacks);
                    }, this.reconnectDelay * this.reconnectAttempts);
                }
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                
                if (callbacks.onError) {
                    callbacks.onError(error);
                }
            };

        } catch (error) {
            console.error('WebSocket连接失败:', error);
            throw error;
        }
    }

    /**
     * 关闭WebSocket连接
     */
    disconnectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
            this.isConnected = false;
        }
    }

    /**
     * 通过WebSocket发送数据
     * @param {Object} data - 要发送的数据
     */
    sendWebSocketData(data) {
        if (this.websocket && this.isConnected) {
            this.websocket.send(JSON.stringify(data));
        } else {
            throw new Error('WebSocket未连接');
        }
    }

    /**
     * 验证串口配置
     * @param {Object} config - 串口配置
     * @returns {Object} 验证结果
     */
    validateConfig(config) {
        const errors = [];

        if (!config.port) {
            errors.push('串口号不能为空');
        }

        const validBaudRates = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600];
        if (!validBaudRates.includes(parseInt(config.baudRate))) {
            errors.push('无效的波特率');
        }

        const validDataBits = [5, 6, 7, 8];
        if (!validDataBits.includes(parseInt(config.dataBits))) {
            errors.push('无效的数据位');
        }

        const validStopBits = [1, 1.5, 2];
        if (!validStopBits.includes(parseFloat(config.stopBits))) {
            errors.push('无效的停止位');
        }

        const validParity = ['none', 'odd', 'even', 'mark', 'space'];
        if (!validParity.includes(config.parity)) {
            errors.push('无效的校验位');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 格式化数据显示
     * @param {string} data - 原始数据
     * @param {string} format - 显示格式
     * @returns {string} 格式化后的数据
     */
    formatData(data, format = 'text') {
        switch (format) {
            case 'hex':
                return data.split('').map(char => 
                    char.charCodeAt(0).toString(16).padStart(2, '0').toUpperCase()
                ).join(' ');
            
            case 'binary':
                return data.split('').map(char => 
                    char.charCodeAt(0).toString(2).padStart(8, '0')
                ).join(' ');
            
            case 'decimal':
                return data.split('').map(char => 
                    char.charCodeAt(0).toString()
                ).join(' ');
            
            case 'text':
            default:
                return data;
        }
    }

    /**
     * 获取本地配置
     * @returns {Array} 本地配置列表
     */
    getLocalConfigs() {
        try {
            const configs = localStorage.getItem('serial_configs');
            return configs ? JSON.parse(configs) : [];
        } catch (error) {
            console.error('读取本地配置失败:', error);
            return [];
        }
    }

    /**
     * 保存本地配置
     * @param {Array} configs - 配置列表
     */
    saveLocalConfigs(configs) {
        try {
            localStorage.setItem('serial_configs', JSON.stringify(configs));
        } catch (error) {
            console.error('保存本地配置失败:', error);
        }
    }

    /**
     * 添加新配置到本地
     * @param {Object} config - 配置对象
     * @returns {Object} 新配置
     */
    addLocalConfig(config) {
        const configs = this.getLocalConfigs();
        const newConfig = {
            configId: 'local_' + Date.now(),
            configName: config.configName,
            baudRate: config.baudRate,
            dataBits: config.dataBits,
            stopBits: config.stopBits,
            parity: config.parity,
            flowControl: config.flowControl || 'none',
            createdAt: new Date().toISOString()
        };

        configs.unshift(newConfig); // 添加到开头
        this.saveLocalConfigs(configs);
        return newConfig;
    }

    /**
     * 删除本地配置
     * @param {string} configId - 配置ID
     * @returns {Array} 更新后的配置列表
     */
    deleteLocalConfig(configId) {
        const configs = this.getLocalConfigs();
        const filteredConfigs = configs.filter(config => config.configId !== configId);
        this.saveLocalConfigs(filteredConfigs);
        return filteredConfigs;
    }

    /**
     * 更新本地配置
     * @param {string} configId - 配置ID
     * @param {Object} updateData - 更新数据
     * @returns {Object|null} 更新后的配置
     */
    updateLocalConfig(configId, updateData) {
        const configs = this.getLocalConfigs();
        const configIndex = configs.findIndex(config => config.configId === configId);

        if (configIndex === -1) {
            return null;
        }

        configs[configIndex] = {
            ...configs[configIndex],
            ...updateData,
            updatedAt: new Date().toISOString()
        };

        this.saveLocalConfigs(configs);
        return configs[configIndex];
    }

}

// 创建串口API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.SerialAPI = new SerialAPI(window.APIService.client);
}
