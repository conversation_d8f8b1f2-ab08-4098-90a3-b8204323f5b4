/**
 * 📦 本地依赖设置脚本
 * 设置项目使用本地库文件而不是CDN
 */

const fs = require('fs');
const path = require('path');

// 检查并创建必要的目录
function ensureDirectories() {
    const dirs = [
        'libs',
        'libs/js',
        'libs/css',
        'libs/fonts'
    ];
    
    dirs.forEach(dir => {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`✅ 创建目录: ${dir}`);
        }
    });
}

// 复制node_modules中的文件
function copyFromNodeModules() {
    const copies = [
        {
            from: 'node_modules/chart.js/dist/chart.umd.js',
            to: 'libs/js/chart.umd.js'
        },
        {
            from: 'node_modules/chart.js/dist/chart.umd.min.js',
            to: 'libs/js/chart.umd.min.js'
        }
    ];
    
    copies.forEach(({ from, to }) => {
        if (fs.existsSync(from)) {
            fs.copyFileSync(from, to);
            console.log(`✅ 复制: ${from} -> ${to}`);
        } else {
            console.log(`⚠️ 源文件不存在: ${from}`);
        }
    });
}

// 创建本地版本的HTML文件
function createLocalVersions() {
    const htmlFiles = [
        'data.html',
        'serial-debug.html',
        'index.html',
        'register.html',
        'feedback.html',
        'signal-denoise.html'
    ];
    
    htmlFiles.forEach(filename => {
        if (fs.existsSync(filename)) {
            createLocalVersion(filename);
        }
    });
}

// 为单个HTML文件创建本地版本
function createLocalVersion(filename) {
    const content = fs.readFileSync(filename, 'utf8');
    
    // 替换CDN链接为本地链接
    let localContent = content
        // Chart.js CDN替换
        .replace(/https:\/\/[^"']*chart\.js[^"']*/g, 'libs/js/chart.umd.js')
        .replace(/https:\/\/[^"']*Chart\.js[^"']*/g, 'libs/js/chart.umd.js')
        .replace(/https:\/\/[^"']*chartjs-plugin-zoom[^"']*/g, 'libs/js/chartjs-plugin-zoom.min.js')
        
        // XLSX CDN替换
        .replace(/https:\/\/[^"']*xlsx[^"']*/g, 'libs/js/xlsx.full.min.js')
        
        // Particles.js CDN替换
        .replace(/https:\/\/[^"']*particles[^"']*/g, 'libs/js/particles.min.js')
        
        // Font Awesome CDN替换
        .replace(/https:\/\/[^"']*font-awesome[^"']*/g, 'libs/css/font-awesome.min.css')
        
        // Animate.css CDN替换
        .replace(/https:\/\/[^"']*animate\.css[^"']*/g, 'libs/css/animate.min.css');
    
    // 移除Chart.js多CDN加载脚本，因为我们使用本地文件
    localContent = localContent.replace(
        /<!-- Chart\.js库 - 多CDN备用加载 -->[\s\S]*?<\/script>/,
        '<!-- Chart.js库 - 本地版本 -->\n    <script src="libs/js/chart.umd.js"></script>'
    );
    
    const localFilename = filename.replace('.html', '-local.html');
    fs.writeFileSync(localFilename, localContent);
    console.log(`✅ 创建本地版本: ${localFilename}`);
}

// 创建依赖状态检查文件
function createDependencyStatus() {
    const statusHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>依赖状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .ok { background: #d4edda; color: #155724; }
        .missing { background: #f8d7da; color: #721c24; }
        .test-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <h1>📦 依赖状态检查</h1>
    <div id="status"></div>
    <button class="test-btn" onclick="testDependencies()">测试依赖</button>
    <button class="test-btn" onclick="testChart()">测试Chart.js</button>
    
    <script src="libs/js/chart.umd.js"></script>
    <script>
        function checkFile(path) {
            return fetch(path, { method: 'HEAD' })
                .then(response => response.ok)
                .catch(() => false);
        }
        
        async function testDependencies() {
            const dependencies = [
                'libs/js/chart.umd.js',
                'libs/js/chartjs-plugin-zoom.min.js',
                'libs/js/xlsx.full.min.js',
                'libs/js/particles.min.js',
                'libs/css/font-awesome.min.css',
                'libs/css/animate.min.css'
            ];
            
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<h3>检查中...</h3>';
            
            let html = '<h3>依赖状态:</h3>';
            
            for (const dep of dependencies) {
                const exists = await checkFile(dep);
                const status = exists ? 'ok' : 'missing';
                const icon = exists ? '✅' : '❌';
                html += \`<div class="status \${status}">\${icon} \${dep}</div>\`;
            }
            
            statusDiv.innerHTML = html;
        }
        
        function testChart() {
            if (typeof Chart !== 'undefined') {
                alert('✅ Chart.js 加载成功！版本: ' + (Chart.version || '未知'));
            } else {
                alert('❌ Chart.js 未加载');
            }
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', testDependencies);
    </script>
</body>
</html>`;
    
    fs.writeFileSync('dependency-status.html', statusHtml);
    console.log('✅ 创建依赖状态检查页面: dependency-status.html');
}

// 创建依赖下载清单
function createDownloadList() {
    const downloadList = `# 📦 依赖下载清单

## 需要手动下载的文件：

### JavaScript库
1. **Chart.js插件** (已有Chart.js主库)
   - URL: https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js
   - 保存为: libs/js/chartjs-plugin-zoom.min.js

2. **XLSX (Excel处理)**
   - URL: https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js
   - 保存为: libs/js/xlsx.full.min.js

3. **Particles.js (粒子动画)**
   - URL: https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js
   - 保存为: libs/js/particles.min.js

### CSS库
1. **Font Awesome (图标)**
   - URL: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css
   - 保存为: libs/css/font-awesome.min.css

2. **Animate.css (动画)**
   - URL: https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css
   - 保存为: libs/css/animate.min.css

## 使用方法：
1. 运行此脚本: \`node setup-local-libs.js\`
2. 手动下载上述文件到对应目录
3. 使用生成的 *-local.html 文件替代原文件
4. 打开 dependency-status.html 检查依赖状态

## 注意事项：
- Google字体仍使用CDN，如需本地化请单独处理
- 某些CDN可能需要翻墙访问
- 建议保留原始HTML文件作为备份
`;
    
    fs.writeFileSync('DOWNLOAD_LIST.md', downloadList);
    console.log('✅ 创建下载清单: DOWNLOAD_LIST.md');
}

// 主函数
function main() {
    console.log('🚀 设置本地依赖...');
    console.log('=' .repeat(50));
    
    ensureDirectories();
    copyFromNodeModules();
    createLocalVersions();
    createDependencyStatus();
    createDownloadList();
    
    console.log('\n✅ 本地依赖设置完成！');
    console.log('📝 请查看 DOWNLOAD_LIST.md 了解需要手动下载的文件');
    console.log('🔍 打开 dependency-status.html 检查依赖状态');
    console.log('📄 使用 *-local.html 文件进行本地开发');
}

if (require.main === module) {
    main();
}

module.exports = { main, createLocalVersion, ensureDirectories };
