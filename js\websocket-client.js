/**
 * WebSocket实时通信客户端 - 已禁用
 * WebSocket功能已禁用 - 服务器不支持WebSocket连接
 */

/**
 * WebSocket客户端类 - 禁用版本
 */
class WebSocketClient {
    constructor(baseUrl = 'wss://cugzcfwwhuiq.sealoshzh.site') {
        console.warn('WebSocket功能已禁用 - 服务器暂不支持实时通信');
        this.baseUrl = baseUrl;
        this.socket = null;
        this.isConnected = false;
        this.messageHandlers = new Map();
    }

    /**
     * 连接WebSocket - 已禁用
     * @param {string} token - 认证令牌
     * @returns {Promise<boolean>} 连接结果
     */
    async connect(token = null) {
        console.warn('WebSocket连接已禁用');
        return Promise.resolve(false);
    }

    /**
     * 断开连接 - 已禁用
     */
    disconnect() {
        console.warn('WebSocket连接已禁用');
    }

    /**
     * 发送消息 - 已禁用
     * @param {Object} message - 消息对象
     * @returns {boolean} 发送结果
     */
    send(message) {
        console.warn('WebSocket发送已禁用');
        return false;
    }

    /**
     * 订阅消息类型 - 已禁用
     * @param {string} type - 消息类型
     * @param {Function} handler - 处理函数
     */
    subscribe(type, handler) {
        console.warn('WebSocket事件监听已禁用');
    }

    /**
     * 取消订阅 - 已禁用
     * @param {string} type - 消息类型
     * @param {Function} handler - 处理函数
     */
    unsubscribe(type, handler) {
        console.warn('WebSocket事件监听已禁用');
    }

    /**
     * 添加事件监听器 - 已禁用
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        console.warn('WebSocket事件监听已禁用');
    }

    /**
     * 移除事件监听器 - 已禁用
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        console.warn('WebSocket事件监听已禁用');
    }

    /**
     * 获取连接状态 - 已禁用
     * @returns {boolean} 连接状态
     */
    getConnectionStatus() {
        return false;
    }

    /**
     * 检查是否连接 - 已禁用
     * @returns {boolean} 连接状态
     */
    isConnected() {
        return false;
    }

}

// 创建全局WebSocket客户端实例 - 禁用版本
if (typeof window !== 'undefined') {
    window.WebSocketClient = WebSocketClient;

    // 创建默认实例（禁用版本）
    window.wsClient = new WebSocketClient();

    console.log('WebSocket功能已禁用 - 实时通信功能暂时不可用');
}
