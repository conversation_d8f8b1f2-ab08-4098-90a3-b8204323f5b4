<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面优化测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>页面优化测试</h1>
    
    <div class="test-section">
        <h2>本地库加载测试</h2>
        <button onclick="testLibraries()">测试库加载</button>
        <div id="libraryResults"></div>
    </div>
    
    <div class="test-section">
        <h2>页面性能测试</h2>
        <button onclick="testPerformance()">测试性能</button>
        <div id="performanceResults"></div>
    </div>
    
    <div class="test-section">
        <h2>页面链接测试</h2>
        <button onclick="testPageLinks()">测试页面链接</button>
        <div id="linkResults"></div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function testLibraries() {
            const container = document.getElementById('libraryResults');
            container.innerHTML = '';
            
            // 测试Chart.js
            const chartScript = document.createElement('script');
            chartScript.src = 'libs/js/chart.min.js';
            chartScript.onload = () => {
                if (typeof Chart !== 'undefined') {
                    addResult('libraryResults', '✅ Chart.js 加载成功', 'success');
                } else {
                    addResult('libraryResults', '❌ Chart.js 加载失败', 'error');
                }
            };
            chartScript.onerror = () => {
                addResult('libraryResults', '❌ Chart.js 文件不存在', 'error');
            };
            document.head.appendChild(chartScript);
            
            // 测试XLSX
            const xlsxScript = document.createElement('script');
            xlsxScript.src = 'libs/js/xlsx.full.min.js';
            xlsxScript.onload = () => {
                if (typeof XLSX !== 'undefined') {
                    addResult('libraryResults', '✅ XLSX 加载成功', 'success');
                } else {
                    addResult('libraryResults', '❌ XLSX 加载失败', 'error');
                }
            };
            xlsxScript.onerror = () => {
                addResult('libraryResults', '❌ XLSX 文件不存在', 'error');
            };
            document.head.appendChild(xlsxScript);
            
            // 测试Font Awesome
            const fontLink = document.createElement('link');
            fontLink.rel = 'stylesheet';
            fontLink.href = 'libs/css/font-awesome.min.css';
            fontLink.onload = () => {
                addResult('libraryResults', '✅ Font Awesome CSS 加载成功', 'success');
            };
            fontLink.onerror = () => {
                addResult('libraryResults', '❌ Font Awesome CSS 文件不存在', 'error');
            };
            document.head.appendChild(fontLink);
        }

        function testPerformance() {
            const container = document.getElementById('performanceResults');
            container.innerHTML = '';
            
            // 测试页面加载时间
            const loadTime = performance.now();
            addResult('performanceResults', `页面加载时间: ${loadTime.toFixed(2)}ms`, 'info');
            
            // 测试内存使用
            if (performance.memory) {
                const memory = performance.memory;
                addResult('performanceResults', 
                    `内存使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB / ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`, 
                    'info');
            }
            
            // 测试资源加载
            if (performance.getEntriesByType) {
                const resources = performance.getEntriesByType('resource');
                addResult('performanceResults', `已加载资源数量: ${resources.length}`, 'info');
                
                const slowResources = resources.filter(r => r.duration > 1000);
                if (slowResources.length > 0) {
                    addResult('performanceResults', `慢速资源数量: ${slowResources.length}`, 'warning');
                } else {
                    addResult('performanceResults', '✅ 所有资源加载速度正常', 'success');
                }
            }
        }

        function testPageLinks() {
            const container = document.getElementById('linkResults');
            container.innerHTML = '';
            
            const pages = [
                'index.html',
                'data.html',
                'signal-denoise.html',
                'serial-debug.html',
                'feedback.html',
                'register.html',
                'forgot-password.html',
                'fft-analysis.html'
            ];
            
            pages.forEach(page => {
                fetch(page, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            addResult('linkResults', `✅ ${page} 可访问`, 'success');
                        } else {
                            addResult('linkResults', `❌ ${page} 返回状态: ${response.status}`, 'error');
                        }
                    })
                    .catch(error => {
                        addResult('linkResults', `❌ ${page} 无法访问: ${error.message}`, 'error');
                    });
            });
        }

        // 页面加载完成后自动运行基本测试
        window.addEventListener('load', () => {
            addResult('performanceResults', '页面加载完成，开始性能测试...', 'info');
            setTimeout(testPerformance, 100);
        });
    </script>
</body>
</html>
