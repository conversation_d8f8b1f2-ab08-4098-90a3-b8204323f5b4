/**
 * 🔧 API配置快速修复脚本 - 固定公网地址版本
 * 在浏览器控制台中运行此脚本来立即修复API配置问题
 */

(function fixApiConfig() {
    console.log('🔧 开始修复API配置...');

    // 固定使用公网地址
    const apiBaseURL = 'https://cugzcfwwhuiq.sealoshzh.site/v1';

    console.log('🌐 使用固定公网地址');
    console.log('📡 设置API地址:', apiBaseURL);
    
    // 修复全局API配置
    if (window.API_CONFIG) {
        window.API_CONFIG.BASE_URL = apiBaseURL;
        window.API_CONFIG.DEMO_MODE = false;
        console.log('✅ API_CONFIG已修复');
    }
    
    // 修复 httpClient
    if (window.httpClient) {
        window.httpClient.baseURL = apiBaseURL.replace('/v1', '');
        console.log('✅ httpClient已修复');
    }
    
    // 修复其他可能的API配置
    if (window.API_BASE_URL) {
        window.API_BASE_URL = apiBaseURL;
        console.log('✅ API_BASE_URL已修复');
    }
    
    // 重新创建API实例
    if (window.HttpClient) {
        try {
            window.httpClient = new HttpClient();
            console.log('✅ 已重新创建httpClient实例');
        } catch (error) {
            console.warn('⚠️ 重新创建httpClient失败:', error.message);
        }
    }
    
    // 测试API连接
    const testURL = apiBaseURL.replace('/v1', '/v1');
    console.log('🔍 测试API连接:', testURL);
    
    fetch(testURL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    })
    .then(data => {
        console.log('✅ API连接测试成功:', data.message || data);
        console.log('🎉 API配置修复完成，请重试登录');
        
        // 显示成功提示
        if (typeof showMessage === 'function') {
            showMessage('API配置已修复，请重试登录', 'success');
        } else if (window.toastSystem) {
            window.toastSystem.success('配置修复', 'API配置已修复，请重试登录');
        }
    })
    .catch(error => {
        console.error('❌ API连接测试失败:', error);
        console.log('💡 可能的原因:');
        console.log('   1. 网络连接问题');
        console.log('   2. 服务器未启动');
        console.log('   3. CORS配置问题');
        console.log('   4. API地址错误');
        
        // 显示错误提示
        if (typeof showMessage === 'function') {
            showMessage('API连接测试失败，请检查网络或联系管理员', 'error');
        } else if (window.toastSystem) {
            window.toastSystem.error('连接失败', 'API连接测试失败，请检查网络或联系管理员');
        }
    });
    
    // 返回修复信息
    return {
        success: true,
        apiBaseURL: apiBaseURL,
        accessType: isPublicAccess ? 'public' : 'local',
        timestamp: new Date().toISOString()
    };
})();

// 使用说明
console.log(`
📋 使用说明:
1. 复制整个脚本内容
2. 打开浏览器开发者工具 (F12)
3. 切换到 Console 标签
4. 粘贴并按回车执行
5. 等待修复完成后重试登录

🔧 手动修复方法:
如果脚本修复失败，可以手动执行:
window.API_CONFIG.BASE_URL = 'https://cugzcfwwhuiq.sealoshzh.site/v1';
window.API_CONFIG.DEMO_MODE = false;
window.API_CONFIG.ENABLE_CORS_FALLBACK = false;
`);
