/**
 * 📦 依赖下载脚本
 * 下载项目中所有需要的外部JavaScript和CSS库
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 创建libs目录
const libsDir = path.join(__dirname, 'libs');
if (!fs.existsSync(libsDir)) {
    fs.mkdirSync(libsDir, { recursive: true });
}

// 创建css目录
const cssLibsDir = path.join(libsDir, 'css');
if (!fs.existsSync(cssLibsDir)) {
    fs.mkdirSync(cssLibsDir, { recursive: true });
}

// 创建js目录
const jsLibsDir = path.join(libsDir, 'js');
if (!fs.existsSync(jsLibsDir)) {
    fs.mkdirSync(jsLibsDir, { recursive: true });
}

// 创建fonts目录
const fontsLibsDir = path.join(libsDir, 'fonts');
if (!fs.existsSync(fontsLibsDir)) {
    fs.mkdirSync(fontsLibsDir, { recursive: true });
}

// 定义所有需要下载的依赖
const dependencies = {
    // JavaScript库
    js: [
        {
            name: 'chart.js',
            url: 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js',
            filename: 'chart.umd.js'
        },
        {
            name: 'chartjs-plugin-zoom',
            url: 'https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js',
            filename: 'chartjs-plugin-zoom.min.js'
        },
        {
            name: 'xlsx',
            url: 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js',
            filename: 'xlsx.full.min.js'
        },
        {
            name: 'particles.js',
            url: 'https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js',
            filename: 'particles.min.js'
        },
        {
            name: 'ml-matrix',
            url: 'https://cdn.jsdelivr.net/npm/ml-matrix@6.10.4/lib/index.min.js',
            filename: 'ml-matrix.min.js'
        }
    ],
    
    // CSS库
    css: [
        {
            name: 'font-awesome',
            url: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
            filename: 'font-awesome.min.css'
        },
        {
            name: 'animate.css',
            url: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
            filename: 'animate.min.css'
        }
    ]
};

// 下载文件的函数
function downloadFile(url, filepath) {
    return new Promise((resolve, reject) => {
        console.log(`📥 正在下载: ${url}`);
        
        const file = fs.createWriteStream(filepath);
        
        https.get(url, (response) => {
            // 处理重定向
            if (response.statusCode === 301 || response.statusCode === 302) {
                return downloadFile(response.headers.location, filepath)
                    .then(resolve)
                    .catch(reject);
            }
            
            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                console.log(`✅ 下载完成: ${path.basename(filepath)}`);
                resolve();
            });
            
            file.on('error', (err) => {
                fs.unlink(filepath, () => {}); // 删除不完整的文件
                reject(err);
            });
            
        }).on('error', (err) => {
            reject(err);
        });
    });
}

// 下载所有依赖
async function downloadAllDependencies() {
    console.log('🚀 开始下载项目依赖...');
    console.log('=' .repeat(50));
    
    try {
        // 下载JavaScript库
        console.log('\n📚 下载JavaScript库:');
        for (const lib of dependencies.js) {
            const filepath = path.join(jsLibsDir, lib.filename);
            await downloadFile(lib.url, filepath);
        }
        
        // 下载CSS库
        console.log('\n🎨 下载CSS库:');
        for (const lib of dependencies.css) {
            const filepath = path.join(cssLibsDir, lib.filename);
            await downloadFile(lib.url, filepath);
        }
        
        console.log('\n✅ 所有依赖下载完成!');
        console.log('📁 文件保存在 ./libs/ 目录中');
        
        // 生成本地引用的HTML模板
        generateLocalReferences();
        
    } catch (error) {
        console.error('❌ 下载失败:', error.message);
        process.exit(1);
    }
}

// 生成本地引用模板
function generateLocalReferences() {
    const template = `
<!-- 本地依赖引用模板 -->
<!-- 将以下代码替换HTML文件中的CDN引用 -->

<!-- JavaScript库 -->
<script src="libs/js/chart.umd.js"></script>
<script src="libs/js/chartjs-plugin-zoom.min.js"></script>
<script src="libs/js/xlsx.full.min.js"></script>
<script src="libs/js/particles.min.js"></script>

<!-- CSS库 -->
<link rel="stylesheet" href="libs/css/font-awesome.min.css">
<link rel="stylesheet" href="libs/css/animate.min.css">

<!-- Google字体本地化说明 -->
<!-- Google字体需要单独处理，建议使用以下备用方案： -->
<!-- 1. 下载字体文件到 libs/fonts/ 目录 -->
<!-- 2. 在CSS中定义 @font-face 规则 -->
<!-- 3. 或使用系统字体作为备用 -->
`;
    
    fs.writeFileSync(path.join(libsDir, 'local-references.html'), template);
    console.log('📝 已生成本地引用模板: libs/local-references.html');
}

// 运行下载
if (require.main === module) {
    downloadAllDependencies();
}

module.exports = { downloadAllDependencies, downloadFile };
