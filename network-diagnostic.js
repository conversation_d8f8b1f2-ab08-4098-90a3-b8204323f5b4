/**
 * 🔍 网络连接诊断脚本
 * 在浏览器控制台中运行此脚本来诊断API连接问题
 */

(async function networkDiagnostic() {
    console.log('🔍 开始网络连接诊断...');
    console.log('=' .repeat(50));
    
    // 1. 检测当前环境
    const currentURL = window.location.href;
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;
    
    console.log('📍 当前环境信息:');
    console.log('   URL:', currentURL);
    console.log('   主机:', hostname);
    console.log('   协议:', protocol);
    
    const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
    const isFileProtocol = protocol === 'file:';
    const isHTTPS = protocol === 'https:';
    
    console.log('   本地环境:', isLocalhost ? '是' : '否');
    console.log('   文件协议:', isFileProtocol ? '是' : '否');
    console.log('   HTTPS协议:', isHTTPS ? '是' : '否');
    
    // 2. 测试不同的API地址
    const testURLs = [
        'https://cugzcfwwhuiq.sealoshzh.site/v1',
        'http://localhost:8080/v1',
        'https://cugzcfwwhuiq.sealoshzh.site/health',
        'http://localhost:8080/health'
    ];
    
    console.log('\n🌐 测试API连接:');
    
    for (const testURL of testURLs) {
        console.log(`\n📡 测试: ${testURL}`);
        
        try {
            const startTime = Date.now();
            
            const response = await fetch(testURL, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                mode: 'cors',
                credentials: 'omit'
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            console.log(`   ✅ 状态: ${response.status} ${response.statusText}`);
            console.log(`   ⏱️ 响应时间: ${responseTime}ms`);
            console.log(`   📋 Headers:`, Object.fromEntries(response.headers.entries()));
            
            if (response.ok) {
                try {
                    const data = await response.json();
                    console.log(`   📄 响应数据:`, data);
                } catch (e) {
                    console.log(`   📄 响应数据: (非JSON格式)`);
                }
            }
            
        } catch (error) {
            console.log(`   ❌ 错误:`, error.name, error.message);
            
            // 详细错误分析
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                console.log(`   🔍 可能原因: CORS错误或网络连接问题`);
            } else if (error.name === 'AbortError') {
                console.log(`   🔍 可能原因: 请求超时`);
            } else {
                console.log(`   🔍 错误类型: ${error.constructor.name}`);
            }
        }
    }
    
    // 3. 测试登录请求
    console.log('\n🔐 测试登录请求:');
    
    const loginURL = isLocalhost ? 
        'http://localhost:8080/v1/auth/login' : 
        'https://cugzcfwwhuiq.sealoshzh.site/v1/auth/login';
    
    console.log(`📡 登录地址: ${loginURL}`);
    
    try {
        const response = await fetch(loginURL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                username: 'test',
                password: 'test'
            }),
            mode: 'cors',
            credentials: 'omit'
        });
        
        console.log(`   ✅ 登录请求状态: ${response.status} ${response.statusText}`);
        
        if (response.status === 401) {
            console.log(`   ✅ 服务器正常响应 (认证失败是预期的)`);
        } else if (response.status === 400) {
            console.log(`   ✅ 服务器正常响应 (请求格式错误是预期的)`);
        }
        
    } catch (error) {
        console.log(`   ❌ 登录请求失败:`, error.name, error.message);
    }
    
    // 4. 检查当前API配置
    console.log('\n⚙️ 当前API配置:');
    
    if (window.API_CONFIG) {
        console.log('   BASE_URL:', window.API_CONFIG.BASE_URL);
        console.log('   TIMEOUT:', window.API_CONFIG.TIMEOUT);
        console.log('   DEMO_MODE:', window.API_CONFIG.DEMO_MODE);
    } else {
        console.log('   ❌ API_CONFIG未找到');
    }
    
    if (window.httpClient) {
        console.log('   httpClient.baseURL:', window.httpClient.baseURL);
    } else {
        console.log('   ❌ httpClient未找到');
    }
    
    // 5. 提供修复建议
    console.log('\n💡 修复建议:');
    
    if (isFileProtocol) {
        console.log('   ⚠️ 检测到文件协议，建议使用HTTP服务器运行');
        console.log('   📝 解决方案: 使用 Live Server 或其他HTTP服务器');
    }
    
    if (!isLocalhost && !isHTTPS) {
        console.log('   ⚠️ 非HTTPS环境可能导致CORS问题');
        console.log('   📝 解决方案: 使用HTTPS或本地开发环境');
    }
    
    console.log('\n🔧 快速修复命令:');
    console.log('// 强制使用本地API');
    console.log('window.API_CONFIG.BASE_URL = "http://localhost:8080/v1";');
    console.log('window.API_CONFIG.DEMO_MODE = false;');
    console.log('');
    console.log('// 强制使用公网API');
    console.log('window.API_CONFIG.BASE_URL = "https://cugzcfwwhuiq.sealoshzh.site/v1";');
    console.log('window.API_CONFIG.DEMO_MODE = false;');
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎯 诊断完成');
    
    return {
        environment: {
            hostname,
            protocol,
            isLocalhost,
            isFileProtocol,
            isHTTPS
        },
        timestamp: new Date().toISOString()
    };
})();
