{"name": "<PERSON><PERSON><PERSON>", "title": "Hammer.JS", "description": "A javascript library for multi-touch gestures", "version": "2.0.8", "homepage": "http://hammerjs.github.io/", "license": "MIT", "keywords": ["touch", "gestures"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": ""}], "repository": {"type": "git", "url": "git://github.com/hammerjs/hammer.js.git"}, "bugs": {"url": "https://github.com/hammerjs/hammer.js/issues"}, "dependencies": {}, "devDependencies": {"changelogplease": "^1.2.0", "git-tags": "^0.2.4", "grunt": "0.4.x", "grunt-banner": "^0.2.3", "grunt-contrib-concat": "0.4.x", "grunt-contrib-connect": "0.7.x", "grunt-contrib-jshint": "0.10.x", "grunt-contrib-qunit": "^0.5.1", "grunt-contrib-uglify": "0.7.x", "grunt-contrib-watch": "0.6.x", "grunt-jscs": "^0.8.0", "grunt-string-replace": "^0.2.7", "hammer-simulator": "git://github.com/hammerjs/simulator#master", "jquery-hammerjs": "2.0.x"}, "main": "hammer.js", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}}