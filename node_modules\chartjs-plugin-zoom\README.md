# chartjs-plugin-zoom

[![GitHub Workflow Status](https://img.shields.io/github/actions/workflow/status/chartjs/chartjs-plugin-zoom/ci.yml)](https://github.com/chartjs/chartjs-plugin-zoom/actions/workflows/ci.yml)
[![Coverage Status](https://coveralls.io/repos/github/chartjs/chartjs-plugin-zoom/badge.svg?branch=master)](https://coveralls.io/github/chartjs/chartjs-plugin-zoom?branch=master)
[![release](https://img.shields.io/github/v/release/chartjs/chartjs-plugin-zoom?include_prereleases)](https://github.com/chartjs/chartjs-plugin-zoom/releases)
[![npm (latest)](https://img.shields.io/npm/v/chartjs-plugin-zoom/latest)](https://www.npmjs.com/package/chartjs-plugin-zoom/v/latest)
[![npm (next)](https://img.shields.io/npm/v/chartjs-plugin-zoom/next)](https://www.npmjs.com/package/chartjs-plugin-zoom/v/next)
[![documentation](https://img.shields.io/static/v1?message=Documentation&color=informational)](https://www.chartjs.org/chartjs-plugin-zoom/index)
<a href="https://github.com/chartjs/awesome"><img src="https://awesome.re/badge-flat2.svg" alt="Awesome"></a>

A zoom and pan plugin for Chart.js >= 3.0.0

For Chart.js 2.6.0 to 2.9.x support, use [version 0.7.7 of this plugin](https://github.com/chartjs/chartjs-plugin-zoom/releases/tag/v0.7.7).

Panning can be done via the mouse or with a finger.
Zooming is done via the mouse wheel or via a pinch gesture. [Hammer.js](https://hammerjs.github.io/) is used for gesture recognition.

## Documentation

You can find documentation for chartjs-plugin-zoom at [www.chartjs.org/chartjs-plugin-zoom](https://www.chartjs.org/chartjs-plugin-zoom/).

Prior to v0.4.0, this plugin was known as 'Chart.Zoom.js'. Old versions are still available on npm under that name.

## Contributing

Before submitting an issue or a pull request to the project, please take a moment to look over the [contributing guidelines](CONTRIBUTING.md) first.

## License

chartjs-plugin-zoom.js is available under the [MIT license](https://opensource.org/licenses/MIT).
