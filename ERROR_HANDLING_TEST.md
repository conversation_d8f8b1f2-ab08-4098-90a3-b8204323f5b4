# 错误处理测试指南

## 登录页面错误处理测试

### 1. 邮箱或密码错误
**测试步骤：**
1. 打开登录页面 (index.html)
2. 输入不存在的邮箱和任意密码
3. 点击登录按钮

**期望结果：**
- 显示错误提示："邮箱或密码错误，请检查后重试"
- 邮箱和密码输入框显示红色边框和错误信息
- 输入框有震动动画效果
- 不会跳转到其他页面

### 2. 用户不存在
**测试步骤：**
1. 输入格式正确但未注册的邮箱
2. 输入任意密码
3. 点击登录按钮

**期望结果：**
- 显示错误提示："该邮箱尚未注册，请先注册账号"
- 邮箱输入框显示："该邮箱尚未注册"
- 有震动动画效果

### 3. 网络连接异常
**测试步骤：**
1. 断开网络连接
2. 输入任意邮箱和密码
3. 点击登录按钮

**期望结果：**
- 显示错误提示："网络连接异常，请检查网络后重试"
- 按钮恢复正常状态（不再显示加载中）

### 4. 空字段验证
**测试步骤：**
1. 留空邮箱字段，输入密码
2. 点击登录按钮

**期望结果：**
- 显示："请输入邮箱地址"
- 邮箱输入框显示错误状态

## 注册页面错误处理测试

### 1. 邮箱已存在
**测试步骤：**
1. 打开注册页面 (register.html)
2. 输入已注册的邮箱
3. 填写其他信息并提交

**期望结果：**
- 显示错误提示："该邮箱已被注册，请使用其他邮箱或直接登录"
- 邮箱输入框显示："该邮箱已被注册"
- 有震动动画效果

### 2. 用户名已存在
**测试步骤：**
1. 输入已存在的用户名
2. 填写其他信息并提交

**期望结果：**
- 显示错误提示："该用户名已被使用，请选择其他用户名"
- 用户名输入框显示："该用户名已被使用"

### 3. 密码强度不够
**测试步骤：**
1. 输入少于6位的密码
2. 提交表单

**期望结果：**
- 显示错误提示："密码强度不够，请使用包含字母、数字的6位以上密码"
- 密码输入框显示："密码强度不够"

### 4. 邮箱格式错误
**测试步骤：**
1. 输入格式错误的邮箱（如：test@）
2. 提交表单

**期望结果：**
- 显示错误提示："邮箱格式不正确，请输入有效的邮箱地址"
- 邮箱输入框显示："邮箱格式不正确"

## 错误处理功能特性

### 1. 错误分类
- **认证错误**: 邮箱密码错误、用户不存在、账号锁定等
- **验证错误**: 格式错误、必填字段为空等
- **网络错误**: 连接超时、网络异常等
- **服务器错误**: 服务器内部错误、服务不可用等

### 2. 错误显示方式
- **全局提示**: 页面顶部的通知消息
- **字段级错误**: 输入框下方的具体错误信息
- **视觉反馈**: 红色边框、震动动画、错误图标

### 3. 用户体验增强
- **加载状态**: 提交时显示加载动画
- **按钮状态**: 防止重复提交
- **自动恢复**: 用户修改输入时自动清除错误状态
- **友好提示**: 提供具体的解决建议

### 4. 错误统计
- 自动记录错误发生情况
- 统计错误类型和频率
- 帮助改进用户体验

## 测试注意事项

1. **浏览器兼容性**: 在不同浏览器中测试错误处理
2. **网络状况**: 测试不同网络条件下的错误处理
3. **API响应**: 确保后端API返回正确的错误代码
4. **用户交互**: 测试用户在错误状态下的各种操作

## 错误代码映射

### 登录错误代码
- `INVALID_CREDENTIALS`: 邮箱或密码错误
- `USER_NOT_FOUND`: 用户不存在
- `ACCOUNT_LOCKED`: 账号被锁定
- `ACCOUNT_DISABLED`: 账号被禁用
- `TOO_MANY_ATTEMPTS`: 尝试次数过多
- `NETWORK_ERROR`: 网络连接异常
- `SERVER_ERROR`: 服务器错误

### 注册错误代码
- `EMAIL_EXISTS`: 邮箱已存在
- `USERNAME_EXISTS`: 用户名已存在
- `INVALID_EMAIL`: 邮箱格式错误
- `WEAK_PASSWORD`: 密码强度不够
- `VALIDATION_ERROR`: 验证错误
- `RATE_LIMIT`: 请求频率限制

## 改进建议

1. **错误恢复**: 提供快速修复错误的建议
2. **帮助链接**: 在错误信息中添加帮助文档链接
3. **错误预防**: 实时验证减少提交时的错误
4. **用户引导**: 为新用户提供更详细的指导


