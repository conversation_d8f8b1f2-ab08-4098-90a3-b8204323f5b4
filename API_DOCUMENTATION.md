# 数据分析平台API接口文档

## 基础信息

- **Base URL**: `https://cugzcfwwhuiq.sealoshzh.site/v1`
- **Content-Type**: `application/json`
- **认证方式**: `Bearer Token` (JWT)
- **版本**: `v1.0.0`

## 认证说明

大部分接口需要在请求头中包含JWT Token：
```
Authorization: Bearer <your_jwt_token>
```

## 标准响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

---

# 1. 认证与用户管理

## 1.1 用户注册

**接口**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "string",     // 用户名，3-50字符
  "email": "string",        // 邮箱地址
  "password": "string",     // 密码，至少6字符
  "confirmPassword": "string" // 确认密码
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
  }'
```

**成功响应** (201):
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "userId": "64f8a12b3c45d67890123456",
    "username": "testuser",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "USER_EXISTS",
    "message": "注册失败",
    "details": "邮箱已被注册"
  }
}
```

## 1.2 用户登录

**接口**: `POST /auth/login`

**请求参数**:
```json
{
  "email": "string",        // 邮箱地址
  "password": "string"      // 密码
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "64f8a12b3c45d67890123456",
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

**错误响应** (401):
```json
{
  "success": false,
  "error": {
    "code": "AUTH_001",
    "message": "登录失败",
    "details": "邮箱或密码错误"
  }
}
```

---

# 2. 数据管理模块

## 2.1 文件上传

**接口**: `POST /data/upload`
**认证**: 必需
**Content-Type**: `multipart/form-data`

**请求参数**:
- `files`: File[] (最多3个文件)
- `description`: string (可选，文件描述)

**支持文件类型**: csv, xlsx, xls, json, txt
**文件大小限制**: 50MB

**请求示例**:
```bash
curl -X POST https://ghlpiewlcmha.sealoshzh.site/v1/data/upload \
  -H "Authorization: Bearer <token>" \
  -F "files=@data1.csv" \
  -F "files=@data2.xlsx" \
  -F "description=测试数据文件"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "uploadId": "64f8a12b3c45d67890123456",
    "files": [
      {
        "fileId": "64f8a12b3c45d67890123457",
        "fileName": "data1.csv",
        "fileSize": 1024,
        "fileType": "csv",
        "uploadTime": "2024-01-01T00:00:00.000Z",
        "downloadUrl": "https://cugzcfwwhuiq.sealoshzh.site/uploads/user123/file456.csv"
      }
    ]
  }
}
```

**错误响应** (400):
```json
{
  "success": false,
  "error": {
    "code": "DATA_002",
    "message": "文件大小超出限制",
    "details": "文件大小不能超过50MB"
  }
}
```

## 2.2 数据解析

**接口**: `POST /data/parse`
**认证**: 必需

**请求参数**:
```json
{
  "fileId": "string",           // 文件ID
  "parseOptions": {
    "format": "csv|excel|json|txt",  // 解析格式
    "delimiter": "string",           // CSV分隔符，默认","
    "encoding": "utf-8|gbk",         // 编码格式，默认utf-8
    "hasHeader": boolean,            // 是否有表头，默认true
    "sampleRate": number            // 采样率，默认1000
  }
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/data/parse \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "fileId": "64f8a12b3c45d67890123457",
    "parseOptions": {
      "format": "csv",
      "delimiter": ",",
      "encoding": "utf-8",
      "hasHeader": true
    }
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "dataId": "64f8a12b3c45d67890123458",
    "parsedData": [1.2, 3.4, 5.6, 7.8],  // 前1000个数据点
    "metadata": {
      "totalPoints": 5000,
      "sampleRate": 1000,
      "duration": 5.0,
      "statistics": {
        "mean": 4.25,
        "std": 2.14,
        "min": 1.2,
        "max": 7.8
      }
    }
  }
}
```

## 2.3 获取历史数据

**接口**: `GET /data/history`
**认证**: 必需

**查询参数**:
- `page`: number (页码，默认1)
- `limit`: number (每页数量，默认20)
- `type`: string (类型筛选: upload|serial|sample)

**请求示例**:
```bash
curl "https://ghlpiewlcmha.sealoshzh.site/v1/data/history?page=1&limit=10&type=upload" \
  -H "Authorization: Bearer <token>"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "dataId": "64f8a12b3c45d67890123458",
        "fileName": "data1.csv",
        "uploadTime": "2024-01-01T00:00:00.000Z",
        "dataType": "csv",
        "fileSize": 1024,
        "metadata": {
          "totalPoints": 5000,
          "sampleRate": 1000
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

---

# 3. 串口调试模块

## 3.1 保存串口配置

**接口**: `POST /serial/config`
**认证**: 必需

**请求参数**:
```json
{
  "configName": "string",                    // 配置名称
  "baudRate": number,                        // 波特率: 9600|19200|38400|57600|115200
  "dataBits": number,                        // 数据位: 5|6|7|8
  "stopBits": number,                        // 停止位: 1|2
  "parity": "none|even|odd",                 // 校验位
  "flowControl": "none|hardware|software"    // 流控制
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/serial/config \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "configName": "默认配置",
    "baudRate": 115200,
    "dataBits": 8,
    "stopBits": 1,
    "parity": "none",
    "flowControl": "none"
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "configId": "64f8a12b3c45d67890123459",
    "configName": "默认配置"
  }
}
```

## 3.2 串口数据记录

**接口**: `POST /serial/data`
**认证**: 必需

**请求参数**:
```json
{
  "sessionId": "string",        // 会话ID
  "data": {
    "timestamp": "ISO8601",     // 时间戳
    "direction": "sent|received", // 数据方向
    "content": "string",        // 数据内容（十六进制字符串）
    "format": "hex|ascii|decimal" // 数据格式
  }
}
```

**请求示例**:
```bash
curl -X POST https://ghlpiewlcmha.sealoshzh.site/v1/serial/data \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "sessionId": "session_123",
    "data": {
      "timestamp": "2024-01-01T00:00:00.000Z",
      "direction": "sent",
      "content": "FF AA 55 CC",
      "format": "hex"
    }
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "recordId": "64f8a12b3c45d67890123460"
  }
}
```

## 3.3 获取串口会话历史

**接口**: `GET /serial/sessions`
**认证**: 必需

**查询参数**:
- `page`: number (页码，默认1)
- `limit`: number (每页数量，默认20)
- `startDate`: ISO8601 (开始时间)
- `endDate`: ISO8601 (结束时间)

**请求示例**:
```bash
curl "https://ghlpiewlcmha.sealoshzh.site/v1/serial/sessions?page=1&limit=10" \
  -H "Authorization: Bearer <token>"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "sessionId": "session_123",
        "startTime": "2024-01-01T00:00:00.000Z",
        "endTime": "2024-01-01T01:00:00.000Z",
        "config": {
          "baudRate": 115200,
          "dataBits": 8,
          "stopBits": 1,
          "parity": "none"
        },
        "statistics": {
          "sentBytes": 1024,
          "receivedBytes": 2048,
          "packets": 150,
          "errors": 2
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

---

# 4. 信号处理模块

## 4.1 FFT分析

**接口**: `POST /signal/fft`
**认证**: 必需

**请求参数**:
```json
{
  "dataId": "string",           // 数据ID
  "options": {
    "windowType": "hanning|hamming|blackman",  // 窗函数类型，默认hanning
    "windowSize": number,                      // 窗口大小，默认1024
    "overlap": number,                         // 重叠率 0-1，默认0.5
    "zeroPadding": boolean                     // 零填充，默认false
  }
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/signal/fft \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "dataId": "64f8a12b3c45d67890123458",
    "options": {
      "windowType": "hanning",
      "windowSize": 1024,
      "overlap": 0.5,
      "zeroPadding": false
    }
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "analysisId": "64f8a12b3c45d67890123461",
    "frequencyData": [0.1, 0.2, 0.3, 0.4],
    "phaseData": [0.0, 1.57, 3.14, 4.71],
    "powerSpectralDensity": [0.01, 0.04, 0.09, 0.16],
    "dominantFrequencies": [
      {
        "frequency": 50,
        "amplitude": 0.8,
        "phase": 1.57
      }
    ]
  }
}
```

## 4.2 信号降噪

**接口**: `POST /signal/denoise`
**认证**: 必需

**请求参数**:
```json
{
  "dataId": "string",               // 数据ID
  "method": "wavelet|butterworth|median|gaussian", // 降噪方法
  "parameters": {
    "cutoffFrequency": number,      // 截止频率
    "order": number,                // 滤波器阶数
    "waveletType": "db4|db8|haar",  // 小波类型
    "threshold": number,            // 阈值
    "windowSize": number,           // 窗口大小（中值滤波）
    "sigma": number                 // 标准差（高斯滤波）
  }
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/signal/denoise \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "dataId": "64f8a12b3c45d67890123458",
    "method": "butterworth",
    "parameters": {
      "cutoffFrequency": 0.2,
      "order": 4
    }
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "denoisedDataId": "64f8a12b3c45d67890123462",
    "denoisedData": [1.1, 3.3, 5.5, 7.7],
    "metrics": {
      "snrImprovement": 15.2,
      "mseReduction": 0.05,
      "correlationCoefficient": 0.98
    }
  }
}
```

## 4.3 包络分析

**接口**: `POST /signal/envelope`
**认证**: 必需

**请求参数**:
```json
{
  "dataId": "string",               // 数据ID
  "method": "hilbert|peak|rms",     // 包络分析方法
  "windowSize": number              // 窗口大小
}
```

**请求示例**:
```bash
curl -X POST https://ghlpiewlcmha.sealoshzh.site/v1/signal/envelope \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "dataId": "64f8a12b3c45d67890123458",
    "method": "hilbert",
    "windowSize": 64
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "envelopeData": [1.5, 3.2, 4.8, 6.1],
    "instantaneousPhase": [0.0, 0.5, 1.2, 1.8],
    "instantaneousFrequency": [0.1, 0.2, 0.15, 0.3]
  }
}
```

---

# 5. 数据导出模块

## 5.1 导出数据

**接口**: `POST /export/data`
**认证**: 必需

**请求参数**:
```json
{
  "dataIds": ["string"],            // 数据ID列表
  "format": "csv|excel|json|matlab", // 导出格式
  "options": {
    "includeMetadata": boolean,     // 是否包含元数据
    "compression": "none|zip|gzip"  // 压缩格式
  }
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/export/data \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "dataIds": ["64f8a12b3c45d67890123458"],
    "format": "excel",
    "options": {
      "includeMetadata": true,
      "compression": "none"
    }
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "exportId": "64f8a12b3c45d67890123463",
    "message": "导出任务已创建，正在处理中"
  }
}
```

## 5.2 导出图表

**接口**: `POST /export/charts`
**认证**: 必需

**请求参数**:
```json
{
  "chartConfigs": [
    {
      "type": "time|frequency|envelope|phase", // 图表类型
      "dataId": "string",                      // 数据ID
      "format": "png|pdf|svg",                 // 图表格式
      "resolution": "720p|1080p|4k"            // 分辨率
    }
  ]
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/export/charts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "chartConfigs": [
      {
        "type": "frequency",
        "dataId": "64f8a12b3c45d67890123458",
        "format": "png",
        "resolution": "1080p"
      }
    ]
  }'
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "exportId": "64f8a12b3c45d67890123464",
    "message": "图表导出任务已创建，正在处理中"
  }
}
```

## 5.3 获取导出状态

**接口**: `GET /export/{exportId}`
**认证**: 必需

**请求示例**:
```bash
curl "https://cugzcfwwhuiq.sealoshzh.site/v1/export/64f8a12b3c45d67890123463" \
  -H "Authorization: Bearer <token>"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "exportId": "64f8a12b3c45d67890123463",
    "status": "completed",
    "exportType": "data",
    "format": "excel",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "processingTime": 5000,
    "downloadUrl": "https://ghlpiewlcmha.sealoshzh.site/v1/export/64f8a12b3c45d67890123463/download",
    "expiresAt": "2024-01-02T00:00:00.000Z",
    "fileSize": 2048
  }
}
```

## 5.4 下载导出文件

**接口**: `GET /export/{exportId}/download`
**认证**: 必需

**请求示例**:
```bash
curl "https://ghlpiewlcmha.sealoshzh.site/v1/export/64f8a12b3c45d67890123463/download" \
  -H "Authorization: Bearer <token>" \
  -o exported_data.xlsx
```

**成功响应**: 直接返回文件内容

---

# 6. 反馈管理模块

## 6.1 提交反馈

**接口**: `POST /feedback`
**认证**: 可选

**请求参数**:
```json
{
  "title": "string",                        // 反馈标题，5-200字符
  "category": "bug|feature|improvement|question", // 反馈类别
  "priority": "low|medium|high|urgent",     // 优先级
  "content": "string",                      // 反馈内容，至少10字符
  "pageUrl": "string",                      // 页面URL（可选）
  "contactInfo": {                          // 联系方式（可选）
    "name": "string",
    "email": "string"
  },
  "attachments": ["string"]                 // 附件ID列表（可选）
}
```

**请求示例**:
```bash
curl -X POST https://ghlpiewlcmha.sealoshzh.site/v1/feedback \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "title": "数据上传功能建议",
    "category": "improvement",
    "priority": "medium",
    "content": "建议支持更多文件格式，如PDF、XML等",
    "pageUrl": "https://example.com/upload",
    "contactInfo": {
      "name": "张三",
      "email": "<EMAIL>"
    }
  }'
```

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "feedbackId": "64f8a12b3c45d67890123465",
    "trackingNumber": "FB1704067200ABCD"
  }
}
```

## 6.2 获取反馈列表

**接口**: `GET /feedback`
**认证**: 必需

**查询参数**:
- `page`: number (页码，默认1)
- `limit`: number (每页数量，默认20)
- `status`: string (状态筛选: new|processing|resolved|closed)
- `category`: string (类别筛选: bug|feature|improvement|question)
- `priority`: string (优先级筛选: low|medium|high|urgent)

**请求示例**:
```bash
curl "https://cugzcfwwhuiq.sealoshzh.site/v1/feedback?page=1&limit=10&status=new" \
  -H "Authorization: Bearer <token>"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "feedbacks": [
      {
        "id": "64f8a12b3c45d67890123465",
        "title": "数据上传功能建议",
        "category": "improvement",
        "priority": "medium",
        "status": "new",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "commentsCount": 2,
        "user": {
          "username": "testuser",
          "email": "<EMAIL>"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15,
      "totalPages": 2
    },
    "statistics": {
      "total": 15,
      "new": 5,
      "processing": 7,
      "resolved": 2,
      "closed": 1
    }
  }
}
```

## 6.3 添加反馈评论

**接口**: `POST /feedback/{feedbackId}/comments`
**认证**: 可选

**请求参数**:
```json
{
  "content": "string",      // 评论内容，至少5字符
  "author": "string"       // 作者姓名（匿名时使用）
}
```

**请求示例**:
```bash
curl -X POST https://cugzcfwwhuiq.sealoshzh.site/v1/feedback/64f8a12b3c45d67890123465/comments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "content": "我们会考虑添加更多文件格式支持",
    "author": "管理员"
  }'
```

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "commentId": "64f8a12b3c45d67890123466",
    "content": "我们会考虑添加更多文件格式支持",
    "author": "管理员",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## 6.4 根据跟踪号查询反馈

**接口**: `GET /feedback/tracking/{trackingNumber}`
**认证**: 无需

**请求示例**:
```bash
curl "https://ghlpiewlcmha.sealoshzh.site/v1/feedback/tracking/FB1704067200ABCD"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "id": "64f8a12b3c45d67890123465",
    "title": "数据上传功能建议",
    "category": "improvement",
    "priority": "medium",
    "status": "processing",
    "trackingNumber": "FB1704067200ABCD",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:30:00.000Z",
    "commentsCount": 2,
    "latestUpdate": "2024-01-01T00:30:00.000Z"
  }
}
```

---

# 7. 系统统计模块

## 7.1 系统统计

**接口**: `GET /stats/system`
**认证**: 必需

**请求示例**:
```bash
curl "https://cugzcfwwhuiq.sealoshzh.site/v1/stats/system" \
  -H "Authorization: Bearer <token>"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 150,
      "active": 80,
      "newToday": 5
    },
    "data": {
      "totalFiles": 1250,
      "totalSize": 5368709120,
      "uploadsToday": 25
    },
    "feedback": {
      "total": 45,
      "resolved": 35,
      "pending": 10
    },
    "analytics": {
      "totalAnalyses": 850,
      "totalExports": 320,
      "totalSerialSessions": 125
    },
    "performance": {
      "avgResponseTime": 180,
      "uptime": 720,
      "errorRate": 0.5,
      "cpuUsage": 35,
      "memoryUsage": 65,
      "diskUsage": 45,
      "networkIO": 85
    }
  }
}
```

## 7.2 用户活动统计

**接口**: `GET /stats/user-activity`
**认证**: 必需

**查询参数**:
- `period`: string (时间周期: day|week|month|year，默认week)
- `startDate`: ISO8601 (开始时间)
- `endDate`: ISO8601 (结束时间)

**请求示例**:
```bash
curl "https://cugzcfwwhuiq.sealoshzh.site/v1/stats/user-activity?period=week" \
  -H "Authorization: Bearer <token>"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "timeline": [
      {
        "date": "2024-01-01",
        "uploads": 25,
        "analyses": 15,
        "exports": 8
      },
      {
        "date": "2024-01-02",
        "uploads": 32,
        "analyses": 20,
        "exports": 12
      }
    ],
    "summary": {
      "totalUploads": 180,
      "totalAnalyses": 120,
      "totalExports": 65
    },
    "popularFeatures": [
      {
        "feature": "fft",
        "usage": 45
      },
      {
        "feature": "denoise",
        "usage": 32
      }
    ],
    "userDistribution": [
      {
        "region": "北京",
        "users": 85
      },
      {
        "region": "上海",
        "users": 72
      }
    ],
    "period": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-08T00:00:00.000Z",
      "type": "week"
    }
  }
}
```

## 7.3 个人统计

**接口**: `GET /stats/personal`
**认证**: 必需

**查询参数**:
- `period`: string (时间周期: day|week|month|year，默认month)

**请求示例**:
```bash
curl "https://cugzcfwwhuiq.sealoshzh.site/v1/stats/personal?period=month" \
  -H "Authorization: Bearer <token>"
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "summary": {
      "files": {
        "totalFiles": 15,
        "totalSize": 52428800
      },
      "analyses": {
        "totalAnalyses": 25,
        "completedAnalyses": 22,
        "avgProcessingTime": 1500
      },
      "exports": {
        "totalExports": 8,
        "totalDownloads": 12
      }
    },
    "activityTimeline": [
      {
        "date": "2024-01-01",
        "uploads": 2,
        "analyses": 3
      },
      {
        "date": "2024-01-02",
        "uploads": 1,
        "analyses": 2
      }
    ],
    "recentActivities": [
      {
        "type": "upload",
        "description": "上传文件: data.csv",
        "timestamp": "2024-01-01T10:30:00.000Z",
        "details": {
          "fileType": "csv"
        }
      },
      {
        "type": "analysis",
        "description": "执行fft分析",
        "timestamp": "2024-01-01T10:35:00.000Z",
        "details": {
          "analysisType": "fft",
          "status": "completed",
          "fileName": "data.csv"
        }
      }
    ],
    "period": {
      "start": "2024-01-01T00:00:00.000Z",
      "end": "2024-01-31T23:59:59.000Z",
      "type": "month"
    }
  }
}
```

---

# 8. WebSocket实时通信

## 8.1 连接建立

**WebSocket URL**: `wss://cugzcfwwhuiq.sealoshzh.site`

**连接示例**:
```javascript
const io = require('socket.io-client');

const socket = io('https://cugzcfwwhuiq.sealoshzh.site', {
  auth: {
    token: 'your_jwt_token_here'
  }
});

// 连接成功
socket.on('connected', (data) => {
  console.log('连接成功:', data);
});

// 连接错误
socket.on('error', (error) => {
  console.error('连接错误:', error);
});
```

## 8.2 串口数据实时传输

### 加入串口会话
```javascript
socket.emit('join_serial_session', {
  sessionId: 'session_123'
});

socket.on('serial_session_joined', (data) => {
  console.log('已加入会话:', data);
});
```

### 发送串口数据
```javascript
socket.emit('serial_data_send', {
  sessionId: 'session_123',
  content: 'FF AA 55 CC',
  format: 'hex'
});
```

### 接收串口数据
```javascript
socket.on('serial_data', (data) => {
  console.log('收到串口数据:', data);
  /*
  {
    "type": "serial_data",
    "data": {
      "sessionId": "session_123",
      "timestamp": "2024-01-01T00:00:00.000Z",
      "direction": "received",
      "content": "FF AA 55 CC",
      "format": "hex"
    }
  }
  */
});
```

### 离开串口会话
```javascript
socket.emit('leave_serial_session', {
  sessionId: 'session_123'
});
```

## 8.3 分析进度订阅

### 订阅分析进度
```javascript
socket.emit('subscribe_analysis', {
  analysisId: '64f8a12b3c45d67890123461'
});

socket.on('analysis_subscribed', (data) => {
  console.log('已订阅分析:', data);
});
```

### 接收进度更新
```javascript
socket.on('analysis_progress', (data) => {
  console.log('分析进度:', data);
  /*
  {
    "type": "analysis_progress",
    "data": {
      "analysisId": "64f8a12b3c45d67890123461",
      "progress": 65,
      "stage": "执行FFT变换",
      "estimatedTime": 30000,
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  }
  */
});
```

### 接收完成通知
```javascript
socket.on('analysis_complete', (data) => {
  console.log('分析完成:', data);
  /*
  {
    "type": "analysis_complete",
    "data": {
      "analysisId": "64f8a12b3c45d67890123461",
      "result": {
        "frequencyData": [...],
        "phaseData": [...]
      },
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  }
  */
});
```

### 取消订阅
```javascript
socket.emit('unsubscribe_analysis', {
  analysisId: '64f8a12b3c45d67890123461'
});
```

## 8.4 心跳检测

```javascript
// 发送心跳
socket.emit('ping');

// 接收心跳响应
socket.on('pong', (data) => {
  console.log('心跳响应:', data.timestamp);
});
```

## 8.5 系统通知

```javascript
// 接收系统通知
socket.on('system_notification', (data) => {
  console.log('系统通知:', data);
});

// 接收系统消息
socket.on('system_message', (data) => {
  console.log('系统消息:', data);
});
```

---

# 9. 错误码说明

| 错误码 | 描述 | 详细说明 |
|--------|------|----------|
| AUTH_001 | 认证失败 | 用户名密码错误或Token无效 |
| AUTH_002 | Token过期 | JWT Token已过期，需要重新登录 |
| DATA_001 | 文件格式不支持 | 上传的文件格式不在支持列表中 |
| DATA_002 | 文件大小超限 | 文件大小超过50MB限制 |
| SIGNAL_001 | 信号处理参数错误 | 传入的信号处理参数不正确 |
| EXPORT_001 | 导出格式不支持 | 请求的导出格式不支持 |
| RATE_001 | 请求频率超限 | API调用频率超过限制 |
| VALIDATION_ERROR | 输入验证失败 | 请求参数验证不通过 |
| NOT_FOUND | 接口不存在 | 请求的接口路径不存在 |
| INTERNAL_ERROR | 服务器内部错误 | 服务器处理过程中发生错误 |

---

# 10. 常用HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 410 | 资源已过期 |
| 429 | 请求频率超限 |
| 500 | 服务器内部错误 |

---

# 11. 数据类型说明

## 用户对象
```typescript
interface User {
  id: string;
  username: string;
  email: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
}
```

## 数据文件对象
```typescript
interface DataFile {
  fileId: string;
  fileName: string;
  fileSize: number;
  fileType: 'csv' | 'excel' | 'json' | 'txt';
  uploadTime: string;
  downloadUrl: string;
  metadata?: {
    totalPoints: number;
    sampleRate: number;
    duration: number;
    statistics: {
      mean: number;
      std: number;
      min: number;
      max: number;
    };
  };
}
```

## 分析结果对象
```typescript
interface AnalysisResult {
  analysisId: string;
  analysisType: 'fft' | 'denoise' | 'envelope' | 'parse';
  status: 'processing' | 'completed' | 'failed';
  progress: number;
  processingTime?: number;
  createdAt: string;
  resultData?: any;
}
```

---

# 12. 开发环境说明

- **服务器地址**: https://cugzcfwwhuiq.sealoshzh.site
- **API版本**: v1.0.0
- **支持协议**: HTTP/HTTPS, WebSocket/WSS
- **数据库**: MongoDB
- **缓存**: Redis (可选)
- **文件存储**: 本地存储

---

# 13. 注意事项

1. **认证Token**: 大部分接口需要JWT认证，Token有效期为7天
2. **文件上传**: 支持最多3个文件同时上传，单文件最大50MB
3. **WebSocket**: 需要在连接时提供有效的JWT Token
4. **分页**: 列表接口默认每页20条记录，最大100条
5. **缓存**: 部分接口有缓存机制，数据可能有延迟
6. **错误处理**: 所有错误都会返回统一格式的错误信息
7. **CORS**: 已配置跨域访问支持

---

这份文档包含了所有API接口的详细信息，您可以基于此文档进行前端开发对接。如有任何问题或需要补充说明，请随时联系！ 