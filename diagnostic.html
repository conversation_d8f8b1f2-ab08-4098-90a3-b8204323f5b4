<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统诊断 - 数据分析平台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .diagnostic-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .diagnostic-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .diagnostic-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .status-ok {
            border-left-color: #28a745;
        }
        .status-warning {
            border-left-color: #ffc107;
        }
        .status-error {
            border-left-color: #dc3545;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .badge-ok {
            background: #28a745;
        }
        .badge-warning {
            background: #ffc107;
        }
        .badge-error {
            background: #dc3545;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .solution-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
        }
        .solution-box h4 {
            margin-top: 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔧 系统诊断工具</h1>
        <p>检测系统环境和网络连接状态，帮助解决常见问题。</p>

        <div class="diagnostic-section">
            <h3>🌐 网络环境检测</h3>
            <div id="network-status">
                <div class="status-item" id="location-status">
                    <span>访问位置</span>
                    <span class="status-badge badge-warning">检测中...</span>
                </div>
                <div class="status-item" id="cors-status">
                    <span>CORS支持</span>
                    <span class="status-badge badge-warning">检测中...</span>
                </div>
                <div class="status-item" id="api-status">
                    <span>API连接</span>
                    <span class="status-badge badge-warning">检测中...</span>
                </div>
            </div>
            <button class="test-button" onclick="testNetworkConnection()">重新检测网络</button>
        </div>

        <div class="diagnostic-section">
            <h3>📚 资源加载检测</h3>
            <div id="resource-status">
                <div class="status-item" id="particles-status">
                    <span>Particles.js</span>
                    <span class="status-badge badge-warning">检测中...</span>
                </div>
                <div class="status-item" id="icons-status">
                    <span>图标字体</span>
                    <span class="status-badge badge-warning">检测中...</span>
                </div>
                <div class="status-item" id="css-status">
                    <span>样式文件</span>
                    <span class="status-badge badge-warning">检测中...</span>
                </div>
            </div>
            <button class="test-button" onclick="testResourceLoading()">重新检测资源</button>
        </div>

        <div class="diagnostic-section">
            <h3>🔐 认证系统测试</h3>
            <div id="auth-status">
                <div class="status-item" id="auth-api-status">
                    <span>认证API</span>
                    <span class="status-badge badge-warning">未测试</span>
                </div>
                <div class="status-item" id="token-status">
                    <span>Token管理</span>
                    <span class="status-badge badge-warning">未测试</span>
                </div>
            </div>
            <button class="test-button" onclick="testAuthSystem()">测试认证系统</button>
        </div>

        <div class="diagnostic-section">
            <h3>📋 诊断日志</h3>
            <div class="log-area" id="diagnostic-log">
                系统诊断开始...<br>
            </div>
            <button class="test-button" onclick="clearLog()">清空日志</button>
            <button class="test-button" onclick="exportLog()">导出日志</button>
        </div>

        <div class="diagnostic-section" id="solutions" style="display: none;">
            <h3>💡 解决方案</h3>
            <div id="solution-content"></div>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/api-service.js"></script>
    <script src="js/auth-api.js"></script>

    <script>
        let diagnosticLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            diagnosticLog.push(logEntry);
            
            const logArea = document.getElementById('diagnostic-log');
            logArea.innerHTML += logEntry + '<br>';
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            const badge = element.querySelector('.status-badge');
            
            badge.className = `status-badge badge-${status}`;
            badge.textContent = text;
            
            element.className = `status-item status-${status}`;
        }

        function showSolution(title, content) {
            const solutionsDiv = document.getElementById('solutions');
            const solutionContent = document.getElementById('solution-content');
            
            solutionContent.innerHTML = `
                <div class="solution-box">
                    <h4>${title}</h4>
                    <p>${content}</p>
                </div>
            `;
            
            solutionsDiv.style.display = 'block';
        }

        // 网络环境检测
        async function testNetworkConnection() {
            log('开始网络环境检测...');
            
            // 检测访问位置
            const isLocalhost = window.location.hostname === 'localhost' || 
                               window.location.hostname === '127.0.0.1' || 
                               window.location.protocol === 'file:';
            
            if (isLocalhost) {
                updateStatus('location-status', 'warning', '本地环境');
                log('检测到本地开发环境');
            } else {
                updateStatus('location-status', 'ok', '在线环境');
                log('检测到在线环境');
            }

            // 测试API连接
            try {
                const httpClient = new HttpClient();
                const startTime = Date.now();
                
                // 尝试一个简单的API请求
                await httpClient.fastRequest('/auth/ping', { method: 'GET' });
                
                const responseTime = Date.now() - startTime;
                updateStatus('api-status', 'ok', `连接正常 (${responseTime}ms)`);
                updateStatus('cors-status', 'ok', '支持');
                log(`API连接成功，响应时间: ${responseTime}ms`);
                
            } catch (error) {
                if (error.code === 'CORS_ERROR' || error.message.includes('CORS')) {
                    updateStatus('cors-status', 'error', '不支持');
                    updateStatus('api-status', 'error', 'CORS错误');
                    log('CORS错误: ' + error.message);
                    
                    showSolution('CORS问题解决方案', 
                        '1. 如果是本地开发，请使用本地服务器运行项目<br>' +
                        '2. 联系管理员配置服务器CORS设置<br>' +
                        '3. 使用浏览器扩展临时禁用CORS检查（仅开发环境）');
                        
                } else if (error.code === 'FAST_TIMEOUT') {
                    updateStatus('api-status', 'warning', '响应慢');
                    updateStatus('cors-status', 'warning', '未知');
                    log('API响应超时: ' + error.message);
                } else {
                    updateStatus('api-status', 'error', '连接失败');
                    updateStatus('cors-status', 'warning', '未知');
                    log('API连接失败: ' + error.message);
                }
            }
        }

        // 资源加载检测
        function testResourceLoading() {
            log('开始资源加载检测...');
            
            // 检测 Particles.js
            if (typeof particlesJS !== 'undefined') {
                updateStatus('particles-status', 'ok', '已加载');
                log('Particles.js 加载成功');
            } else {
                updateStatus('particles-status', 'error', '未加载');
                log('Particles.js 加载失败');
                
                showSolution('Particles.js 加载问题', 
                    '1. 检查网络连接是否正常<br>' +
                    '2. 尝试刷新页面重新加载<br>' +
                    '3. 粒子背景不影响核心功能，可以忽略');
            }

            // 检测图标字体
            const testIcon = document.createElement('span');
            testIcon.className = 'iconfont icon-user';
            testIcon.style.position = 'absolute';
            testIcon.style.left = '-9999px';
            document.body.appendChild(testIcon);
            
            setTimeout(() => {
                const computedStyle = window.getComputedStyle(testIcon);
                if (computedStyle.fontFamily.includes('iconfont')) {
                    updateStatus('icons-status', 'ok', '已加载');
                    log('图标字体加载成功');
                } else {
                    updateStatus('icons-status', 'warning', '备用方案');
                    log('图标字体使用备用方案');
                }
                document.body.removeChild(testIcon);
            }, 100);

            // 检测CSS
            updateStatus('css-status', 'ok', '已加载');
            log('CSS样式文件加载正常');
        }

        // 认证系统测试
        async function testAuthSystem() {
            log('开始认证系统测试...');
            
            try {
                const httpClient = new HttpClient();
                const authAPI = new AuthAPI(httpClient);
                
                // 测试Token管理
                if (httpClient.tokenManager) {
                    updateStatus('token-status', 'ok', '正常');
                    log('Token管理器初始化成功');
                } else {
                    updateStatus('token-status', 'error', '异常');
                    log('Token管理器初始化失败');
                }
                
                // 测试认证API（使用无效凭据，只测试连接）
                try {
                    await authAPI.login({ email: '<EMAIL>', password: 'test' });
                } catch (error) {
                    if (error.code === 'CORS_ERROR') {
                        updateStatus('auth-api-status', 'error', 'CORS错误');
                        log('认证API CORS错误');
                    } else {
                        updateStatus('auth-api-status', 'ok', '连接正常');
                        log('认证API连接正常（预期的认证失败）');
                    }
                }
                
            } catch (error) {
                updateStatus('auth-api-status', 'error', '异常');
                log('认证系统测试失败: ' + error.message);
            }
        }

        function clearLog() {
            diagnosticLog = [];
            document.getElementById('diagnostic-log').innerHTML = '日志已清空...<br>';
        }

        function exportLog() {
            const logContent = diagnosticLog.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `diagnostic-log-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            log('诊断日志已导出');
        }

        // 页面加载完成后自动开始检测
        window.addEventListener('DOMContentLoaded', function() {
            log('系统诊断工具启动');
            
            setTimeout(() => {
                testNetworkConnection();
                testResourceLoading();
            }, 1000);
        });
    </script>
</body>
</html>
