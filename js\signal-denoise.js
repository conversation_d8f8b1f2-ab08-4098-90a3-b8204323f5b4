// 信号去噪功能实现
class SignalDenoiser {
    constructor() {
        this.originalSignal = null;
        this.noisySignal = null;
        this.cleanSignal = null;
        this.denoisedSignals = {};
        this.charts = {};
        this.metrics = {};

        try {
            console.log('开始初始化事件监听器...');
            this.initializeEventListeners();
            console.log('事件监听器初始化完成');

            console.log('开始初始化图表...');
            this.initializeCharts();
            console.log('图表初始化完成');
        } catch (error) {
            console.error('SignalDenoiser构造函数错误:', error);
            throw error;
        }
    }

    initializeEventListeners() {
        try {
            // 文件导入
            const fileImport = document.getElementById('fileImport');
            const fileInput = document.getElementById('fileInput');
            if (fileImport && fileInput) {
                fileImport.addEventListener('click', () => {
                    fileInput.click();
                });

                fileInput.addEventListener('change', (e) => {
                    this.handleFileImport(e.target.files[0]);
                });
            }

            // 粘贴板导入
            const clipboardImport = document.getElementById('clipboardImport');
            if (clipboardImport) {
                clipboardImport.addEventListener('click', () => {
                    this.handleClipboardImport();
                });
            }

            // 信号生成
            const generateSignal = document.getElementById('generateSignal');
            if (generateSignal) {
                generateSignal.addEventListener('click', () => {
                    this.generateSignal();
                });
            }

            // 参数更新
            this.setupParameterListeners();

            // 算法选择
            this.setupAlgorithmListeners();

            // 去噪执行
            const executeDenoising = document.getElementById('executeDenoising');
            if (executeDenoising) {
                executeDenoising.addEventListener('click', () => {
                    this.executeDenoising();
                });
            }

            // 应用噪声按钮
            const applyNoiseBtn = document.getElementById('applyNoiseBtn');
            if (applyNoiseBtn) {
                applyNoiseBtn.addEventListener('click', () => {
                    this.applyNoise();
                });
            }

            // 噪声配置初始化
            this.initNoiseConfig();

            // 清除结果
            const clearResults = document.getElementById('clearResults');
            if (clearResults) {
                clearResults.addEventListener('click', () => {
                    this.clearResults();
                });
            }

            // 导出功能
            this.setupExportListeners();

            // 噪声配置
            const addNoise = document.getElementById('addNoise');
            const noiseConfig = document.getElementById('noiseConfig');
            if (addNoise && noiseConfig) {
                addNoise.addEventListener('change', (e) => {
                    noiseConfig.style.display = e.target.checked ? 'block' : 'none';
                });
            }

            // 初始化图表操作按钮
            this.initChartActions();
        } catch (error) {
            console.error('初始化事件监听器时出错:', error);
            throw error;
        }
    }

    setupParameterListeners() {
        // 信号生成参数 - 新的HTML结构
        const freqSlider = document.getElementById('freqSlider');
        if (freqSlider) {
            freqSlider.addEventListener('input', (e) => {
                const logValue = parseFloat(e.target.value);
                const freq = Math.pow(10, logValue);
                let freqText;
                if (freq >= 1000000) {
                    freqText = (freq / 1000000).toFixed(1) + 'MHz';
                } else if (freq >= 1000) {
                    freqText = (freq / 1000).toFixed(1) + 'kHz';
                } else {
                    freqText = freq.toFixed(1) + 'Hz';
                }
                document.getElementById('freqValue').textContent = freqText;
                this.updateSliderProgress(e.target);
            });
            this.updateSliderProgress(freqSlider);
        }

        const ampSlider = document.getElementById('ampSlider');
        if (ampSlider) {
            ampSlider.addEventListener('input', (e) => {
                document.getElementById('ampValue').textContent = e.target.value;
                this.updateSliderProgress(e.target);
            });
            this.updateSliderProgress(ampSlider);
        }

        const pointsSlider = document.getElementById('pointsSlider');
        if (pointsSlider) {
            pointsSlider.addEventListener('input', (e) => {
                document.getElementById('pointsValue').textContent = e.target.value;
                this.updateSliderProgress(e.target);
            });
            this.updateSliderProgress(pointsSlider);
        }

        const noiseLevel = document.getElementById('noiseLevel');
        if (noiseLevel) {
            noiseLevel.addEventListener('input', (e) => {
                document.getElementById('noiseValue').textContent = e.target.value;
            });
        }

        // 信号类型选择
        const sampleItems = document.querySelectorAll('.sample-item');
        sampleItems.forEach(item => {
            item.addEventListener('click', () => {
                sampleItems.forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });

        // 去噪算法参数
        const denoiseParams = ['windowSize', 'sgWindow', 'sgOrder', 'svdRatio', 'kernelSize', 'emdDepth', 'waveletLevels', 'thresholdFactor'];
        denoiseParams.forEach(param => {
            const element = document.getElementById(param);
            if (element) {
                element.addEventListener('input', (e) => {
                    document.getElementById(param + 'Value').textContent = e.target.value;
                    this.updateSliderProgress(e.target);
                });
                this.updateSliderProgress(element);
            }
        });

        // 噪声幅度滑动条
        const noiseAmpSlider = document.getElementById('noiseAmpSlider');
        if (noiseAmpSlider) {
            noiseAmpSlider.addEventListener('input', (e) => {
                document.getElementById('noiseAmpValue').textContent = e.target.value;
                this.updateSliderProgress(e.target);
            });
            this.updateSliderProgress(noiseAmpSlider);
        }
    }

    setupAlgorithmListeners() {
        const algorithms = ['movingAverage', 'sgFilter', 'svdDenoise', 'convolution', 'emdDenoise', 'waveletDenoise'];
        algorithms.forEach(algo => {
            const element = document.getElementById(algo);
            if (element) {
                element.addEventListener('change', (e) => {
                    const paramPanel = document.getElementById(algo + 'Params');
                    if (paramPanel) {
                        paramPanel.style.display = e.target.checked ? 'block' : 'none';
                    }
                });
            }
        });
    }

    setupExportListeners() {
        document.getElementById('exportData').addEventListener('click', () => this.exportData());
        document.getElementById('exportChart').addEventListener('click', () => this.exportAllChartsFromResultPanel());
        document.getElementById('exportReport').addEventListener('click', () => this.exportReport());
        document.getElementById('exportMetrics').addEventListener('click', () => this.exportMetrics());
    }

    async handleFileImport(file) {
        if (!file) return;

        try {
            const data = await this.readFile(file);
            this.processImportedData(data);
            this.showNotification('文件导入成功', 'success');
        } catch (error) {
            this.showNotification('文件导入失败: ' + error.message, 'error');
        }
    }

    async readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const content = e.target.result;
                    let data;

                    if (file.name.endsWith('.csv') || file.name.endsWith('.txt')) {
                        data = this.parseCSV(content);
                    } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                        const workbook = XLSX.read(content, { type: 'binary' });
                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];
                        data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    } else {
                        throw new Error('不支持的文件格式');
                    }

                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => reject(new Error('文件读取失败'));

            if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                reader.readAsBinaryString(file);
            } else {
                reader.readAsText(file);
            }
        });
    }

    parseCSV(content) {
        const lines = content.trim().split('\n');
        return lines.map(line => {
            return line.split(',').map(cell => {
                const num = parseFloat(cell.trim());
                return isNaN(num) ? cell.trim() : num;
            });
        });
    }

    processImportedData(data) {
        // 假设第一列是时间，第二列是信号值
        if (data.length < 2) {
            throw new Error('数据格式不正确');
        }

        // 跳过标题行（如果存在）
        let startRow = 0;
        if (isNaN(data[0][0]) || isNaN(data[0][1])) {
            startRow = 1;
        }

        const timeData = [];
        const signalData = [];

        for (let i = startRow; i < data.length; i++) {
            if (data[i].length >= 2 && !isNaN(data[i][0]) && !isNaN(data[i][1])) {
                timeData.push(data[i][0]);
                signalData.push(data[i][1]);
            }
        }

        if (signalData.length === 0) {
            throw new Error('未找到有效的信号数据');
        }

        this.noisySignal = signalData;
        this.updateTimeChart();
    }

    async handleClipboardImport() {
        try {
            const text = await navigator.clipboard.readText();
            const data = this.parseCSV(text);
            this.processImportedData(data);
            this.showNotification('剪贴板数据导入成功', 'success');
        } catch (error) {
            this.showNotification('剪贴板导入失败: ' + error.message, 'error');
        }
    }

    generateSignal() {
        try {
            // 获取选中的信号类型
            const activeItem = document.querySelector('.sample-item.active');
            const signalType = activeItem ? activeItem.dataset.type : 'sine';

            // 获取参数值
            const freqSlider = document.getElementById('freqSlider');
            const ampSlider = document.getElementById('ampSlider');
            const pointsSlider = document.getElementById('pointsSlider');
            const noiseCheck = document.getElementById('noiseCheck');
            const noiseAmpSlider = document.getElementById('noiseAmpSlider');

            if (!freqSlider || !ampSlider || !pointsSlider) {
                this.showNotification('参数控件未找到', 'error');
                return;
            }

            const logFreq = parseFloat(freqSlider.value);
            const frequency = Math.pow(10, logFreq);
            const amplitude = parseFloat(ampSlider.value);
            const samples = parseInt(pointsSlider.value);
            const addNoise = noiseCheck ? noiseCheck.checked : false;
            const noiseLevel = noiseAmpSlider ? parseFloat(noiseAmpSlider.value) : 0.2;

        // 生成时间轴
        const timeStep = 1 / (frequency * 20); // 采样频率为信号频率的20倍
        const time = Array.from({ length: samples }, (_, i) => i * timeStep);

        // 生成纯净信号
        let cleanSignal;
        switch (signalType) {
            case 'sine':
                cleanSignal = time.map(t => amplitude * Math.sin(2 * Math.PI * frequency * t));
                break;
            case 'cosine':
                cleanSignal = time.map(t => amplitude * Math.cos(2 * Math.PI * frequency * t));
                break;
            case 'square':
                cleanSignal = time.map(t => amplitude * Math.sign(Math.sin(2 * Math.PI * frequency * t)));
                break;
            case 'triangle':
                cleanSignal = time.map(t => amplitude * (2 / Math.PI) * Math.asin(Math.sin(2 * Math.PI * frequency * t)));
                break;
            case 'sawtooth':
                cleanSignal = time.map(t => amplitude * (2 * (t * frequency - Math.floor(t * frequency + 0.5))));
                break;
            case 'pulse':
                cleanSignal = time.map(t => {
                    const phase = (t * frequency) % 1;
                    return amplitude * (phase < 0.5 ? 1 : -1);
                });
                break;
            default:
                cleanSignal = time.map(t => amplitude * Math.sin(2 * Math.PI * frequency * t));
        }

        this.cleanSignal = cleanSignal;

        // 添加噪声（如果需要）
        if (addNoise) {
            this.noisySignal = cleanSignal.map(val =>
                val + noiseLevel * amplitude * (Math.random() - 0.5) * 2
            );
        } else {
            this.noisySignal = [...cleanSignal];
        }

            this.updateTimeChart();

            // 显示频率信息
            let freqText;
            if (frequency >= 1000000) {
                freqText = (frequency / 1000000).toFixed(1) + 'MHz';
            } else if (frequency >= 1000) {
                freqText = (frequency / 1000).toFixed(1) + 'kHz';
            } else {
                freqText = frequency.toFixed(1) + 'Hz';
            }

            this.showNotification(`信号生成成功: ${this.getSignalTypeName(signalType)} (${freqText}, ${amplitude}倍, ${samples}点)`, 'success');
        } catch (error) {
            console.error('信号生成失败:', error);
            this.showNotification('信号生成失败: ' + error.message, 'error');
        }
    }

    getSignalTypeName(type) {
        const names = {
            sine: '正弦波',
            cosine: '余弦波',
            square: '方波',
            triangle: '三角波',
            sawtooth: '锯齿波',
            pulse: '脉冲波'
        };
        return names[type] || '正弦波';
    }

    initNoiseConfig() {
        const noiseCheck = document.getElementById('noiseCheck');
        const noiseConfigPanel = document.getElementById('noiseConfigPanel');

        if (noiseCheck && noiseConfigPanel) {
            // 噪声配置面板控制
            noiseCheck.addEventListener('change', (e) => {
                if (e.target.checked) {
                    noiseConfigPanel.style.display = 'block';
                    // 默认选择高斯噪声
                    const gaussianNoise = document.getElementById('gaussianNoise');
                    if (gaussianNoise) {
                        gaussianNoise.checked = true;
                    }
                } else {
                    noiseConfigPanel.style.display = 'none';
                    // 清除所有噪声选择
                    document.querySelectorAll('.noise-checkbox').forEach(cb => cb.checked = false);
                }
            });
        }
    }

    applyNoise() {
        if (!this.noisySignal || this.noisySignal.length === 0) {
            this.showNotification('请先生成信号', 'warning');
            return;
        }

        // 获取选中的噪声类型
        const selectedNoiseTypes = [];
        document.querySelectorAll('.noise-checkbox:checked').forEach(cb => {
            selectedNoiseTypes.push(cb.id.replace('Noise', ''));
        });

        if (selectedNoiseTypes.length === 0) {
            this.showNotification('请选择至少一种噪声类型', 'warning');
            return;
        }

        // 获取噪声幅度
        const noiseAmpSlider = document.getElementById('noiseAmpSlider');
        const noiseAmplitude = noiseAmpSlider ? parseFloat(noiseAmpSlider.value) : 0.2;

        // 添加多种噪声
        const noisyData = this.noisySignal.map((val, i) => {
            let noiseValue = 0;

            selectedNoiseTypes.forEach(noiseType => {
                switch (noiseType) {
                    case 'gaussian':
                        // 高斯噪声 (Box-Muller变换)
                        const u1 = Math.random();
                        const u2 = Math.random();
                        const gaussian = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
                        noiseValue += gaussian * noiseAmplitude * 0.3;
                        break;

                    case 'impulse':
                        // 脉冲噪声 (椒盐噪声)
                        if (Math.random() < 0.05) { // 5%概率出现脉冲
                            noiseValue += (Math.random() > 0.5 ? 1 : -1) * noiseAmplitude * 2;
                        }
                        break;

                    case 'white':
                        // 白噪声 (均匀分布)
                        noiseValue += (Math.random() - 0.5) * noiseAmplitude;
                        break;

                    case 'flicker':
                        // 闪烁噪声 (1/f噪声)
                        const flickerFreq = 1 / (i + 1);
                        noiseValue += Math.sin(2 * Math.PI * flickerFreq * i * 0.01) * noiseAmplitude * 0.5;
                        break;

                    case 'thermal':
                        // 热噪声 (约翰逊噪声)
                        const thermal = Math.sqrt(-2 * Math.log(Math.random())) * Math.cos(2 * Math.PI * Math.random());
                        noiseValue += thermal * noiseAmplitude * 0.1;
                        break;

                    case 'colored':
                        // 色噪声 (粉红噪声近似)
                        const coloredFreq = 0.1 + 0.9 * Math.random();
                        noiseValue += Math.sin(2 * Math.PI * coloredFreq * i * 0.01) * noiseAmplitude * 0.4;
                        break;
                }
            });

            return val + noiseValue;
        });

        this.noisySignal = noisyData;
        this.updateTimeChart();

        // 显示噪声类型信息
        const noiseNames = {
            'gaussian': '高斯',
            'impulse': '脉冲',
            'white': '白',
            'flicker': '闪烁',
            'thermal': '热',
            'colored': '色'
        };
        const noiseTypeNames = selectedNoiseTypes.map(type => noiseNames[type]).join('+');
        this.showNotification(`已添加${noiseTypeNames}噪声`, 'success');
    }

    // 更新滑动条进度条效果
    updateSliderProgress(slider) {
        if (!slider) return;

        const value = slider.value;
        const min = slider.min || 0;
        const max = slider.max || 100;
        const percentage = ((value - min) / (max - min)) * 100;

        // 创建渐变背景，显示进度
        const gradient = `linear-gradient(90deg, #3b82f6 0%, #3b82f6 ${percentage}%, #e2e8f0 ${percentage}%, #e2e8f0 100%)`;
        slider.style.background = gradient;
    }

    initializeCharts() {
        try {
            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js未加载，跳过图表初始化');
                return;
            }

            // 时域图表
            const timeChart = document.getElementById('timeChart');
            if (!timeChart) {
                console.warn('时域图表元素未找到');
                return;
            }

            const timeCtx = timeChart.getContext('2d');
            this.charts.time = new Chart(timeCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false, // 让图表自适应容器
                plugins: {
                    title: {
                        display: false // 隐藏图表内标题，使用外部标题
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1e293b',
                        bodyColor: '#64748b',
                        borderColor: '#3b82f6',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true
                    },
                    zoom: {
                        wheel: {
                            enabled: true,
                            speed: 0.1
                        },
                        pinch: {
                            enabled: true
                        },
                        mode: 'xy',
                        limits: {
                            x: {min: 'original', max: 'original'},
                            y: {min: 'original', max: 'original'}
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '时间 (ms)',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(59, 130, 246, 0.1)',
                            lineWidth: 1
                        },
                        ticks: {
                            color: '#64748b',
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '幅度',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(59, 130, 246, 0.1)',
                            lineWidth: 1
                        },
                        ticks: {
                            color: '#64748b',
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.1,
                        borderWidth: 2
                    },
                    point: {
                        radius: 0,
                        hoverRadius: 4
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

            // 频域图表
            const frequencyChart = document.getElementById('frequencyChart');
            if (frequencyChart) {
                const freqCtx = frequencyChart.getContext('2d');
                this.charts.frequency = new Chart(freqCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1e293b',
                        bodyColor: '#64748b',
                        borderColor: '#3b82f6',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true
                    },
                    zoom: {
                        wheel: {
                            enabled: true,
                            speed: 0.1
                        },
                        pinch: {
                            enabled: true
                        },
                        mode: 'xy',
                        limits: {
                            x: {min: 'original', max: 'original'},
                            y: {min: 'original', max: 'original'}
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '频率 (Hz)',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(59, 130, 246, 0.1)',
                            lineWidth: 1
                        },
                        ticks: {
                            color: '#64748b',
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '幅度',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(59, 130, 246, 0.1)',
                            lineWidth: 1
                        },
                        ticks: {
                            color: '#64748b',
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.1,
                        borderWidth: 2
                    },
                    point: {
                        radius: 0,
                        hoverRadius: 4
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
                });
            }

            // 对比图表
            const comparisonChart = document.getElementById('comparisonChart');
            if (comparisonChart) {
                const compCtx = comparisonChart.getContext('2d');
                this.charts.comparison = new Chart(compCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1e293b',
                        bodyColor: '#64748b',
                        borderColor: '#3b82f6',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true
                    },
                    zoom: {
                        wheel: {
                            enabled: true,
                            speed: 0.1
                        },
                        pinch: {
                            enabled: true
                        },
                        mode: 'xy',
                        limits: {
                            x: {min: 'original', max: 'original'},
                            y: {min: 'original', max: 'original'}
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '算法',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#64748b'
                        },
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#64748b',
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '评估指标',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            color: '#64748b'
                        },
                        grid: {
                            color: 'rgba(59, 130, 246, 0.1)',
                            lineWidth: 1
                        },
                        ticks: {
                            color: '#64748b',
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                elements: {
                    bar: {
                        borderRadius: 6,
                        borderSkipped: false
                    }
                }
            }
                });
            }
        } catch (error) {
            console.error('图表初始化失败:', error);
            // 不抛出错误，允许页面继续加载
        }
    }

    updateTimeChart() {
        if (!this.noisySignal) return;

        // 确保图表已初始化
        if (!this.charts || !this.charts.time) {
            console.warn('时域图表未初始化，尝试重新初始化...');
            this.initializeCharts();
            if (!this.charts || !this.charts.time) {
                console.error('时域图表初始化失败');
                return;
            }
        }

        const timeLabels = Array.from({ length: this.noisySignal.length }, (_, i) => i);
        const datasets = [];

        // 含噪信号
        datasets.push({
            label: '含噪信号',
            data: this.noisySignal,
            borderColor: 'rgba(239, 68, 68, 0.8)',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            borderWidth: 1,
            pointRadius: 0
        });

        // 纯净信号（如果存在）
        if (this.cleanSignal) {
            datasets.push({
                label: '纯净信号',
                data: this.cleanSignal,
                borderColor: 'rgba(34, 197, 94, 0.8)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                borderWidth: 2,
                pointRadius: 0
            });
        }

        // 去噪后信号
        Object.keys(this.denoisedSignals).forEach((algorithm, index) => {
            const colors = [
                'rgba(59, 130, 246, 0.8)',
                'rgba(147, 51, 234, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(236, 72, 153, 0.8)',
                'rgba(14, 165, 233, 0.8)'
            ];

            datasets.push({
                label: `${algorithm}去噪`,
                data: this.denoisedSignals[algorithm],
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length].replace('0.8', '0.1'),
                borderWidth: 2,
                pointRadius: 0
            });
        });

        try {
            this.charts.time.data.labels = timeLabels;
            this.charts.time.data.datasets = datasets;
            this.charts.time.update();
        } catch (error) {
            console.error('更新时域图表失败:', error);
            // 尝试重新初始化图表
            this.initializeCharts();
        }
    }

    async executeDenoising() {
        if (!this.noisySignal) {
            this.showNotification('请先导入或生成信号数据', 'error');
            return;
        }

        this.denoisedSignals = {};
        this.metrics = {};

        // 检查选中的算法
        const algorithms = [
            { id: 'movingAverage', name: '平均滑动', method: this.movingAverageFilter, apiMethod: 'filter' },
            { id: 'sgFilter', name: 'SG滤波', method: this.savitzkyGolayFilter, apiMethod: 'filter' },
            { id: 'svdDenoise', name: 'SVD降噪', method: this.svdDenoise, apiMethod: 'denoise' },
            { id: 'convolution', name: '卷积滑动', method: this.convolutionFilter, apiMethod: 'filter' },
            { id: 'emdDenoise', name: 'EMD分解', method: this.emdDenoise, apiMethod: 'denoise' },
            { id: 'waveletDenoise', name: '小波去噪', method: this.waveletDenoise, apiMethod: 'denoise' }
        ];

        let hasSelectedAlgorithm = false;
        let processedCount = 0;

        this.showNotification('开始执行去噪处理...', 'info');

        for (const algo of algorithms) {
            const checkbox = document.getElementById(algo.id);
            if (checkbox && checkbox.checked) {
                hasSelectedAlgorithm = true;

                try {
                    let denoisedResult = null;
                    let useLocalMethod = false;

                    // 尝试使用后端API
                    if (window.SignalAPI && algo.apiMethod) {
                        try {
                            this.showNotification(`正在使用后端API执行${algo.name}...`, 'info');

                            let apiParams = {
                                data: this.noisySignal,
                                method: this.getAPIMethodName(algo.id),
                                sampleRate: 1000
                            };

                            // 根据算法类型添加特定参数
                            if (algo.id === 'movingAverage') {
                                apiParams.filterType = 'lowpass';
                                apiParams.cutoffFreq = 50;
                            } else if (algo.id === 'waveletDenoise') {
                                apiParams.waveletType = 'db4';
                                apiParams.levels = 4;
                                apiParams.threshold = parseFloat(document.getElementById('threshold')?.value || '0.1');
                            } else if (algo.id === 'sgFilter') {
                                apiParams.filterType = 'savgol';
                                apiParams.order = 3;
                            }

                            let apiResult;
                            if (algo.apiMethod === 'denoise') {
                                apiResult = await window.SignalAPI.denoiseSignal(apiParams);
                            } else if (algo.apiMethod === 'filter') {
                                apiResult = await window.SignalAPI.filterSignal(apiParams);
                            }

                            if (apiResult && apiResult.success) {
                                denoisedResult = apiResult.data.denoisedData || apiResult.data.filteredData;
                                this.showNotification(`${algo.name} API处理完成`, 'success');
                            }
                        } catch (apiError) {
                            console.warn(`${algo.name} API处理失败，使用本地方法:`, apiError);
                            useLocalMethod = true;
                        }
                    } else {
                        useLocalMethod = true;
                    }

                    // 如果API失败或不可用，使用本地方法
                    if (!denoisedResult || useLocalMethod) {
                        this.showNotification(`使用本地方法执行${algo.name}...`, 'info');
                        denoisedResult = algo.method.call(this, this.noisySignal);
                    }

                    if (denoisedResult) {
                        this.denoisedSignals[algo.name] = denoisedResult;
                        this.calculateMetrics(algo.name, denoisedResult);
                        processedCount++;
                    }

                } catch (error) {
                    console.error(`${algo.name}算法执行失败:`, error);
                    this.showNotification(`${algo.name}算法执行失败: ${error.message}`, 'error');
                }
            }
        }

        if (!hasSelectedAlgorithm) {
            this.showNotification('请至少选择一种去噪算法', 'error');
            return;
        }

        if (processedCount === 0) {
            this.showNotification('所有算法执行失败', 'error');
            return;
        }

        this.updateTimeChart();
        this.updateFrequencyChart();
        this.updateMetricsDisplay();
        this.updateComparisonChart();
        this.showNotification(`去噪处理完成，成功处理${processedCount}种算法`, 'success');
    }

    // 获取API方法名称映射
    getAPIMethodName(algorithmId) {
        const methodMap = {
            'movingAverage': 'moving_average',
            'sgFilter': 'savgol',
            'svdDenoise': 'svd',
            'convolution': 'convolution',
            'emdDenoise': 'emd',
            'waveletDenoise': 'wavelet'
        };
        return methodMap[algorithmId] || 'wavelet';
    }

    // 平均滑动滤波
    movingAverageFilter(signal) {
        const windowSize = parseInt(document.getElementById('windowSize').value);
        const filtered = [];
        const halfWindow = Math.floor(windowSize / 2);

        for (let i = 0; i < signal.length; i++) {
            let sum = 0;
            let count = 0;

            for (let j = Math.max(0, i - halfWindow); j <= Math.min(signal.length - 1, i + halfWindow); j++) {
                sum += signal[j];
                count++;
            }

            filtered[i] = sum / count;
        }

        return filtered;
    }

    // Savitzky-Golay滤波
    savitzkyGolayFilter(signal) {
        const windowSize = parseInt(document.getElementById('sgWindow').value);
        const order = parseInt(document.getElementById('sgOrder').value);

        // 确保窗口大小为奇数
        const actualWindowSize = windowSize % 2 === 0 ? windowSize + 1 : windowSize;
        const halfWindow = Math.floor(actualWindowSize / 2);

        // 简化的SG滤波实现
        const filtered = [...signal];

        for (let i = halfWindow; i < signal.length - halfWindow; i++) {
            let sum = 0;
            let weightSum = 0;

            for (let j = -halfWindow; j <= halfWindow; j++) {
                // 简化的权重计算
                const weight = 1 - Math.abs(j) / (halfWindow + 1);
                sum += signal[i + j] * weight;
                weightSum += weight;
            }

            filtered[i] = sum / weightSum;
        }

        return filtered;
    }

    // SVD降噪
    svdDenoise(signal) {
        const ratio = parseFloat(document.getElementById('svdRatio').value);

        // 构建Hankel矩阵
        const L = Math.floor(signal.length / 2);
        const K = signal.length - L + 1;
        const hankel = [];

        for (let i = 0; i < L; i++) {
            hankel[i] = [];
            for (let j = 0; j < K; j++) {
                hankel[i][j] = signal[i + j];
            }
        }

        // 简化的SVD实现（使用主成分分析近似）
        const filtered = [...signal];

        // 应用低通滤波作为SVD的近似
        const cutoff = Math.floor(signal.length * ratio);
        const fft = this.fft(signal);

        // 保留低频成分
        for (let i = cutoff; i < fft.length - cutoff; i++) {
            fft[i] = { real: 0, imag: 0 };
        }

        const ifftResult = this.ifft(fft);
        return ifftResult.map(c => c.real);
    }

    // 卷积滑动平均滤波
    convolutionFilter(signal) {
        const kernelSize = parseInt(document.getElementById('kernelSize').value);

        // 创建高斯核
        const kernel = [];
        const sigma = kernelSize / 3;
        const center = Math.floor(kernelSize / 2);
        let sum = 0;

        for (let i = 0; i < kernelSize; i++) {
            const x = i - center;
            const value = Math.exp(-(x * x) / (2 * sigma * sigma));
            kernel[i] = value;
            sum += value;
        }

        // 归一化核
        for (let i = 0; i < kernelSize; i++) {
            kernel[i] /= sum;
        }

        // 执行卷积
        const filtered = [];
        const halfKernel = Math.floor(kernelSize / 2);

        for (let i = 0; i < signal.length; i++) {
            let convSum = 0;

            for (let j = 0; j < kernelSize; j++) {
                const signalIndex = i - halfKernel + j;
                if (signalIndex >= 0 && signalIndex < signal.length) {
                    convSum += signal[signalIndex] * kernel[j];
                }
            }

            filtered[i] = convSum;
        }

        return filtered;
    }

    // EMD分解降噪（简化实现）
    emdDenoise(signal) {
        const depth = parseInt(document.getElementById('emdDepth').value);

        // 简化的EMD实现
        let residue = [...signal];
        const imfs = [];

        for (let i = 0; i < depth; i++) {
            const imf = this.extractIMF(residue);
            imfs.push(imf);

            // 计算残余
            for (let j = 0; j < residue.length; j++) {
                residue[j] -= imf[j];
            }
        }

        // 重构信号，去除高频噪声（前几个IMF）
        const filtered = new Array(signal.length).fill(0);

        // 只保留低频成分
        const keepIMFs = Math.max(1, Math.floor(depth * 0.6));
        for (let i = keepIMFs; i < imfs.length; i++) {
            for (let j = 0; j < signal.length; j++) {
                filtered[j] += imfs[i][j];
            }
        }

        // 添加残余
        for (let j = 0; j < signal.length; j++) {
            filtered[j] += residue[j];
        }

        return filtered;
    }

    // 小波去噪（简化实现）
    waveletDenoise(signal) {
        const levels = parseInt(document.getElementById('waveletLevels').value);
        const thresholdMethod = document.getElementById('thresholdMethod').value;
        const thresholdFactor = parseFloat(document.getElementById('thresholdFactor').value);

        // 简化的小波变换实现
        // 这里使用Haar小波作为示例
        let coeffs = [...signal];
        const length = coeffs.length;

        // 前向小波变换（多层分解）
        for (let level = 0; level < levels; level++) {
            const currentLength = Math.floor(length / Math.pow(2, level));
            if (currentLength < 2) break;

            const temp = new Array(currentLength);

            // Haar小波变换
            for (let i = 0; i < currentLength / 2; i++) {
                const sum = coeffs[2 * i] + coeffs[2 * i + 1];
                const diff = coeffs[2 * i] - coeffs[2 * i + 1];
                temp[i] = sum / Math.sqrt(2); // 低频系数
                temp[i + currentLength / 2] = diff / Math.sqrt(2); // 高频系数
            }

            // 复制回原数组
            for (let i = 0; i < currentLength; i++) {
                coeffs[i] = temp[i];
            }
        }

        // 阈值处理
        const threshold = thresholdFactor * Math.max(...coeffs.map(Math.abs));

        for (let i = 0; i < coeffs.length; i++) {
            if (thresholdMethod === 'soft') {
                // 软阈值
                if (Math.abs(coeffs[i]) <= threshold) {
                    coeffs[i] = 0;
                } else {
                    coeffs[i] = Math.sign(coeffs[i]) * (Math.abs(coeffs[i]) - threshold);
                }
            } else {
                // 硬阈值
                if (Math.abs(coeffs[i]) <= threshold) {
                    coeffs[i] = 0;
                }
            }
        }

        // 反向小波变换（重构）
        for (let level = levels - 1; level >= 0; level--) {
            const currentLength = Math.floor(length / Math.pow(2, level));
            if (currentLength < 2) continue;

            const temp = new Array(currentLength);

            // Haar小波反变换
            for (let i = 0; i < currentLength / 2; i++) {
                const low = coeffs[i];
                const high = coeffs[i + currentLength / 2];
                temp[2 * i] = (low + high) / Math.sqrt(2);
                temp[2 * i + 1] = (low - high) / Math.sqrt(2);
            }

            // 复制回原数组
            for (let i = 0; i < currentLength; i++) {
                coeffs[i] = temp[i];
            }
        }

        return coeffs.slice(0, signal.length);
    }

    // 提取本征模态函数（简化实现）
    extractIMF(signal) {
        const imf = [...signal];
        const iterations = 10;

        for (let iter = 0; iter < iterations; iter++) {
            const maxima = this.findLocalMaxima(imf);
            const minima = this.findLocalMinima(imf);

            if (maxima.length < 2 || minima.length < 2) break;

            const upperEnvelope = this.interpolate(maxima, imf.length);
            const lowerEnvelope = this.interpolate(minima, imf.length);

            // 计算均值包络
            for (let i = 0; i < imf.length; i++) {
                const mean = (upperEnvelope[i] + lowerEnvelope[i]) / 2;
                imf[i] -= mean;
            }
        }

        return imf;
    }

    findLocalMaxima(signal) {
        const maxima = [];
        for (let i = 1; i < signal.length - 1; i++) {
            if (signal[i] > signal[i - 1] && signal[i] > signal[i + 1]) {
                maxima.push({ index: i, value: signal[i] });
            }
        }
        return maxima;
    }

    findLocalMinima(signal) {
        const minima = [];
        for (let i = 1; i < signal.length - 1; i++) {
            if (signal[i] < signal[i - 1] && signal[i] < signal[i + 1]) {
                minima.push({ index: i, value: signal[i] });
            }
        }
        return minima;
    }

    interpolate(points, length) {
        const result = new Array(length);

        if (points.length === 0) {
            return result.fill(0);
        }

        if (points.length === 1) {
            return result.fill(points[0].value);
        }

        // 线性插值
        for (let i = 0; i < length; i++) {
            if (i <= points[0].index) {
                result[i] = points[0].value;
            } else if (i >= points[points.length - 1].index) {
                result[i] = points[points.length - 1].value;
            } else {
                // 找到相邻的两个点
                let leftPoint = points[0];
                let rightPoint = points[points.length - 1];

                for (let j = 0; j < points.length - 1; j++) {
                    if (points[j].index <= i && points[j + 1].index >= i) {
                        leftPoint = points[j];
                        rightPoint = points[j + 1];
                        break;
                    }
                }

                // 线性插值
                const t = (i - leftPoint.index) / (rightPoint.index - leftPoint.index);
                result[i] = leftPoint.value + t * (rightPoint.value - leftPoint.value);
            }
        }

        return result;
    }

    showNotification(message, type = 'info') {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            transition: all 0.3s ease;
        `;

        switch (type) {
            case 'success':
                notification.style.background = 'linear-gradient(135deg, #22c55e, #16a34a)';
                break;
            case 'error':
                notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
                break;
            default:
                notification.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // FFT实现
    fft(signal) {
        const N = signal.length;
        if (N <= 1) return signal.map(x => ({ real: x, imag: 0 }));

        // 确保长度为2的幂
        const nextPowerOf2 = Math.pow(2, Math.ceil(Math.log2(N)));
        const paddedSignal = [...signal];
        while (paddedSignal.length < nextPowerOf2) {
            paddedSignal.push(0);
        }

        return this.fftRecursive(paddedSignal.map(x => ({ real: x, imag: 0 })));
    }

    fftRecursive(x) {
        const N = x.length;
        if (N <= 1) return x;

        // 分治
        const even = [];
        const odd = [];
        for (let i = 0; i < N; i++) {
            if (i % 2 === 0) {
                even.push(x[i]);
            } else {
                odd.push(x[i]);
            }
        }

        const evenFFT = this.fftRecursive(even);
        const oddFFT = this.fftRecursive(odd);

        const result = new Array(N);
        for (let k = 0; k < N / 2; k++) {
            const t = {
                real: Math.cos(-2 * Math.PI * k / N) * oddFFT[k].real - Math.sin(-2 * Math.PI * k / N) * oddFFT[k].imag,
                imag: Math.sin(-2 * Math.PI * k / N) * oddFFT[k].real + Math.cos(-2 * Math.PI * k / N) * oddFFT[k].imag
            };

            result[k] = {
                real: evenFFT[k].real + t.real,
                imag: evenFFT[k].imag + t.imag
            };

            result[k + N / 2] = {
                real: evenFFT[k].real - t.real,
                imag: evenFFT[k].imag - t.imag
            };
        }

        return result;
    }

    // IFFT实现
    ifft(X) {
        const N = X.length;

        // 共轭
        const conjugated = X.map(x => ({ real: x.real, imag: -x.imag }));

        // FFT
        const result = this.fftRecursive(conjugated);

        // 共轭并除以N
        return result.map(x => ({ real: x.real / N, imag: -x.imag / N }));
    }

    // 计算评估指标
    calculateMetrics(algorithmName, denoisedSignal) {
        if (!this.cleanSignal) {
            // 如果没有纯净信号，只计算基本统计指标
            this.metrics[algorithmName] = {
                mean: this.calculateMean(denoisedSignal),
                std: this.calculateStd(denoisedSignal),
                variance: this.calculateVariance(denoisedSignal)
            };
            return;
        }

        const snr = this.calculateSNR(this.cleanSignal, denoisedSignal);
        const ncc = this.calculateNCC(this.cleanSignal, denoisedSignal);
        const mse = this.calculateMSE(this.cleanSignal, denoisedSignal);
        const rmse = Math.sqrt(mse);

        this.metrics[algorithmName] = {
            snr: snr,
            ncc: ncc,
            mse: mse,
            rmse: rmse
        };
    }

    // 信噪比计算
    calculateSNR(cleanSignal, noisySignal) {
        const signalPower = cleanSignal.reduce((sum, val) => sum + val * val, 0) / cleanSignal.length;
        const noise = cleanSignal.map((clean, i) => noisySignal[i] - clean);
        const noisePower = noise.reduce((sum, val) => sum + val * val, 0) / noise.length;

        if (noisePower === 0) return Infinity;
        return 10 * Math.log10(signalPower / noisePower);
    }

    // 归一化互相关系数
    calculateNCC(signal1, signal2) {
        const mean1 = this.calculateMean(signal1);
        const mean2 = this.calculateMean(signal2);

        let numerator = 0;
        let denominator1 = 0;
        let denominator2 = 0;

        for (let i = 0; i < signal1.length; i++) {
            const diff1 = signal1[i] - mean1;
            const diff2 = signal2[i] - mean2;

            numerator += diff1 * diff2;
            denominator1 += diff1 * diff1;
            denominator2 += diff2 * diff2;
        }

        const denominator = Math.sqrt(denominator1 * denominator2);
        return denominator === 0 ? 0 : numerator / denominator;
    }

    // 均方误差
    calculateMSE(signal1, signal2) {
        let sum = 0;
        for (let i = 0; i < signal1.length; i++) {
            const diff = signal1[i] - signal2[i];
            sum += diff * diff;
        }
        return sum / signal1.length;
    }

    // 计算均值
    calculateMean(signal) {
        return signal.reduce((sum, val) => sum + val, 0) / signal.length;
    }

    // 计算标准差
    calculateStd(signal) {
        const mean = this.calculateMean(signal);
        const variance = signal.reduce((sum, val) => sum + (val - mean) * (val - mean), 0) / signal.length;
        return Math.sqrt(variance);
    }

    // 计算方差
    calculateVariance(signal) {
        const mean = this.calculateMean(signal);
        return signal.reduce((sum, val) => sum + (val - mean) * (val - mean), 0) / signal.length;
    }

    // 更新频域图表
    updateFrequencyChart() {
        if (!this.noisySignal) return;

        const datasets = [];
        const sampleRate = 1000; // 假设采样率为1000Hz
        const freqLabels = Array.from({ length: Math.floor(this.noisySignal.length / 2) },
            (_, i) => i * sampleRate / this.noisySignal.length);

        // 含噪信号频谱
        const noisyFFT = this.fft(this.noisySignal);
        const noisyMagnitude = noisyFFT.slice(0, Math.floor(noisyFFT.length / 2))
            .map(c => Math.sqrt(c.real * c.real + c.imag * c.imag));

        datasets.push({
            label: '含噪信号频谱',
            data: noisyMagnitude,
            borderColor: 'rgba(239, 68, 68, 0.8)',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            borderWidth: 1,
            pointRadius: 0
        });

        // 纯净信号频谱
        if (this.cleanSignal) {
            const cleanFFT = this.fft(this.cleanSignal);
            const cleanMagnitude = cleanFFT.slice(0, Math.floor(cleanFFT.length / 2))
                .map(c => Math.sqrt(c.real * c.real + c.imag * c.imag));

            datasets.push({
                label: '纯净信号频谱',
                data: cleanMagnitude,
                borderColor: 'rgba(34, 197, 94, 0.8)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                borderWidth: 2,
                pointRadius: 0
            });
        }

        // 去噪后信号频谱
        Object.keys(this.denoisedSignals).forEach((algorithm, index) => {
            const colors = [
                'rgba(59, 130, 246, 0.8)',
                'rgba(147, 51, 234, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(236, 72, 153, 0.8)',
                'rgba(14, 165, 233, 0.8)'
            ];

            const denoisedFFT = this.fft(this.denoisedSignals[algorithm]);
            const denoisedMagnitude = denoisedFFT.slice(0, Math.floor(denoisedFFT.length / 2))
                .map(c => Math.sqrt(c.real * c.real + c.imag * c.imag));

            datasets.push({
                label: `${algorithm}频谱`,
                data: denoisedMagnitude,
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length].replace('0.8', '0.1'),
                borderWidth: 2,
                pointRadius: 0
            });
        });

        this.charts.frequency.data.labels = freqLabels;
        this.charts.frequency.data.datasets = datasets;
        this.charts.frequency.update();
    }

    // 更新评估指标显示
    updateMetricsDisplay() {
        const metricsGrid = document.getElementById('metricsGrid');
        metricsGrid.innerHTML = '';

        Object.keys(this.metrics).forEach(algorithm => {
            const metrics = this.metrics[algorithm];

            Object.keys(metrics).forEach(metricName => {
                const metricCard = document.createElement('div');
                metricCard.className = 'metric-card';

                const value = metrics[metricName];
                const displayValue = typeof value === 'number' ? value.toFixed(4) : value;

                metricCard.innerHTML = `
                    <h4>${algorithm} - ${this.getMetricDisplayName(metricName)}</h4>
                    <div class="value">${displayValue}<span class="unit">${this.getMetricUnit(metricName)}</span></div>
                `;

                metricsGrid.appendChild(metricCard);
            });
        });
    }

    getMetricDisplayName(metricName) {
        const names = {
            snr: '信噪比',
            ncc: '相关系数',
            mse: '均方误差',
            rmse: '均方根误差',
            mean: '均值',
            std: '标准差',
            variance: '方差'
        };
        return names[metricName] || metricName;
    }

    getMetricUnit(metricName) {
        const units = {
            snr: 'dB',
            ncc: '',
            mse: '',
            rmse: '',
            mean: '',
            std: '',
            variance: ''
        };
        return units[metricName] || '';
    }

    // 更新对比图表
    updateComparisonChart() {
        if (Object.keys(this.metrics).length === 0) return;

        const algorithms = Object.keys(this.metrics);
        const metricTypes = this.cleanSignal ? ['snr', 'ncc', 'mse'] : ['mean', 'std'];

        const datasets = metricTypes.map((metricType, index) => {
            const colors = [
                'rgba(59, 130, 246, 0.8)',
                'rgba(34, 197, 94, 0.8)',
                'rgba(239, 68, 68, 0.8)',
                'rgba(147, 51, 234, 0.8)'
            ];

            return {
                label: this.getMetricDisplayName(metricType),
                data: algorithms.map(algo => this.metrics[algo][metricType] || 0),
                backgroundColor: colors[index % colors.length],
                borderColor: colors[index % colors.length],
                borderWidth: 2
            };
        });

        this.charts.comparison.data.labels = algorithms;
        this.charts.comparison.data.datasets = datasets;
        this.charts.comparison.update();
    }

    // 清除结果
    clearResults() {
        this.denoisedSignals = {};
        this.metrics = {};

        this.updateTimeChart();
        this.updateFrequencyChart();
        this.updateMetricsDisplay();
        this.updateComparisonChart();

        this.showNotification('结果已清除', 'info');
    }

    // 导出数据
    exportData() {
        if (!this.noisySignal) {
            this.showNotification('没有可导出的数据', 'error');
            return;
        }

        const data = [];
        const headers = ['时间', '含噪信号'];

        if (this.cleanSignal) {
            headers.push('纯净信号');
        }

        Object.keys(this.denoisedSignals).forEach(algorithm => {
            headers.push(`${algorithm}去噪`);
        });

        data.push(headers);

        for (let i = 0; i < this.noisySignal.length; i++) {
            const row = [i, this.noisySignal[i]];

            if (this.cleanSignal) {
                row.push(this.cleanSignal[i]);
            }

            Object.keys(this.denoisedSignals).forEach(algorithm => {
                row.push(this.denoisedSignals[algorithm][i]);
            });

            data.push(row);
        }

        this.downloadCSV(data, 'signal_denoise_data.csv');
        this.showNotification('数据导出成功', 'success');
    }

    // 从结果导出面板导出所有图表
    exportAllChartsFromResultPanel() {
        console.log('从结果导出面板导出所有图表');

        // 确保图表已初始化
        if (!this.charts) {
            console.log('图表对象未初始化，尝试重新初始化...');
            this.initializeCharts();

            setTimeout(() => {
                this.exportAllChartsFromResultPanel();
            }, 500);
            return;
        }

        // 定义要导出的三个图表
        const chartsToExport = [
            { id: 'timeChart', name: '时域波形', chartKey: 'time' },
            { id: 'frequencyChart', name: '频谱分析', chartKey: 'frequency' },
            { id: 'comparisonChart', name: '算法对比', chartKey: 'comparison' }
        ];

        const timestamp = new Date().getTime();
        let exportedCount = 0;
        const totalCharts = chartsToExport.length;

        this.showNotification(`开始导出${totalCharts}个图表...`, 'info');

        chartsToExport.forEach((chartInfo, index) => {
            setTimeout(() => {
                try {
                    const canvas = document.getElementById(chartInfo.id);
                    if (!canvas) {
                        console.warn(`图表元素未找到: ${chartInfo.id}`);
                        exportedCount++;
                        if (exportedCount === totalCharts) {
                            this.showNotification(`导出完成！成功导出${exportedCount}个图表`, 'success');
                        }
                        return;
                    }

                    // 创建临时画布，添加白色背景
                    const tempCanvas = document.createElement('canvas');
                    const tempCtx = tempCanvas.getContext('2d');
                    tempCanvas.width = canvas.width;
                    tempCanvas.height = canvas.height;

                    // 填充白色背景
                    tempCtx.fillStyle = '#ffffff';
                    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                    // 绘制图表
                    tempCtx.drawImage(canvas, 0, 0);

                    // 创建下载链接
                    const link = document.createElement('a');
                    link.download = `${chartInfo.name}_${timestamp}.png`;
                    link.href = tempCanvas.toDataURL('image/png', 1.0);

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    exportedCount++;

                    // 当所有图表都导出完成时显示成功消息
                    if (exportedCount === totalCharts) {
                        this.showNotification(`导出完成！成功导出${exportedCount}个图表`, 'success');
                    }
                } catch (error) {
                    console.error(`导出${chartInfo.name}失败:`, error);
                    exportedCount++;
                    if (exportedCount === totalCharts) {
                        this.showNotification(`导出完成，部分图表可能导出失败`, 'warning');
                    }
                }
            }, index * 300); // 每个图表间隔300ms导出
        });
    }

    // 导出图表（保留原方法作为备用）
    exportChart() {
        const canvas = document.getElementById('timeChart');
        const link = document.createElement('a');
        link.download = 'signal_denoise_chart.png';
        link.href = canvas.toDataURL();
        link.click();
        this.showNotification('图表导出成功', 'success');
    }

    // 导出评估指标
    exportMetrics() {
        if (Object.keys(this.metrics).length === 0) {
            this.showNotification('没有可导出的评估指标', 'error');
            return;
        }

        const data = [];
        const algorithms = Object.keys(this.metrics);
        const metricNames = Object.keys(this.metrics[algorithms[0]]);

        // 添加标题行
        data.push(['算法', ...metricNames.map(name => this.getMetricDisplayName(name))]);

        // 添加数据行
        algorithms.forEach(algorithm => {
            const row = [algorithm];
            metricNames.forEach(metricName => {
                const value = this.metrics[algorithm][metricName];
                row.push(typeof value === 'number' ? value.toFixed(6) : value);
            });
            data.push(row);
        });

        this.downloadCSV(data, 'signal_denoise_metrics.csv');
        this.showNotification('评估指标导出成功', 'success');
    }

    // 生成报告
    exportReport() {
        if (!this.noisySignal) {
            this.showNotification('没有可生成报告的数据', 'error');
            return;
        }

        const report = this.generateReport();
        this.downloadText(report, 'signal_denoise_report.md');
        this.showNotification('报告生成成功', 'success');
    }

    generateReport() {
        const timestamp = new Date().toLocaleString('zh-CN');
        let report = `# 信号去噪分析报告\n\n`;
        report += `**生成时间**: ${timestamp}\n\n`;

        // 数据信息
        report += `## 数据信息\n\n`;
        report += `- 信号长度: ${this.noisySignal.length} 个采样点\n`;
        if (this.cleanSignal) {
            report += `- 包含纯净信号参考\n`;
        }
        report += `\n`;

        // 使用的算法
        if (Object.keys(this.denoisedSignals).length > 0) {
            report += `## 使用的去噪算法\n\n`;
            Object.keys(this.denoisedSignals).forEach(algorithm => {
                report += `- ${algorithm}\n`;
            });
            report += `\n`;
        }

        // 评估指标
        if (Object.keys(this.metrics).length > 0) {
            report += `## 评估指标\n\n`;

            Object.keys(this.metrics).forEach(algorithm => {
                report += `### ${algorithm}\n\n`;
                const metrics = this.metrics[algorithm];

                Object.keys(metrics).forEach(metricName => {
                    const value = metrics[metricName];
                    const displayValue = typeof value === 'number' ? value.toFixed(6) : value;
                    const unit = this.getMetricUnit(metricName);
                    report += `- ${this.getMetricDisplayName(metricName)}: ${displayValue}${unit}\n`;
                });

                report += `\n`;
            });
        }

        // 算法参数
        report += `## 算法参数设置\n\n`;

        if (document.getElementById('movingAverage').checked) {
            const windowSize = document.getElementById('windowSize').value;
            report += `### 平均滑动滤波\n- 窗口长度: ${windowSize}\n\n`;
        }

        if (document.getElementById('sgFilter').checked) {
            const sgWindow = document.getElementById('sgWindow').value;
            const sgOrder = document.getElementById('sgOrder').value;
            report += `### SG滤波\n- 窗口长度: ${sgWindow}\n- 多项式阶次: ${sgOrder}\n\n`;
        }

        if (document.getElementById('svdDenoise').checked) {
            const svdRatio = document.getElementById('svdRatio').value;
            report += `### SVD降噪\n- 保留比例: ${svdRatio}\n\n`;
        }

        if (document.getElementById('convolution').checked) {
            const kernelSize = document.getElementById('kernelSize').value;
            report += `### 卷积滑动平均\n- 卷积核大小: ${kernelSize}\n\n`;
        }

        if (document.getElementById('emdDenoise').checked) {
            const emdDepth = document.getElementById('emdDepth').value;
            report += `### EMD分解\n- 分解深度: ${emdDepth}\n\n`;
        }

        // 结论
        if (this.cleanSignal && Object.keys(this.metrics).length > 0) {
            report += `## 分析结论\n\n`;

            // 找出最佳算法
            let bestAlgorithm = '';
            let bestSNR = -Infinity;

            Object.keys(this.metrics).forEach(algorithm => {
                if (this.metrics[algorithm].snr && this.metrics[algorithm].snr > bestSNR) {
                    bestSNR = this.metrics[algorithm].snr;
                    bestAlgorithm = algorithm;
                }
            });

            if (bestAlgorithm) {
                report += `根据信噪比评估，**${bestAlgorithm}** 算法在本次去噪任务中表现最佳，`;
                report += `信噪比达到 ${bestSNR.toFixed(2)} dB。\n\n`;
            }
        }

        report += `---\n*本报告由信号去噪分析平台自动生成*`;

        return report;
    }

    // 下载CSV文件
    downloadCSV(data, filename) {
        const csvContent = data.map(row =>
            row.map(cell =>
                typeof cell === 'string' && cell.includes(',') ? `"${cell}"` : cell
            ).join(',')
        ).join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    // 下载文本文件
    downloadText(content, filename) {
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面开始加载...');

    try {
        // 页面加载动画
        setTimeout(() => {
            const pageLoader = document.getElementById('pageLoader');
            if (pageLoader) {
                pageLoader.classList.add('loaded');
                console.log('页面加载动画完成');
            }
        }, 1500);

        // 初始化信号去噪器
        console.log('初始化信号去噪器...');
        window.signalDenoiser = new SignalDenoiser();
        console.log('信号去噪器初始化完成');

        // 初始化导航功能
        console.log('初始化导航功能...');
        initializeNavigation();

        // 初始化背景效果
        console.log('初始化背景效果...');
        initializeBackgroundEffects();

        console.log('所有初始化完成');
    } catch (error) {
        console.error('初始化过程中发生错误:', error);

        // 强制隐藏加载动画
        setTimeout(() => {
            const pageLoader = document.getElementById('pageLoader');
            if (pageLoader) {
                pageLoader.classList.add('loaded');
            }
        }, 2000);

        // 显示错误信息
        alert('页面初始化失败，请刷新页面重试。错误信息: ' + error.message);
    }
});

// 导航功能
function initializeNavigation() {
    // 分类展开/收起
    document.querySelectorAll('.category-header').forEach(header => {
        header.addEventListener('click', () => {
            const category = header.closest('.nav-category');
            const isExpanded = category.classList.contains('expanded');

            if (isExpanded) {
                category.classList.remove('expanded');
                header.classList.remove('expanded');
            } else {
                category.classList.add('expanded');
                header.classList.add('expanded');
            }
        });
    });

    // 导航链接点击事件
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            // 如果链接有data-page属性，说明是页面跳转链接，不阻止默认行为
            if (link.hasAttribute('data-page')) {
                console.log('页面跳转链接被点击:', link.getAttribute('href'));
                // 直接跳转，不阻止默认行为
                return;
            }

            // 如果链接有data-function属性，处理功能调用
            if (link.hasAttribute('data-function')) {
                e.preventDefault();
                console.log('功能链接被点击:', link.getAttribute('data-function'));
                // 这里可以添加具体的功能处理逻辑
            }
        });
    });
}

// 背景效果
function initializeBackgroundEffects() {
    // 创建粒子效果
    const particlesContainer = document.getElementById('particles');
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);
    }
}

// 扩展SignalDenoiser类，添加图表操作方法
SignalDenoiser.prototype.initChartActions = function() {
    const self = this; // 保存this上下文

    // 使用事件委托绑定到document，确保能捕获所有点击
    document.addEventListener('click', function(e) {
        // 检查点击的元素是否是图表按钮
        if (e.target.classList.contains('chart-btn') || e.target.closest('.chart-btn')) {
            const btn = e.target.classList.contains('chart-btn') ? e.target : e.target.closest('.chart-btn');

            e.preventDefault();
            e.stopPropagation();

            const action = btn.dataset.action;
            const chartType = btn.dataset.chart;

            if (!action || !chartType) {
                console.error('Button missing data attributes:', btn);
                self.showNotification('按钮配置错误', 'error');
                return;
            }

            console.log('图表按钮点击:', action, chartType);

            // 处理不同的操作
            switch (action) {
                case 'zoom-x-in':
                    self.zoomChart(chartType, 'x', 'in');
                    break;
                case 'zoom-x-out':
                    self.zoomChart(chartType, 'x', 'out');
                    break;
                case 'zoom-y-in':
                    self.zoomChart(chartType, 'y', 'in');
                    break;
                case 'zoom-y-out':
                    self.zoomChart(chartType, 'y', 'out');
                    break;
                case 'auto':
                    self.autoScaleChart(chartType);
                    break;
                case 'export':
                    self.exportChart(chartType, 'png');
                    break;
                case 'export-jpg':
                    self.exportChart(chartType, 'jpg');
                    break;
                case 'export-excel':
                    self.exportChart(chartType, 'excel');
                    break;
                case 'fullscreen':
                    self.toggleFullscreen(chartType);
                    break;
                default:
                    console.warn('Unknown action:', action);
            }
        }
    });
};

// 图表缩放功能
SignalDenoiser.prototype.zoomChart = function(chartType, axis, direction) {
    const chart = this.charts[chartType];

    if (!chart) {
        this.showNotification(`图表未找到，请先生成信号`, 'warning');
        return;
    }

    if (!chart.options || !chart.options.scales) {
        this.showNotification('图表不支持缩放功能', 'warning');
        return;
    }

    const scale = chart.options.scales[axis];
    if (!scale) return;

    const zoomFactor = direction === 'in' ? 0.8 : 1.25;

    if (scale.min !== undefined && scale.max !== undefined) {
        const range = scale.max - scale.min;
        const center = (scale.min + scale.max) / 2;
        const newRange = range * zoomFactor;

        scale.min = center - newRange / 2;
        scale.max = center + newRange / 2;
    } else {
        // 如果没有设置范围，使用Chart.js的zoom插件
        if (chart.zoom) {
            if (direction === 'in') {
                chart.zoom(zoomFactor);
            } else {
                chart.zoom(zoomFactor);
            }
        }
    }

    chart.update();
    this.showNotification(`${axis}轴${direction === 'in' ? '放大' : '缩小'}完成`, 'success');
};

// 自动缩放功能
SignalDenoiser.prototype.autoScaleChart = function(chartType) {
    const chart = this.charts[chartType];

    if (!chart) {
        this.showNotification(`图表未找到，请先生成信号`, 'warning');
        return;
    }

    // 重置缩放
    if (chart.resetZoom) {
        chart.resetZoom();
    } else {
        // 手动重置范围
        if (chart.options && chart.options.scales) {
            Object.keys(chart.options.scales).forEach(scaleKey => {
                const scale = chart.options.scales[scaleKey];
                delete scale.min;
                delete scale.max;
            });
            chart.update();
        }
    }

    this.showNotification('自动缩放完成', 'success');
};

// 导出图表功能
SignalDenoiser.prototype.exportChart = function(chartType, format) {
    console.log('导出图表:', chartType, format);
    console.log('可用图表:', Object.keys(this.charts || {}));

    // 确保图表已初始化
    if (!this.charts) {
        console.log('图表对象未初始化，尝试重新初始化...');
        this.initializeCharts();

        // 等待一段时间后重试
        setTimeout(() => {
            this.exportChart(chartType, format);
        }, 500);
        return;
    }

    // 检查图表对象是否存在
    if (!this.charts[chartType]) {
        console.error('图表未找到:', chartType);
        this.showNotification(`图表 "${chartType}" 未找到，请先生成信号数据`, 'warning');
        return;
    }

    const chart = this.charts[chartType];

    if (!chart) {
        this.showNotification(`图表对象无效`, 'error');
        return;
    }

    // 检查图表是否有数据
    if (!chart.data || !chart.data.datasets || chart.data.datasets.length === 0) {
        this.showNotification('图表没有数据，请先生成信号', 'warning');
        return;
    }

    try {
        if (format === 'excel') {
            this.exportChartToExcel(chartType);
        } else {
            // 导出为图片
            const canvas = chart.canvas;

            if (!canvas) {
                this.showNotification('无法获取图表画布', 'error');
                return;
            }

            // 确保图表已经渲染完成
            chart.update('none');

            // 等待一小段时间确保渲染完成
            setTimeout(() => {
                try {
                    // 设置背景色为白色
                    const tempCanvas = document.createElement('canvas');
                    const tempCtx = tempCanvas.getContext('2d');
                    tempCanvas.width = canvas.width;
                    tempCanvas.height = canvas.height;

                    // 填充白色背景
                    tempCtx.fillStyle = '#ffffff';
                    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                    // 绘制图表
                    tempCtx.drawImage(canvas, 0, 0);

                    // 生成下载链接
                    const mimeType = format === 'jpg' ? 'image/jpeg' : 'image/png';
                    const url = tempCanvas.toDataURL(mimeType, 0.9);

                    const link = document.createElement('a');
                    link.download = `${chartType}_chart_${new Date().getTime()}.${format}`;
                    link.href = url;

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    this.showNotification(`图表已导出为${format.toUpperCase()}`, 'success');
                } catch (innerError) {
                    console.error('图片导出失败:', innerError);
                    this.showNotification('图片导出失败: ' + innerError.message, 'error');
                }
            }, 100);
        }
    } catch (error) {
        console.error('导出失败:', error);
        this.showNotification('导出失败: ' + error.message, 'error');
    }
};

// 导出所有图表功能
SignalDenoiser.prototype.exportAllCharts = function(format) {
    console.log('导出所有图表:', format);

    // 确保图表已初始化
    if (!this.charts) {
        console.log('图表对象未初始化，尝试重新初始化...');
        this.initializeCharts();

        // 等待一段时间后重试
        setTimeout(() => {
            this.exportAllCharts(format);
        }, 500);
        return;
    }

    // 定义要导出的三个图表（右侧可视化区域的图表）
    const chartsToExport = ['time', 'frequency', 'comparison'];

    // 检查图表对象是否存在
    const availableCharts = [];
    chartsToExport.forEach(chartType => {
        if (this.charts[chartType]) {
            availableCharts.push(chartType);
        }
    });

    if (availableCharts.length === 0) {
        this.showNotification('图表未初始化，请刷新页面重试', 'error');
        return;
    }

    try {
        if (format === 'excel') {
            this.exportAllChartsToExcel();
        } else {
            this.exportAllChartsAsImages(format);
        }
    } catch (error) {
        console.error('导出所有图表失败:', error);
        this.showNotification('导出失败: ' + error.message, 'error');
    }
};

// 导出所有图表为图片
SignalDenoiser.prototype.exportAllChartsAsImages = function(format) {
    // 定义要导出的三个图表（右侧可视化区域的图表）
    const chartsToExport = ['time', 'frequency', 'comparison'];
    const chartsForExport = [];

    // 获取所有要导出的图表，无论是否有数据
    chartsToExport.forEach(chartType => {
        const chart = this.charts[chartType];
        if (chart) {
            chartsForExport.push({ type: chartType, chart: chart });
        }
    });

    if (chartsForExport.length === 0) {
        this.showNotification('图表未初始化，请刷新页面重试', 'error');
        return;
    }

    const timestamp = new Date().getTime();
    let exportedCount = 0;
    const totalCharts = chartsForExport.length;

    // 显示开始导出的提示
    this.showNotification(`开始导出${totalCharts}个图表...`, 'info');

    chartsForExport.forEach((item, index) => {
        const { type, chart } = item;

        // 确保图表已经渲染完成
        chart.update('none');

        setTimeout(() => {
            try {
                const canvas = chart.canvas;
                if (!canvas) {
                    console.warn(`无法获取${type}图表画布`);
                    exportedCount++;
                    if (exportedCount === totalCharts) {
                        this.showNotification(`导出完成，成功导出${exportedCount}个图表为${format.toUpperCase()}`, 'success');
                    }
                    return;
                }

                // 创建临时画布，添加白色背景
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = canvas.width;
                tempCanvas.height = canvas.height;

                // 填充白色背景
                tempCtx.fillStyle = '#ffffff';
                tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                // 绘制图表
                tempCtx.drawImage(canvas, 0, 0);

                // 生成下载链接
                const mimeType = format === 'jpg' ? 'image/jpeg' : 'image/png';
                const url = tempCanvas.toDataURL(mimeType, 0.9);

                // 获取中文名称
                const chartNames = {
                    'time': '时域波形',
                    'frequency': '频谱分析',
                    'comparison': '算法对比'
                };
                const chartName = chartNames[type] || type;

                const link = document.createElement('a');
                link.download = `${chartName}_${timestamp}.${format}`;
                link.href = url;

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                exportedCount++;

                // 当所有图表都导出完成时显示成功消息
                if (exportedCount === totalCharts) {
                    this.showNotification(`导出完成！成功导出${exportedCount}个图表为${format.toUpperCase()}`, 'success');
                }
            } catch (error) {
                console.error(`导出${type}图表失败:`, error);
                exportedCount++;
                if (exportedCount === totalCharts) {
                    this.showNotification(`导出完成，部分图表可能导出失败`, 'warning');
                }
            }
        }, index * 300); // 每个图表间隔300ms导出，确保稳定性
    });
};

// 导出所有图表数据到Excel
SignalDenoiser.prototype.exportAllChartsToExcel = function() {
    try {
        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 定义要导出的三个图表（右侧可视化区域的图表）
        const chartsToExport = ['time', 'frequency', 'comparison'];
        let exportedSheets = 0;

        // 为每个图表创建工作表，无论是否有数据
        chartsToExport.forEach(chartType => {
            const chart = this.charts[chartType];
            if (!chart) {
                return;
            }

            // 准备数据
            const data = [];
            const labels = chart.data?.labels || [];
            const datasets = chart.data?.datasets || [];

            // 添加标题行
            const headers = ['X轴'];
            if (datasets.length > 0) {
                datasets.forEach(dataset => {
                    headers.push(dataset.label || '数据');
                });
            } else {
                headers.push('暂无数据');
            }
            data.push(headers);

            // 添加数据行
            if (labels.length > 0 && datasets.length > 0) {
                labels.forEach((label, index) => {
                    const row = [label];
                    datasets.forEach(dataset => {
                        row.push(dataset.data?.[index] || '');
                    });
                    data.push(row);
                });
            } else {
                // 如果没有数据，添加一行说明
                const row = ['暂无数据'];
                for (let i = 1; i < headers.length; i++) {
                    row.push('');
                }
                data.push(row);
            }

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(data);

            // 获取中文名称
            const sheetNames = {
                'time': '时域波形',
                'frequency': '频谱分析',
                'comparison': '算法对比'
            };
            const sheetName = sheetNames[chartType] || chartType;

            XLSX.utils.book_append_sheet(wb, ws, sheetName);
            exportedSheets++;
        });

        if (exportedSheets === 0) {
            this.showNotification('没有可导出的图表', 'warning');
            return;
        }

        // 导出文件
        const timestamp = new Date().getTime();
        XLSX.writeFile(wb, `信号去噪图表数据_${timestamp}.xlsx`);
        this.showNotification(`成功导出${exportedSheets}个图表数据到Excel`, 'success');
    } catch (error) {
        console.error('Excel导出失败:', error);
        this.showNotification('Excel导出失败: ' + error.message, 'error');
    }
};

// 导出到Excel
SignalDenoiser.prototype.exportChartToExcel = function(chartType) {
    const chart = this.charts[chartType];
    if (!chart || !chart.data) return;

    try {
        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 准备数据
        const data = [];
        const labels = chart.data.labels || [];

        // 添加标题行
        const headers = ['X轴'];
        chart.data.datasets.forEach(dataset => {
            headers.push(dataset.label || '数据');
        });
        data.push(headers);

        // 添加数据行
        labels.forEach((label, index) => {
            const row = [label];
            chart.data.datasets.forEach(dataset => {
                row.push(dataset.data[index] || '');
            });
            data.push(row);
        });

        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(data);
        XLSX.utils.book_append_sheet(wb, ws, chartType + '图表');

        // 导出文件
        XLSX.writeFile(wb, `${chartType}_chart.xlsx`);
        this.showNotification('图表数据已导出为Excel', 'success');
    } catch (error) {
        console.error('Excel导出失败:', error);
        this.showNotification('Excel导出失败: ' + error.message, 'error');
    }
};

// 全屏功能
SignalDenoiser.prototype.toggleFullscreen = function(chartType) {
    const chartContainer = document.querySelector(`#${chartType}Chart`).closest('.chart-container');

    if (!chartContainer) return;

    if (!document.fullscreenElement) {
        chartContainer.requestFullscreen().then(() => {
            this.showNotification('已进入全屏模式', 'success');
        }).catch(err => {
            console.error('全屏失败:', err);
            this.showNotification('全屏失败', 'error');
        });
    } else {
        document.exitFullscreen().then(() => {
            this.showNotification('已退出全屏模式', 'success');
        });
    }
};

// 优化的初始化函数
function initializeSignalDenoiser() {
    // 确保所有必要的库都已加载
    if (typeof Chart === 'undefined') {
        console.error('Chart.js未加载，延迟初始化...');
        setTimeout(initializeSignalDenoiser, 100);
        return;
    }

    try {
        // 创建信号去噪器实例
        window.signalDenoiser = new SignalDenoiser();
        console.log('✅ 信号去噪器初始化成功');
    } catch (error) {
        console.error('❌ 信号去噪器初始化失败:', error);
        // 显示错误提示
        if (window.showToast) {
            showToast('页面初始化失败，请刷新重试', 'error');
        }
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSignalDenoiser);
} else {
    // DOM已经加载完成
    initializeSignalDenoiser();
}
