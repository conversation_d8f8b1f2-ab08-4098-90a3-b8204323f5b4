<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API超时测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>API超时测试页面</h1>
    <p>测试500ms快速超时和本地降级功能</p>

    <div class="test-section">
        <h3>数据API测试</h3>
        <button class="test-button" onclick="testDataUpload()">测试文件上传</button>
        <button class="test-button" onclick="testDataHistory()">测试历史数据</button>
        <button class="test-button" onclick="testDataStats()">测试数据统计</button>
        <div id="data-results"></div>
    </div>

    <div class="test-section">
        <h3>串口API测试</h3>
        <button class="test-button" onclick="testSerialPorts()">测试串口列表</button>
        <button class="test-button" onclick="testSerialConnect()">测试串口连接</button>
        <div id="serial-results"></div>
    </div>

    <div class="test-section">
        <h3>认证API测试</h3>
        <button class="test-button" onclick="testAuthLogin()">测试登录</button>
        <button class="test-button" onclick="testAuthUser()">测试用户信息</button>
        <div id="auth-results"></div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/api-service.js"></script>
    <script src="js/data-api.js"></script>
    <script src="js/serial-api.js"></script>
    <script src="js/auth-api.js"></script>

    <script>
        // 初始化API服务
        const httpClient = new HttpClient();
        const dataAPI = new DataAPI(httpClient);
        const serialAPI = new SerialAPI(httpClient);
        const authAPI = new AuthAPI(httpClient);

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(resultDiv);
            
            // 限制显示的结果数量
            const results = container.querySelectorAll('.result');
            if (results.length > 5) {
                container.removeChild(results[0]);
            }
        }

        // 数据API测试
        async function testDataUpload() {
            showResult('data-results', '开始测试文件上传...', 'info');
            try {
                // 创建一个测试文件
                const testFile = new File(['test,data\n1,2\n3,4'], 'test.csv', { type: 'text/csv' });
                const result = await dataAPI.uploadFile(testFile);
                
                if (result.useLocal) {
                    showResult('data-results', '✅ API超时，成功使用本地处理', 'warning');
                } else {
                    showResult('data-results', '✅ API响应成功', 'success');
                }
            } catch (error) {
                showResult('data-results', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testDataHistory() {
            showResult('data-results', '开始测试历史数据...', 'info');
            try {
                const result = await dataAPI.getHistoryData();
                showResult('data-results', '✅ 历史数据获取成功', 'success');
            } catch (error) {
                showResult('data-results', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testDataStats() {
            showResult('data-results', '开始测试数据统计...', 'info');
            try {
                const result = await dataAPI.getDataStatistics();
                
                if (result.source === 'local') {
                    showResult('data-results', '✅ API超时，成功使用本地统计', 'warning');
                } else {
                    showResult('data-results', '✅ API统计响应成功', 'success');
                }
            } catch (error) {
                showResult('data-results', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 串口API测试
        async function testSerialPorts() {
            showResult('serial-results', '开始测试串口列表...', 'info');
            try {
                const result = await serialAPI.getAvailablePorts();
                
                if (result.source === 'local') {
                    showResult('serial-results', '✅ API超时，返回本地提示', 'warning');
                } else {
                    showResult('serial-results', '✅ 串口列表获取成功', 'success');
                }
            } catch (error) {
                showResult('serial-results', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testSerialConnect() {
            showResult('serial-results', '开始测试串口连接...', 'info');
            try {
                const result = await serialAPI.connectPort({
                    port: 'COM1',
                    baudRate: 9600
                });
                
                if (result.useLocal) {
                    showResult('serial-results', '✅ API超时，建议使用本地串口', 'warning');
                } else {
                    showResult('serial-results', '✅ 串口连接成功', 'success');
                }
            } catch (error) {
                showResult('serial-results', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 认证API测试
        async function testAuthLogin() {
            showResult('auth-results', '开始测试登录...', 'info');
            try {
                const result = await authAPI.login({
                    email: '<EMAIL>',
                    password: 'testpassword'
                });
                showResult('auth-results', '✅ 登录测试完成', 'success');
            } catch (error) {
                if (error.message.includes('超时')) {
                    showResult('auth-results', '⚠️ 登录请求超时', 'warning');
                } else {
                    showResult('auth-results', `❌ 登录测试: ${error.message}`, 'error');
                }
            }
        }

        async function testAuthUser() {
            showResult('auth-results', '开始测试用户信息...', 'info');
            try {
                const result = await authAPI.getCurrentUser();
                showResult('auth-results', '✅ 用户信息测试完成', 'success');
            } catch (error) {
                if (error.message.includes('超时')) {
                    showResult('auth-results', '⚠️ 用户信息请求超时', 'warning');
                } else {
                    showResult('auth-results', `❌ 用户信息测试: ${error.message}`, 'error');
                }
            }
        }

        // 页面加载完成后显示配置信息
        window.addEventListener('DOMContentLoaded', function() {
            showResult('data-results', `快速超时设置: ${API_CONFIG.FAST_TIMEOUT}ms`, 'info');
            showResult('serial-results', `认证超时设置: ${API_CONFIG.AUTH_TIMEOUT}ms`, 'info');
            showResult('auth-results', `本地降级: ${API_CONFIG.ENABLE_LOCAL_FALLBACK ? '启用' : '禁用'}`, 'info');
        });
    </script>
</body>
</html>
