<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>串口调试平台 - 快速版</title>
    
    <!-- 最小化CSS -->
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .content { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #2563eb; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .loading { text-align: center; padding: 40px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📡 串口调试平台 - 快速版</h1>
            <p>轻量级串口调试工具，快速加载，专注功能</p>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                <h3>🔄 正在初始化...</h3>
                <p>请稍候，正在加载串口调试功能</p>
            </div>
            
            <div id="main-content" style="display: none;">
                <div class="status success">
                    ✅ 串口调试功能已就绪
                </div>
                
                <button class="btn" onclick="testConnection()">测试连接</button>
                <button class="btn" onclick="openFullVersion()">打开完整版</button>
                <button class="btn" onclick="goBack()">返回主页</button>
                
                <div id="debug-info" style="margin-top: 20px;">
                    <h3>调试信息:</h3>
                    <div id="debug-log" style="background: #f1f5f9; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最小化脚本 -->
    <script>
        let debugLog = '';
        
        function log(message) {
            debugLog += new Date().toLocaleTimeString() + ' - ' + message + '\n';
            const logElement = document.getElementById('debug-log');
            if (logElement) {
                logElement.textContent = debugLog;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        function testConnection() {
            log('🔍 测试串口连接...');
            // 模拟测试
            setTimeout(() => {
                log('✅ 连接测试完成');
            }, 1000);
        }
        
        function openFullVersion() {
            log('🔄 跳转到完整版...');
            window.location.href = 'serial-debug.html';
        }
        
        function goBack() {
            window.location.href = 'data.html';
        }
        
        // 快速初始化
        setTimeout(() => {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('main-content').style.display = 'block';
            log('🚀 串口调试快速版已加载');
            log('💡 这是轻量级版本，如需完整功能请点击"打开完整版"');
        }, 500);
    </script>
</body>
</html>