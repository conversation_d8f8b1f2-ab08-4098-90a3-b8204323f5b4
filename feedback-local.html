<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>意见反馈 - 数据分析平台</title>
    <!-- 图标库 -->
    <link rel="stylesheet" href="libs/css/font-awesome.min.css">
    
    <!-- Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Toast通知系统 -->
    <link rel="stylesheet" href="css/toast-system.css">
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            overflow-x: auto;
            overflow-y: auto;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(147, 197, 253, 0.3) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.25) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(167, 243, 208, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 60% 80%, rgba(254, 202, 202, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 90% 60%, rgba(253, 230, 138, 0.15) 0%, transparent 60%),
                linear-gradient(135deg, #f8fafc 0%, #f1f5f9 20%, #e2e8f0 40%, #f0f9ff 60%, #fef3c7 80%, #fce7f3 100%);
            background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
            color: #1e293b;
            overflow-x: auto;
            overflow-y: auto;
            position: relative;
            animation: backgroundShift 30s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background-position: 0% 0%, 100% 100%, 50% 50%, 25% 75%, 90% 60%, 0% 0%;
            }
            16% {
                background-position: 10% 20%, 90% 80%, 30% 70%, 45% 55%, 80% 40%, 16% 16%;
            }
            33% {
                background-position: 30% 40%, 70% 60%, 60% 30%, 65% 35%, 70% 80%, 33% 33%;
            }
            50% {
                background-position: 60% 80%, 40% 20%, 80% 60%, 25% 75%, 60% 20%, 50% 50%;
            }
            66% {
                background-position: 80% 60%, 20% 40%, 40% 80%, 75% 25%, 50% 60%, 66% 66%;
            }
            83% {
                background-position: 40% 20%, 60% 80%, 20% 40%, 55% 65%, 40% 80%, 83% 83%;
            }
        }

        /* 主容器布局 */
        .main-container {
            display: grid;
            grid-template-columns: 260px 1fr;
            min-height: 100vh;
            height: auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            min-width: 1200px;
        }

        /* 左侧导航栏 */
        .sidebar {
            background: rgba(255, 255, 255, 0.92);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                4px 0 25px rgba(0, 0, 0, 0.06),
                2px 0 15px rgba(59, 130, 246, 0.08);
            overflow-y: auto;
            position: relative;
            padding: 15px 12px;
        }

        .sidebar-header {
            padding: 20px 15px;
            text-align: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-radius: 16px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 导航菜单 */
        .nav-section {
            padding: 25px 20px;
        }

        .nav-category {
            margin-bottom: 25px;
        }

        .category-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .category-header:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .category-icon {
            font-size: 18px;
            color: #3b82f6;
            margin-right: 12px;
            width: 20px;
        }

        .category-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .category-arrow {
            font-size: 12px;
            color: #3b82f6;
            transition: transform 0.3s ease;
        }

        .category-header.expanded .category-arrow {
            transform: rotate(180deg);
        }

        .nav-submenu {
            list-style: none;
            margin-top: 10px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .nav-category.expanded .nav-submenu {
            max-height: 300px;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 45px;
            color: #64748b;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), transparent);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link:hover {
            color: #3b82f6;
            transform: translateX(8px);
        }

        .nav-icon {
            font-size: 16px;
            margin-right: 10px;
            width: 18px;
            color: #94a3b8;
            transition: color 0.3s ease;
        }

        .nav-link:hover .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .nav-link.active .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active::before {
            width: 100%;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* 意见反馈样式 */
        .feedback-section {
            position: absolute;
            bottom: 20px;
            left: 12px;
            right: 12px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .feedback-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .feedback-icon {
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .feedback-link:hover .feedback-icon {
            transform: scale(1.1);
        }

        .feedback-text {
            font-size: 14px;
            font-weight: 600;
        }

        /* 移动端导航按钮 */
        .mobile-nav-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .mobile-nav-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        /* 页面加载动画 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #7dd3fc 75%, #38bdf8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.6s ease, visibility 0.6s ease;
        }

        .page-loader.loaded {
            opacity: 0;
            visibility: hidden;
        }

        .loader-content {
            text-align: center;
            color: #0369a1;
        }

        .spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 4px solid transparent;
            border-top-color: #0369a1;
            animation: spin 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
        }

        .spinner-ring:nth-child(1) {
            animation-delay: 0s;
        }

        .spinner-ring:nth-child(2) {
            width: 80%;
            height: 80%;
            border-top-color: #0284c7;
            animation-delay: 0.15s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 60%;
            height: 60%;
            border-top-color: #0ea5e9;
            animation-delay: 0.3s;
        }

        .spinner-dot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #0369a1;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(3, 105, 161, 0.5);
            animation: spinnerDot 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes spinnerDot {
            0%, 100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .loading-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(3, 105, 161, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #0369a1, #0ea5e9);
            border-radius: 2px;
            animation: loadingProgress 2s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% {
                width: 0%;
                transform: translateX(-100%);
            }
            50% {
                width: 100%;
                transform: translateX(0%);
            }
            100% {
                width: 100%;
                transform: translateX(100%);
            }
        }
    </style>
</head>
<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-dot"></div>
            </div>
            <div class="loading-text">正在加载意见反馈页面...</div>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>
    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1 class="sidebar-title">数据分析平台</h1>
            </div>

            <nav class="nav-section">
                <!-- 信号可视化分类 -->
                <div class="nav-category expanded">
                    <div class="category-header expanded" data-category="signal-visualization">
                        <i class="fas fa-chart-line category-icon"></i>
                        <span class="category-title">信号可视化</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="data.html" class="nav-link" data-page="data">
                                <i class="fas fa-upload nav-icon"></i>
                                <span class="nav-text">数据上传</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="serial-debug.html" class="nav-link" data-page="serial-debug">
                                <i class="fas fa-terminal nav-icon"></i>
                                <span class="nav-text">串口调试</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号处理分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-processing">
                        <i class="fas fa-wave-square category-icon"></i>
                        <span class="category-title">信号处理</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="signal-denoise.html" class="nav-link" data-page="signal-denoise">
                                <i class="fas fa-magic nav-icon"></i>
                                <span class="nav-text">信号去噪</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号分析分类 -->
                <div class="nav-category expanded">
                    <div class="category-header expanded" data-category="signal-analysis">
                        <i class="fas fa-chart-area category-icon"></i>
                        <span class="category-title">信号分析</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="fft-analysis.html" class="nav-link" data-page="fft-analysis">
                                <i class="fas fa-chart-bar nav-icon"></i>
                                <span class="nav-text">频谱分析</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- 意见反馈 - 底部固定位置 -->
            <div class="feedback-section">
                <a href="feedback.html" class="feedback-link">
                    <i class="fas fa-comment-dots feedback-icon"></i>
                    <span class="feedback-text">意见反馈</span>
                </a>
            </div>
        </aside>

        <!-- 移动端导航按钮 -->
        <div class="mobile-nav-toggle" id="mobileNavToggle">
            <i class="fas fa-bars"></i>
        </div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <style>
                /* 主内容区域样式 */
                .main-content {
                    padding: 10px 25px 20px;
                    background: rgba(255, 255, 255, 0.02);
                    min-height: 100vh;
                    overflow-y: auto;
                }

                .feedback-container {
                    max-width: 1600px;
                    margin: 0 auto;
                    display: grid;
                    grid-template-columns: 1fr 300px 400px;
                    gap: 20px;
                    align-items: start;
                }

                .feedback-form-section {
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(25px);
                    border-radius: 20px;
                    padding: 30px;
                    box-shadow:
                        0 10px 40px rgba(0, 0, 0, 0.06),
                        0 4px 20px rgba(59, 130, 246, 0.08),
                        inset 0 1px 0 rgba(255, 255, 255, 0.8);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    height: fit-content;
                    min-height: calc(100vh - 40px);
                    display: flex;
                    flex-direction: column;
                }

                .feedback-header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
                }

                .feedback-header h1 {
                    font-size: 28px;
                    font-weight: 700;
                    color: #1e293b;
                    margin-bottom: 10px;
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                .feedback-header p {
                    color: #64748b;
                    font-size: 16px;
                    line-height: 1.6;
                }

                .form-group {
                    margin-bottom: 25px;
                }

                .form-group label {
                    display: block;
                    font-size: 14px;
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 8px;
                }

                .form-group label .required {
                    color: #ef4444;
                    margin-left: 4px;
                }

                .form-control {
                    width: 100%;
                    padding: 12px 16px;
                    border: 2px solid rgba(59, 130, 246, 0.2);
                    border-radius: 12px;
                    font-size: 14px;
                    background: rgba(255, 255, 255, 0.9);
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    font-family: inherit;
                    position: relative;
                }

                .form-control:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow:
                        0 0 0 4px rgba(59, 130, 246, 0.1),
                        0 8px 25px rgba(59, 130, 246, 0.15);
                    background: white;
                    transform: translateY(-2px);
                }

                .form-control:hover:not(:focus) {
                    border-color: rgba(59, 130, 246, 0.4);
                    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
                }

                .form-control.error {
                    border-color: #ef4444;
                    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
                }

                textarea.form-control {
                    min-height: 120px;
                    resize: vertical;
                }

                .form-select {
                    appearance: none;
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 12px center;
                    background-repeat: no-repeat;
                    background-size: 16px;
                    padding-right: 40px;
                }

                .form-row {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                }

                .file-upload-area {
                    border: 2px dashed rgba(59, 130, 246, 0.3);
                    border-radius: 8px;
                    padding: 20px;
                    text-align: center;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }

                .file-upload-area:hover {
                    border-color: #3b82f6;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
                }

                .file-upload-area.dragover {
                    border-color: #3b82f6;
                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
                    transform: scale(1.02);
                }

                .file-upload-icon {
                    font-size: 32px;
                    color: #3b82f6;
                    margin-bottom: 10px;
                }

                .file-upload-text {
                    color: #64748b;
                    font-size: 14px;
                    margin-bottom: 5px;
                }

                .file-upload-hint {
                    color: #94a3b8;
                    font-size: 12px;
                }

                .uploaded-files {
                    margin-top: 15px;
                }

                .uploaded-file {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px 12px;
                    background: rgba(59, 130, 246, 0.1);
                    border-radius: 6px;
                    margin-bottom: 8px;
                    font-size: 14px;
                }

                .uploaded-file-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .uploaded-file-remove {
                    background: none;
                    border: none;
                    color: #ef4444;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }

                .uploaded-file-remove:hover {
                    background: rgba(239, 68, 68, 0.1);
                }

                .error-message {
                    color: #ef4444;
                    font-size: 12px;
                    margin-top: 5px;
                    display: none;
                }

                .error-message.show {
                    display: block;
                }

                .btn {
                    padding: 12px 24px;
                    border: none;
                    border-radius: 12px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                    text-decoration: none;
                    position: relative;
                    overflow: hidden;
                }

                .btn::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    transition: left 0.6s ease;
                }

                .btn:hover::before {
                    left: 100%;
                }

                .btn-primary {
                    background: linear-gradient(135deg, #3b82f6, #0ea5e9);
                    color: white;
                    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                }

                .btn-primary:hover {
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
                    background: linear-gradient(135deg, #2563eb, #0284c7);
                }

                .btn-primary:active {
                    transform: translateY(-1px) scale(0.98);
                    transition: all 0.1s ease;
                }

                .btn-primary:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                }

                .btn-secondary {
                    background: rgba(148, 163, 184, 0.1);
                    color: #64748b;
                    border: 2px solid rgba(148, 163, 184, 0.3);
                }

                .btn-secondary:hover {
                    background: rgba(148, 163, 184, 0.2);
                    border-color: rgba(148, 163, 184, 0.5);
                }

                .form-actions {
                    display: flex;
                    gap: 15px;
                    justify-content: flex-end;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid rgba(59, 130, 246, 0.1);
                }

                .draft-indicator {
                    display: none;
                    align-items: center;
                    gap: 8px;
                    color: #10b981;
                    font-size: 12px;
                    margin-top: 10px;
                }

                .draft-indicator.show {
                    display: flex;
                }
            </style>

            <div class="feedback-container">
                <!-- 反馈表单 -->
                <div class="feedback-form-section">
                    <div class="feedback-header">
                        <h1><i class="fas fa-comment-dots"></i> 意见反馈</h1>
                        <p>您的反馈对我们非常重要，帮助我们不断改进产品和服务</p>
                    </div>

                    <form id="feedbackForm" class="feedback-form">
                        <!-- 标题 -->
                        <div class="form-group">
                            <label for="feedbackTitle">反馈标题<span class="required">*</span></label>
                            <input type="text" id="feedbackTitle" name="title" class="form-control"
                                   placeholder="请简要概括您的反馈内容" required>
                            <div class="error-message" id="titleError">请输入反馈标题</div>
                        </div>

                        <!-- 分类和优先级 -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="feedbackCategory">反馈分类</label>
                                <select id="feedbackCategory" name="category" class="form-control form-select">
                                    <option value="">请选择分类</option>
                                    <option value="bug">错误报告</option>
                                    <option value="feature">功能建议</option>
                                    <option value="ux">用户体验改进</option>
                                    <option value="content">内容问题</option>
                                    <option value="performance">性能问题</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="feedbackPriority">优先级</label>
                                <select id="feedbackPriority" name="priority" class="form-control form-select">
                                    <option value="low">低</option>
                                    <option value="medium" selected>中</option>
                                    <option value="high">高</option>
                                </select>
                            </div>
                        </div>

                        <!-- 详细描述 -->
                        <div class="form-group">
                            <label for="feedbackContent">详细描述<span class="required">*</span></label>
                            <textarea id="feedbackContent" name="content" class="form-control"
                                      placeholder="请详细描述您遇到的问题、建议或反馈内容..." required></textarea>
                            <div class="error-message" id="contentError">请输入详细描述</div>
                        </div>

                        <!-- 页面URL -->
                        <div class="form-group">
                            <label for="pageUrl">页面URL</label>
                            <input type="url" id="pageUrl" name="pageUrl" class="form-control"
                                   placeholder="问题发生的页面地址（自动填充）" readonly>
                        </div>

                        <!-- 截图上传 -->
                        <div class="form-group">
                            <label for="screenshots">截图上传</label>
                            <div class="file-upload-area" id="fileUploadArea">
                                <div class="file-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="file-upload-text">点击或拖拽文件到此处上传</div>
                                <div class="file-upload-hint">支持 JPG、PNG、GIF 格式，最大 5MB</div>
                                <input type="file" id="screenshots" name="screenshots" multiple
                                       accept="image/*" style="display: none;">
                            </div>
                            <div class="uploaded-files" id="uploadedFiles"></div>
                        </div>

                        <!-- 联系信息 -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="contactEmail">邮箱地址</label>
                                <input type="email" id="contactEmail" name="email" class="form-control"
                                       placeholder="用于接收回复（可选）">
                                <div class="error-message" id="emailError">请输入有效的邮箱地址</div>
                            </div>
                            <div class="form-group">
                                <label for="contactName">姓名/昵称</label>
                                <input type="text" id="contactName" name="name" class="form-control"
                                       placeholder="您的姓名或昵称（可选）">
                            </div>
                        </div>

                        <!-- 草稿保存提示 -->
                        <div class="draft-indicator" id="draftIndicator">
                            <i class="fas fa-save"></i>
                            <span>草稿已自动保存</span>
                        </div>

                        <!-- 表单操作按钮 -->
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" id="saveDraftBtn">
                                <i class="fas fa-save"></i>
                                保存草稿
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-paper-plane"></i>
                                提交反馈
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 中间列：反馈统计、服务评价、快速操作 -->
                <div class="middle-column">
                    <div class="feedback-stats-card">
                        <h3><i class="fas fa-chart-bar"></i> 反馈统计</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number" id="totalFeedbacks">0</div>
                                <div class="stat-label">总反馈数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="resolvedFeedbacks">0</div>
                                <div class="stat-label">已解决</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="pendingFeedbacks">0</div>
                                <div class="stat-label">处理中</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="newFeedbacks">0</div>
                                <div class="stat-label">新反馈</div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户评价卡片 -->
                    <div class="user-rating-card">
                        <div class="rating-header">
                            <h3><i class="fas fa-star"></i> 服务评价</h3>
                            <span class="rating-subtitle">为我们的服务打分</span>
                        </div>
                        <div class="rating-content">
                            <div class="overall-rating">
                                <div class="rating-score">4.8</div>
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="rating-count">基于 127 条评价</div>
                            </div>
                            <div class="rating-breakdown">
                                <div class="rating-row">
                                    <span class="rating-label">5星</span>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: 85%"></div>
                                    </div>
                                    <span class="rating-percent">85%</span>
                                </div>
                                <div class="rating-row">
                                    <span class="rating-label">4星</span>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: 12%"></div>
                                    </div>
                                    <span class="rating-percent">12%</span>
                                </div>
                                <div class="rating-row">
                                    <span class="rating-label">3星</span>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: 2%"></div>
                                    </div>
                                    <span class="rating-percent">2%</span>
                                </div>
                                <div class="rating-row">
                                    <span class="rating-label">2星</span>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: 1%"></div>
                                    </div>
                                    <span class="rating-percent">1%</span>
                                </div>
                                <div class="rating-row">
                                    <span class="rating-label">1星</span>
                                    <div class="rating-bar">
                                        <div class="rating-fill" style="width: 0%"></div>
                                    </div>
                                    <span class="rating-percent">0%</span>
                                </div>
                            </div>
                            <button class="rate-now-btn" id="rateNowBtn">
                                <i class="fas fa-thumbs-up"></i>
                                立即评价
                            </button>
                        </div>
                    </div>

                    <!-- 快速操作卡片 -->
                    <div class="quick-actions-card">
                        <div class="actions-header">
                            <h3><i class="fas fa-bolt"></i> 快速操作</h3>
                        </div>
                        <div class="actions-grid">
                            <button class="action-btn" id="exportBtn">
                                <i class="fas fa-download"></i>
                                <span>导出反馈</span>
                            </button>
                            <button class="action-btn" id="clearDraftBtn">
                                <i class="fas fa-trash-alt"></i>
                                <span>清空草稿</span>
                            </button>
                            <button class="action-btn" id="templateBtn">
                                <i class="fas fa-file-alt"></i>
                                <span>反馈模板</span>
                            </button>
                            <button class="action-btn" id="helpBtn">
                                <i class="fas fa-question-circle"></i>
                                <span>使用帮助</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 右侧列：评价评论、最近反馈 -->
                <div class="right-column">
                    <!-- 评价评论区 -->
                    <div class="comments-card">
                        <div class="comments-header">
                            <h3><i class="fas fa-comment-alt"></i> 评价评论</h3>
                            <div class="comments-filters">
                                <select id="commentsSort" class="form-control form-select">
                                    <option value="latest">最新评论</option>
                                    <option value="most-liked">最多点赞</option>
                                </select>
                            </div>
                        </div>

                        <div class="comments-list" id="commentsList">
                            <!-- 评论列表将通过JavaScript动态生成 -->
                        </div>

                        <div class="add-comment-section">
                            <div class="comment-input-wrapper">
                                <textarea id="newCommentText" placeholder="写下您的评价或建议..." class="comment-input"></textarea>
                                <button class="btn btn-primary" id="addCommentBtn">
                                    <i class="fas fa-paper-plane"></i>
                                    发表评论
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 最近反馈 -->
                    <div class="recent-feedbacks-card">
                        <h3><i class="fas fa-comments"></i> 最近反馈</h3>
                        <div class="feedback-filters">
                            <select id="categoryFilter" class="form-control form-select">
                                <option value="">所有分类</option>
                                <option value="bug">错误报告</option>
                                <option value="feature">功能建议</option>
                                <option value="ux">用户体验改进</option>
                                <option value="content">内容问题</option>
                                <option value="performance">性能问题</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="feedback-list" id="feedbackList">
                            <!-- 反馈列表将通过JavaScript动态生成 -->
                        </div>
                        <div class="load-more-container">
                            <button class="btn btn-secondary" id="loadMoreBtn">
                                <i class="fas fa-chevron-down"></i>
                                加载更多
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </main>
    </div>

    <!-- 成功提示模态框 -->
    <div class="modal" id="successModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-check-circle"></i> 提交成功</h3>
            </div>
            <div class="modal-body">
                <p>感谢您的反馈！我们已收到您的意见，反馈编号为：</p>
                <div class="feedback-id" id="feedbackId">#FB-2024-001</div>
                <p>我们会尽快处理您的反馈，如有需要会通过邮箱与您联系。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="closeModalBtn">确定</button>
            </div>
        </div>
    </div>

    <!-- 反馈详情模态框 -->
    <div class="modal feedback-detail-modal" id="feedbackDetailModal">
        <div class="modal-content feedback-detail-content">
            <div class="modal-header">
                <h3 id="detailTitle"><i class="fas fa-comment-alt"></i> 反馈详情</h3>
                <button class="modal-close" id="closeDetailBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body feedback-detail-body">
                <div class="feedback-detail-info">
                    <div class="detail-meta">
                        <span class="detail-status" id="detailStatus">新反馈</span>
                        <span class="detail-category" id="detailCategory">功能建议</span>
                        <span class="detail-priority" id="detailPriority">中</span>
                        <span class="detail-time" id="detailTime">2024-12-01</span>
                    </div>
                    <h4 class="detail-feedback-title" id="detailFeedbackTitle">反馈标题</h4>
                    <div class="detail-content" id="detailContent">反馈内容...</div>
                    <div class="detail-page-url" id="detailPageUrl" style="display: none;">
                        <strong>页面地址：</strong>
                        <a href="#" target="_blank" id="detailPageLink"></a>
                    </div>
                </div>

                <!-- 评论区域 -->
                <div class="comments-section">
                    <h4><i class="fas fa-comments"></i> 评论与回复 (<span id="commentsCount">0</span>)</h4>

                    <!-- 添加评论 -->
                    <div class="add-comment">
                        <div class="comment-form">
                            <div class="comment-input-group">
                                <input type="text" id="commenterName" placeholder="您的昵称（可选）" class="comment-name-input">
                                <textarea id="commentContent" placeholder="写下您的评论或建议..." class="comment-textarea"></textarea>
                            </div>
                            <div class="comment-actions">
                                <button class="btn btn-primary" id="addCommentBtn">
                                    <i class="fas fa-paper-plane"></i>
                                    发表评论
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 评论列表 -->
                    <div class="comments-list" id="modalCommentsList">
                        <!-- 评论将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 中间列样式 */
        .middle-column {
            display: flex;
            flex-direction: column;
            gap: 40px;
            height: calc(100vh - 80px);
            align-items: stretch;
        }

        /* 右侧列样式 */
        .right-column {
            display: flex;
            flex-direction: column;
            gap: 20px;
            height: calc(100vh - 80px);
            align-items: stretch;
        }

        /* 中间列卡片高度调整 - 确保底部对齐 */
        .middle-column .feedback-stats-card {
            height: 220px;
            flex-shrink: 0;
            overflow: visible;
        }

        .middle-column .user-rating-card {
            flex: 1;
            min-height: 500px;
            overflow: visible;
        }

        .middle-column .quick-actions-card {
            height: 200px;
            flex-shrink: 0;
            overflow: visible;
        }

        /* 右侧列卡片高度调整 - 确保底部对齐 */
        .right-column .comments-card {
            height: calc(45% - 10px);
            flex-shrink: 0;
            min-height: 320px;
            overflow: hidden;
        }

        .right-column .recent-feedbacks-card {
            height: calc(62.2% - 10px);
            flex-shrink: 0;
            min-height: 400px;
            overflow: hidden;
        }

        .feedback-stats-card,
        .recent-feedbacks-card,
        .user-rating-card,
        .quick-actions-card,
        .comments-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 20px;
            box-shadow:
                0 10px 40px rgba(0, 0, 0, 0.06),
                0 4px 20px rgba(59, 130, 246, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 15px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        /* 评价评论区和最近反馈卡片内部布局 */
        .comments-card,
        .recent-feedbacks-card {
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .feedback-stats-card:hover,
        .recent-feedbacks-card:hover,
        .user-rating-card:hover,
        .quick-actions-card:hover,
        .comments-card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 30px rgba(59, 130, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(59, 130, 246, 0.2);
        }

        .feedback-stats-card::before,
        .recent-feedbacks-card::before,
        .user-rating-card::before,
        .quick-actions-card::before,
        .comments-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
            transition: left 0.6s ease;
        }

        .feedback-stats-card:hover::before,
        .recent-feedbacks-card:hover::before,
        .user-rating-card:hover::before,
        .quick-actions-card:hover::before,
        .comments-card:hover::before {
            left: 100%;
        }

        .feedback-stats-card h3,
        .recent-feedbacks-card h3,
        .user-rating-card h3,
        .quick-actions-card h3,
        .comments-card h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feedback-stats-card h3 i,
        .recent-feedbacks-card h3 i,
        .user-rating-card h3 i,
        .quick-actions-card h3 i,
        .comments-card h3 i {
            color: #3b82f6;
            font-size: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 10px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 3px;
        }

        .stat-label {
            font-size: 11px;
            color: #64748b;
            font-weight: 500;
        }

        .feedback-filters {
            margin-bottom: 20px;
        }

        .feedback-list {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }

        .feedback-item {
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 12px;
            border-left: 4px solid;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feedback-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .feedback-item.bug {
            border-left-color: #ef4444;
        }

        .feedback-item.feature {
            border-left-color: #10b981;
        }

        .feedback-item.ux {
            border-left-color: #f59e0b;
        }

        .feedback-item.content {
            border-left-color: #8b5cf6;
        }

        .feedback-item.performance {
            border-left-color: #06b6d4;
        }

        .feedback-item.other {
            border-left-color: #6b7280;
        }

        .feedback-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .feedback-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .feedback-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
            color: #64748b;
        }

        .feedback-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .feedback-status.new {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .feedback-status.in-progress {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .feedback-status.resolved {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .feedback-content-preview {
            font-size: 13px;
            color: #64748b;
            line-height: 1.4;
            margin-top: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .load-more-container {
            text-align: center;
            margin-top: 15px;
        }

        /* 用户评价卡片样式 */
        .rating-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .rating-subtitle {
            display: block;
            font-size: 14px;
            color: #64748b;
            margin-top: 5px;
        }

        .overall-rating {
            text-align: center;
            margin-bottom: 25px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
            border-radius: 15px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .rating-score {
            font-size: 48px;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 10px;
        }

        .rating-stars {
            margin-bottom: 10px;
        }

        .rating-stars i {
            color: #fbbf24;
            font-size: 20px;
            margin: 0 2px;
        }

        .rating-count {
            font-size: 14px;
            color: #64748b;
        }

        .rating-breakdown {
            margin-bottom: 15px;
        }

        .rating-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .rating-label {
            font-size: 14px;
            color: #374151;
            min-width: 30px;
        }

        .rating-bar {
            flex: 1;
            height: 8px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .rating-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #0ea5e9);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .rating-percent {
            font-size: 12px;
            color: #64748b;
            min-width: 35px;
            text-align: right;
        }

        .rate-now-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .rate-now-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        /* 快速操作卡片样式 */
        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            margin-top: 8px;
        }

        .action-btn {
            padding: 6px 4px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            text-decoration: none;
            color: #374151;
            height: 55px;
            justify-content: center;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .action-btn i {
            font-size: 12px;
            color: #3b82f6;
            transition: color 0.3s ease;
        }

        .action-btn:hover i {
            color: white;
        }

        .action-btn span {
            font-size: 8px;
            font-weight: 600;
            text-align: center;
            line-height: 1.1;
            white-space: nowrap;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 10000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 25px 30px 0;
            text-align: center;
        }

        .modal-header h3 {
            font-size: 20px;
            font-weight: 600;
            color: #10b981;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .modal-header h3 i {
            font-size: 24px;
        }

        .modal-body {
            padding: 20px 30px;
            text-align: center;
        }

        .modal-body p {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .feedback-id {
            font-size: 18px;
            font-weight: 700;
            color: #3b82f6;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            padding: 10px 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }

        .modal-footer {
            padding: 0 30px 30px;
            text-align: center;
        }

        .modal-footer .btn {
            min-width: 120px;
        }

        /* 反馈详情模态框样式 */
        .feedback-detail-modal .modal-content {
            max-width: 800px;
            width: 95%;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .feedback-detail-content .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #64748b;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .feedback-detail-body {
            padding: 0;
            overflow-y: auto;
            flex: 1;
        }

        .feedback-detail-info {
            padding: 25px 30px;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .detail-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .detail-status,
        .detail-category,
        .detail-priority,
        .detail-time {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .detail-status.new {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .detail-status.in-progress {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .detail-status.resolved {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .detail-category {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
        }

        .detail-priority {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .detail-time {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
        }

        .detail-feedback-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
        }

        .detail-content {
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
            margin-bottom: 15px;
            white-space: pre-wrap;
        }

        .detail-page-url {
            font-size: 13px;
            color: #64748b;
            padding: 10px;
            background: rgba(59, 130, 246, 0.05);
            border-radius: 8px;
        }

        .detail-page-url a {
            color: #3b82f6;
            text-decoration: none;
        }

        .detail-page-url a:hover {
            text-decoration: underline;
        }

        /* 评论区域样式 */
        .comments-section {
            padding: 25px 30px;
        }

        .comments-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .comments-section h4 i {
            color: #3b82f6;
        }

        .add-comment {
            margin-bottom: 25px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .comment-input-group {
            margin-bottom: 15px;
        }

        .comment-name-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 6px;
            font-size: 14px;
            margin-bottom: 10px;
            background: white;
            transition: all 0.3s ease;
        }

        .comment-name-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .comment-textarea {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 6px;
            font-size: 14px;
            resize: vertical;
            background: white;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .comment-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .comment-actions {
            text-align: right;
        }

        .comments-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .comment-item {
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 12px;
            border-left: 3px solid #3b82f6;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .comment-author {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .comment-time {
            font-size: 12px;
            color: #64748b;
        }

        .comment-content {
            font-size: 14px;
            line-height: 1.5;
            color: #374151;
            white-space: pre-wrap;
        }

        .no-comments {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }

        .no-comments i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* 评价模态框样式 */
        .rating-modal .modal-content {
            max-width: 600px;
            width: 95%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .rating-modal-body {
            padding: 0 30px 20px;
        }

        .rating-form h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .rating-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
            border-radius: 15px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .star-rating {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin: 15px 0;
        }

        .star {
            font-size: 32px;
            color: #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .star:hover {
            transform: scale(1.2);
        }

        .star.active {
            color: #fbbf24;
            transform: scale(1.1);
        }

        .star.active i {
            animation: starPulse 0.6s ease;
        }

        @keyframes starPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }

        .rating-text {
            font-size: 18px;
            font-weight: 600;
            color: #3b82f6;
            margin-top: 15px;
        }

        .rating-aspects {
            margin-bottom: 25px;
        }

        .aspect-rating {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .aspect-rating label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            min-width: 80px;
        }

        .aspect-stars .star {
            font-size: 20px;
        }

        .rating-comment {
            margin-bottom: 25px;
        }

        .rating-comment textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 10px;
            font-size: 14px;
            resize: vertical;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .rating-comment textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: white;
        }

        .rating-tags {
            margin-bottom: 20px;
        }

        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .rating-tag {
            padding: 6px 12px;
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .rating-tag:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: translateY(-1px);
        }

        .rating-tag.selected {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 评价评论区详细样式 */
        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .comments-filters {
            min-width: 120px;
        }

        .comments-list {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            min-height: 0;
        }

        .comment-item {
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            margin-bottom: 12px;
            border-left: 4px solid #3b82f6;
            transition: all 0.3s ease;
            position: relative;
        }

        .comment-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .comment-author {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .comment-time {
            font-size: 12px;
            color: #64748b;
        }

        .comment-content {
            color: #374151;
            line-height: 1.6;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .comment-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .like-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .like-btn:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .like-btn.liked {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }

        .like-btn.liked i {
            animation: heartBeat 0.6s ease;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }

        .add-comment-section {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(59, 130, 246, 0.1);
        }

        .comment-input-wrapper {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .comment-input {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 10px;
            font-size: 14px;
            resize: vertical;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .comment-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: white;
        }

        .comment-input::placeholder {
            color: #94a3b8;
        }

        .empty-comments {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .empty-comments i {
            font-size: 48px;
            color: #cbd5e1;
            margin-bottom: 15px;
        }

        .empty-comments p {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .empty-comments small {
            font-size: 14px;
            color: #94a3b8;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .feedback-container {
                grid-template-columns: 1fr;
                max-width: 900px;
                gap: 20px;
            }

            .feedback-display-section {
                order: -1;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 15px;
            }

            .feedback-container {
                gap: 15px;
            }

            .feedback-display-section {
                grid-template-columns: 1fr;
            }

            .feedback-form-section,
            .feedback-stats-card,
            .recent-feedbacks-card,
            .user-rating-card,
            .quick-actions-card {
                padding: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .feedback-detail-modal .modal-content {
                width: 98%;
                margin: 1%;
            }

            .feedback-detail-info,
            .comments-section {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                grid-template-columns: 1fr;
                min-width: auto;
            }

            .sidebar {
                position: fixed;
                top: 0;
                left: -260px;
                height: 100vh;
                z-index: 999;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .mobile-nav-toggle {
                display: flex;
            }

            .main-content {
                padding: 80px 10px 10px;
            }

            .feedback-form-section,
            .feedback-stats-card,
            .recent-feedbacks-card,
            .user-rating-card,
            .quick-actions-card {
                padding: 15px;
            }

            .actions-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .rating-score {
                font-size: 36px;
            }

            .overall-rating {
                padding: 15px;
            }
        }
    </style>

    <!-- 用户评价模态框 -->
    <div class="modal rating-modal" id="ratingModal">
        <div class="modal-content rating-modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-star"></i> 服务评价</h3>
                <button class="modal-close" id="closeRatingBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body rating-modal-body">
                <div class="rating-form">
                    <div class="rating-section">
                        <h4>整体满意度</h4>
                        <div class="star-rating" id="overallRating">
                            <span class="star" data-rating="1"><i class="far fa-star"></i></span>
                            <span class="star" data-rating="2"><i class="far fa-star"></i></span>
                            <span class="star" data-rating="3"><i class="far fa-star"></i></span>
                            <span class="star" data-rating="4"><i class="far fa-star"></i></span>
                            <span class="star" data-rating="5"><i class="far fa-star"></i></span>
                        </div>
                        <div class="rating-text" id="ratingText">请选择您的评分</div>
                    </div>

                    <div class="rating-aspects">
                        <h4>各项评价</h4>
                        <div class="aspect-rating">
                            <label>界面设计</label>
                            <div class="star-rating aspect-stars" data-aspect="design">
                                <span class="star" data-rating="1"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="2"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="3"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="4"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="5"><i class="far fa-star"></i></span>
                            </div>
                        </div>
                        <div class="aspect-rating">
                            <label>功能完整性</label>
                            <div class="star-rating aspect-stars" data-aspect="functionality">
                                <span class="star" data-rating="1"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="2"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="3"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="4"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="5"><i class="far fa-star"></i></span>
                            </div>
                        </div>
                        <div class="aspect-rating">
                            <label>响应速度</label>
                            <div class="star-rating aspect-stars" data-aspect="performance">
                                <span class="star" data-rating="1"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="2"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="3"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="4"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="5"><i class="far fa-star"></i></span>
                            </div>
                        </div>
                        <div class="aspect-rating">
                            <label>易用性</label>
                            <div class="star-rating aspect-stars" data-aspect="usability">
                                <span class="star" data-rating="1"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="2"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="3"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="4"><i class="far fa-star"></i></span>
                                <span class="star" data-rating="5"><i class="far fa-star"></i></span>
                            </div>
                        </div>
                    </div>

                    <div class="rating-comment">
                        <h4>评价留言</h4>
                        <textarea id="ratingComment" placeholder="请分享您的使用体验和建议..." rows="4"></textarea>
                    </div>

                    <div class="rating-tags">
                        <h4>选择标签</h4>
                        <div class="tag-list">
                            <span class="rating-tag" data-tag="界面美观">界面美观</span>
                            <span class="rating-tag" data-tag="功能强大">功能强大</span>
                            <span class="rating-tag" data-tag="操作简单">操作简单</span>
                            <span class="rating-tag" data-tag="响应快速">响应快速</span>
                            <span class="rating-tag" data-tag="数据准确">数据准确</span>
                            <span class="rating-tag" data-tag="体验良好">体验良好</span>
                            <span class="rating-tag" data-tag="需要改进">需要改进</span>
                            <span class="rating-tag" data-tag="功能不足">功能不足</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelRatingBtn">取消</button>
                <button class="btn btn-primary" id="submitRatingBtn">提交评价</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let uploadedFiles = [];
        let feedbackData = [];
        let currentPage = 1;
        const itemsPerPage = 5;
        let currentFeedbackId = null;
        let commentsData = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 显示加载动画
            showPageLoader();

            // 模拟加载过程
            setTimeout(async () => {
                initializePage();
                await loadFeedbackData();
                loadCommentsData();
                loadDraft();
                setupEventListeners();
                updateStats();
                loadRatingData();
                hidePageLoader();
            }, 1500);
        });

        // 显示页面加载动画
        function showPageLoader() {
            const loader = document.getElementById('pageLoader');
            loader.style.display = 'flex';
        }

        // 隐藏页面加载动画
        function hidePageLoader() {
            const loader = document.getElementById('pageLoader');
            loader.classList.add('loaded');
        }

        // 初始化页面
        function initializePage() {
            // 自动填充当前页面URL
            const pageUrlInput = document.getElementById('pageUrl');
            pageUrlInput.value = document.referrer || window.location.origin;

            // 设置导航栏激活状态
            const feedbackLink = document.querySelector('.feedback-link');
            if (feedbackLink) {
                feedbackLink.style.background = 'linear-gradient(135deg, #3b82f6, #0ea5e9)';
                feedbackLink.style.color = 'white';
            }

            // 初始化评论区
            initCommentsSection();
            generateSampleComments();
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 表单提交
            const form = document.getElementById('feedbackForm');
            form.addEventListener('submit', handleFormSubmit);

            // 文件上传
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('screenshots');

            fileUploadArea.addEventListener('click', () => fileInput.click());
            fileUploadArea.addEventListener('dragover', handleDragOver);
            fileUploadArea.addEventListener('dragleave', handleDragLeave);
            fileUploadArea.addEventListener('drop', handleFileDrop);
            fileInput.addEventListener('change', handleFileSelect);

            // 保存草稿
            const saveDraftBtn = document.getElementById('saveDraftBtn');
            saveDraftBtn.addEventListener('click', saveDraft);

            // 自动保存草稿
            const formInputs = form.querySelectorAll('input, textarea, select');
            formInputs.forEach(input => {
                input.addEventListener('input', debounce(autoSaveDraft, 2000));
            });

            // 模态框关闭
            const closeModalBtn = document.getElementById('closeModalBtn');
            closeModalBtn.addEventListener('click', closeModal);

            // 反馈详情模态框关闭
            const closeDetailBtn = document.getElementById('closeDetailBtn');
            closeDetailBtn.addEventListener('click', closeFeedbackDetail);

            // 添加评论
            const addCommentBtn = document.getElementById('addCommentBtn');
            addCommentBtn.addEventListener('click', addComment);

            // 分类过滤
            const categoryFilter = document.getElementById('categoryFilter');
            categoryFilter.addEventListener('change', filterFeedbacks);

            // 加载更多
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            loadMoreBtn.addEventListener('click', loadMoreFeedbacks);

            // 导航栏功能
            setupNavigation();

            // 移动端导航切换
            const mobileNavToggle = document.getElementById('mobileNavToggle');
            if (mobileNavToggle) {
                mobileNavToggle.addEventListener('click', toggleMobileNav);
            }

            // 新功能按钮事件
            setupNewFeatureEvents();
        }

        // 设置导航栏功能
        function setupNavigation() {
            // 分类展开/收起
            const categoryHeaders = document.querySelectorAll('.category-header');
            categoryHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const category = this.closest('.nav-category');
                    const isExpanded = category.classList.contains('expanded');

                    // 收起所有其他分类
                    document.querySelectorAll('.nav-category').forEach(cat => {
                        if (cat !== category) {
                            cat.classList.remove('expanded');
                            const catHeader = cat.querySelector('.category-header');
                            if (catHeader) {
                                catHeader.classList.remove('expanded');
                            }
                        }
                    });

                    // 切换当前分类
                    if (isExpanded) {
                        category.classList.remove('expanded');
                        this.classList.remove('expanded');
                    } else {
                        category.classList.add('expanded');
                        this.classList.add('expanded');
                    }
                });
            });
        }

        // 表单提交处理
        function handleFormSubmit(e) {
            e.preventDefault();

            if (validateForm()) {
                const formData = collectFormData();
                submitFeedback(formData);
            }
        }

        // 表单验证
        function validateForm() {
            let isValid = true;

            // 验证标题
            const title = document.getElementById('feedbackTitle');
            const titleError = document.getElementById('titleError');
            if (!title.value.trim()) {
                showError(title, titleError, '请输入反馈标题');
                isValid = false;
            } else {
                hideError(title, titleError);
            }

            // 验证内容
            const content = document.getElementById('feedbackContent');
            const contentError = document.getElementById('contentError');
            if (!content.value.trim()) {
                showError(content, contentError, '请输入详细描述');
                isValid = false;
            } else {
                hideError(content, contentError);
            }

            // 验证邮箱（如果填写了）
            const email = document.getElementById('contactEmail');
            const emailError = document.getElementById('emailError');
            if (email.value && !isValidEmail(email.value)) {
                showError(email, emailError, '请输入有效的邮箱地址');
                isValid = false;
            } else {
                hideError(email, emailError);
            }

            return isValid;
        }

        // 显示错误
        function showError(input, errorElement, message) {
            input.classList.add('error');
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }

        // 隐藏错误
        function hideError(input, errorElement) {
            input.classList.remove('error');
            errorElement.classList.remove('show');
        }

        // 邮箱验证
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // 收集表单数据
        function collectFormData() {
            return {
                id: generateFeedbackId(),
                title: document.getElementById('feedbackTitle').value.trim(),
                category: document.getElementById('feedbackCategory').value,
                priority: document.getElementById('feedbackPriority').value,
                content: document.getElementById('feedbackContent').value.trim(),
                pageUrl: document.getElementById('pageUrl').value,
                email: document.getElementById('contactEmail').value.trim(),
                name: document.getElementById('contactName').value.trim(),
                screenshots: uploadedFiles,
                status: 'new',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
        }

        // 生成反馈ID
        function generateFeedbackId() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `FB-${year}${month}${day}-${random}`;
        }

        // 映射分类到API类型
        function mapCategoryToType(category) {
            const categoryMap = {
                'bug': 'bug',
                'feature': 'feature',
                'ux': 'improvement',
                'content': 'general',
                'performance': 'bug',
                'other': 'general'
            };
            return categoryMap[category] || 'general';
        }

        // 提交反馈
        async function submitFeedback(formData) {
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;

            // 显示提交中状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

            try {
                // 尝试使用API提交反馈
                if (window.FeedbackAPI) {
                    const feedbackData = {
                        type: mapCategoryToType(formData.category),
                        title: formData.title,
                        description: formData.content,
                        priority: formData.priority,
                        category: formData.category,
                        attachments: uploadedFiles.map(file => ({
                            filename: file.name,
                            size: file.size,
                            type: file.type
                        })),
                        metadata: {
                            pageUrl: formData.pageUrl,
                            contactEmail: formData.email,
                            contactName: formData.name
                        }
                    };

                    const result = await window.FeedbackAPI.submitFeedback(feedbackData);

                    if (result.success) {
                        // API提交成功
                        formData.id = result.data.ticketNumber || result.data.feedbackId;
                        formData.status = result.data.status || 'open';
                        formData.createdAt = result.data.createdAt || new Date().toISOString();

                        // 保存到本地存储作为备份
                        feedbackData.unshift(formData);
                        localStorage.setItem('feedbackData', JSON.stringify(feedbackData));

                        // 清除草稿
                        localStorage.removeItem('feedbackDraft');

                        // 重置表单
                        document.getElementById('feedbackForm').reset();
                        uploadedFiles = [];
                        updateUploadedFilesList();

                        // 恢复按钮状态
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;

                        // 显示成功模态框
                        document.getElementById('feedbackId').textContent = formData.id;
                        showModal();

                        // 更新统计和列表
                        updateStats();
                        renderFeedbackList();
                        return;
                    }
                }

                // API失败或不可用，使用本地存储
                throw new Error('API不可用，使用本地存储');

            } catch (error) {
                console.warn('API提交失败，使用本地存储:', error);

                // 本地存储备用方案
                feedbackData.unshift(formData);
                localStorage.setItem('feedbackData', JSON.stringify(feedbackData));

                // 清除草稿
                localStorage.removeItem('feedbackDraft');

                // 重置表单
                document.getElementById('feedbackForm').reset();
                uploadedFiles = [];
                updateUploadedFilesList();

                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;

                // 显示成功模态框
                document.getElementById('feedbackId').textContent = formData.id;
                showModal();

                // 更新统计和列表
                updateStats();
                renderFeedbackList();
            }
        }

        // 文件拖拽处理
        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        // 处理文件
        function processFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    const fileData = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        file: file
                    };
                    uploadedFiles.push(fileData);
                }
            });
            updateUploadedFilesList();
        }

        // 文件验证
        function validateFile(file) {
            const maxSize = 5 * 1024 * 1024; // 5MB
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];

            if (!allowedTypes.includes(file.type)) {
                alert('只支持 JPG、PNG、GIF 格式的图片');
                return false;
            }

            if (file.size > maxSize) {
                alert('文件大小不能超过 5MB');
                return false;
            }

            return true;
        }

        // 更新已上传文件列表
        function updateUploadedFilesList() {
            const container = document.getElementById('uploadedFiles');

            if (uploadedFiles.length === 0) {
                container.innerHTML = '';
                return;
            }

            container.innerHTML = uploadedFiles.map(file => `
                <div class="uploaded-file">
                    <div class="uploaded-file-info">
                        <i class="fas fa-image"></i>
                        <span>${file.name}</span>
                        <small>(${formatFileSize(file.size)})</small>
                    </div>
                    <button type="button" class="uploaded-file-remove" onclick="removeFile(${file.id})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id !== fileId);
            updateUploadedFilesList();
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 草稿保存
        function saveDraft() {
            const draftData = {
                title: document.getElementById('feedbackTitle').value,
                category: document.getElementById('feedbackCategory').value,
                priority: document.getElementById('feedbackPriority').value,
                content: document.getElementById('feedbackContent').value,
                pageUrl: document.getElementById('pageUrl').value,
                email: document.getElementById('contactEmail').value,
                name: document.getElementById('contactName').value,
                savedAt: new Date().toISOString()
            };

            localStorage.setItem('feedbackDraft', JSON.stringify(draftData));
            showDraftIndicator();
        }

        // 自动保存草稿
        function autoSaveDraft() {
            const title = document.getElementById('feedbackTitle').value;
            const content = document.getElementById('feedbackContent').value;

            // 只有在有内容时才自动保存
            if (title.trim() || content.trim()) {
                saveDraft();
            }
        }

        // 加载草稿
        function loadDraft() {
            const draftData = localStorage.getItem('feedbackDraft');
            if (draftData) {
                const draft = JSON.parse(draftData);

                document.getElementById('feedbackTitle').value = draft.title || '';
                document.getElementById('feedbackCategory').value = draft.category || '';
                document.getElementById('feedbackPriority').value = draft.priority || 'medium';
                document.getElementById('feedbackContent').value = draft.content || '';
                document.getElementById('pageUrl').value = draft.pageUrl || '';
                document.getElementById('contactEmail').value = draft.email || '';
                document.getElementById('contactName').value = draft.name || '';

                if (draft.title || draft.content) {
                    showDraftIndicator();
                }
            }
        }

        // 显示草稿指示器
        function showDraftIndicator() {
            const indicator = document.getElementById('draftIndicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 模态框显示
        function showModal() {
            document.getElementById('successModal').classList.add('show');
        }

        // 模态框关闭
        function closeModal() {
            document.getElementById('successModal').classList.remove('show');
        }

        // 关闭反馈详情模态框
        function closeFeedbackDetail() {
            document.getElementById('feedbackDetailModal').classList.remove('show');
            currentFeedbackId = null;
        }

        // 显示反馈详情
        function showFeedbackDetail(feedbackId) {
            const feedback = feedbackData.find(f => f.id === feedbackId);
            if (!feedback) return;

            currentFeedbackId = feedbackId;

            // 填充详情信息
            document.getElementById('detailFeedbackTitle').textContent = feedback.title;
            document.getElementById('detailContent').textContent = feedback.content;
            document.getElementById('detailStatus').textContent = getStatusText(feedback.status);
            document.getElementById('detailStatus').className = `detail-status ${feedback.status}`;
            document.getElementById('detailCategory').textContent = getCategoryText(feedback.category);
            document.getElementById('detailPriority').textContent = getPriorityText(feedback.priority);
            document.getElementById('detailTime').textContent = formatDate(feedback.createdAt);

            // 页面URL
            if (feedback.pageUrl) {
                document.getElementById('detailPageUrl').style.display = 'block';
                const pageLink = document.getElementById('detailPageLink');
                pageLink.href = feedback.pageUrl;
                pageLink.textContent = feedback.pageUrl;
            } else {
                document.getElementById('detailPageUrl').style.display = 'none';
            }

            // 加载评论
            loadComments(feedbackId);

            // 显示模态框
            document.getElementById('feedbackDetailModal').classList.add('show');
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityMap = {
                'low': '低',
                'medium': '中',
                'high': '高'
            };
            return priorityMap[priority] || priority;
        }

        // 加载评论数据
        function loadCommentsData() {
            const savedComments = localStorage.getItem('commentsData');
            if (savedComments) {
                commentsData = JSON.parse(savedComments);
            } else {
                // 初始化示例评论数据
                commentsData = {
                    'FB-20241201-001': [
                        {
                            id: 'comment-1',
                            author: '开发团队',
                            content: '感谢您的建议！我们正在评估Excel和JSON格式的支持，预计在下个版本中实现。',
                            createdAt: '2024-12-01T15:30:00Z'
                        },
                        {
                            id: 'comment-2',
                            author: '用户A',
                            content: '期待这个功能，CSV格式有时候处理起来不太方便。',
                            createdAt: '2024-12-01T16:45:00Z'
                        }
                    ],
                    'FB-20241130-002': [
                        {
                            id: 'comment-3',
                            author: '技术支持',
                            content: '问题已修复，请清除浏览器缓存后重试。如果仍有问题请联系我们。',
                            createdAt: '2024-12-01T09:20:00Z'
                        }
                    ]
                };
                localStorage.setItem('commentsData', JSON.stringify(commentsData));
            }
        }

        // 加载评论
        function loadComments(feedbackId) {
            const comments = commentsData[feedbackId] || [];
            const commentsContainer = document.getElementById('commentsList');
            const commentsCount = document.getElementById('commentsCount');

            commentsCount.textContent = comments.length;

            if (comments.length === 0) {
                commentsContainer.innerHTML = `
                    <div class="no-comments">
                        <i class="fas fa-comments"></i>
                        <p>暂无评论，来发表第一条评论吧！</p>
                    </div>
                `;
                return;
            }

            commentsContainer.innerHTML = comments.map(comment => `
                <div class="comment-item">
                    <div class="comment-header">
                        <span class="comment-author">${comment.author || '匿名用户'}</span>
                        <span class="comment-time">${formatDate(comment.createdAt)}</span>
                    </div>
                    <div class="comment-content">${comment.content}</div>
                </div>
            `).join('');
        }

        // 添加评论
        async function addComment() {
            if (!currentFeedbackId) return;

            const authorInput = document.getElementById('commenterName');
            const contentInput = document.getElementById('commentContent');

            const author = authorInput.value.trim() || '匿名用户';
            const content = contentInput.value.trim();

            if (!content) {
                alert('请输入评论内容');
                return;
            }

            const addCommentBtn = document.getElementById('addCommentBtn');
            const originalText = addCommentBtn.innerHTML;
            addCommentBtn.disabled = true;
            addCommentBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发表中...';

            try {
                // 尝试使用API添加评论
                if (window.FeedbackAPI) {
                    const result = await window.FeedbackAPI.addComment(currentFeedbackId, {
                        content: content,
                        author: author
                    });

                    if (result.success) {
                        // API成功，创建本地评论对象
                        const newComment = {
                            id: result.data.commentId || 'comment-' + Date.now(),
                            author: author,
                            content: content,
                            createdAt: result.data.createdAt || new Date().toISOString()
                        };

                        // 添加到评论数据
                        if (!commentsData[currentFeedbackId]) {
                            commentsData[currentFeedbackId] = [];
                        }
                        commentsData[currentFeedbackId].push(newComment);

                        // 保存到本地存储
                        localStorage.setItem('commentsData', JSON.stringify(commentsData));

                        // 清空输入框并更新显示
                        authorInput.value = '';
                        contentInput.value = '';
                        renderComments();

                        addCommentBtn.disabled = false;
                        addCommentBtn.innerHTML = originalText;
                        return;
                    }
                }

                // API失败或不可用，使用本地存储
                throw new Error('API不可用，使用本地存储');

            } catch (error) {
                console.warn('API添加评论失败，使用本地存储:', error);

                // 本地存储备用方案
                const newComment = {
                    id: 'comment-' + Date.now(),
                    author: author,
                    content: content,
                    createdAt: new Date().toISOString()
                };

                // 添加到评论数据
                if (!commentsData[currentFeedbackId]) {
                    commentsData[currentFeedbackId] = [];
                }
                commentsData[currentFeedbackId].push(newComment);

                // 保存到本地存储
                localStorage.setItem('commentsData', JSON.stringify(commentsData));

                // 清空输入框并更新显示
                authorInput.value = '';
                contentInput.value = '';
                renderComments();

                addCommentBtn.disabled = false;
                addCommentBtn.innerHTML = originalText;
        }

        // 加载反馈数据
        async function loadFeedbackData() {
            try {
                // 尝试从API加载反馈数据
                if (window.FeedbackAPI) {
                    const result = await window.FeedbackAPI.getFeedbackList({
                        page: 1,
                        limit: 50
                    });

                    if (result.success && result.data.feedbacks) {
                        feedbackData = result.data.feedbacks.map(feedback => ({
                            id: feedback.ticketNumber || feedback.feedbackId,
                            title: feedback.title,
                            category: feedback.category,
                            priority: feedback.priority,
                            content: feedback.description,
                            status: feedback.status,
                            createdAt: feedback.createdAt,
                            updatedAt: feedback.updatedAt || feedback.createdAt
                        }));

                        // 保存到本地存储作为缓存
                        localStorage.setItem('feedbackData', JSON.stringify(feedbackData));
                        return;
                    }
                }

                // API失败或不可用，使用本地存储
                throw new Error('API不可用，使用本地数据');

            } catch (error) {
                console.warn('API加载失败，使用本地数据:', error);

                // 从本地存储加载
                const savedData = localStorage.getItem('feedbackData');
                if (savedData) {
                    feedbackData = JSON.parse(savedData);
                } else {
                    // 初始化示例数据
                    feedbackData = [
                    {
                        id: 'FB-20241201-001',
                        title: '数据上传功能建议',
                        category: 'feature',
                        priority: 'medium',
                        content: '希望能支持更多的文件格式，比如Excel和JSON格式的数据导入。',
                        status: 'in-progress',
                        createdAt: '2024-12-01T10:30:00Z',
                        updatedAt: '2024-12-01T14:20:00Z'
                    },
                    {
                        id: 'FB-20241130-002',
                        title: '图表显示异常',
                        category: 'bug',
                        priority: 'high',
                        content: '在Chrome浏览器中，频谱分析图表有时会显示不完整，刷新页面后恢复正常。',
                        status: 'resolved',
                        createdAt: '2024-11-30T16:45:00Z',
                        updatedAt: '2024-12-01T09:15:00Z'
                    },
                    {
                        id: 'FB-20241129-003',
                        title: '界面优化建议',
                        category: 'ux',
                        priority: 'low',
                        content: '左侧导航栏可以考虑添加收起功能，在小屏幕设备上使用会更方便。',
                        status: 'new',
                        createdAt: '2024-11-29T14:20:00Z',
                        updatedAt: '2024-11-29T14:20:00Z'
                    }
                    ];
                    localStorage.setItem('feedbackData', JSON.stringify(feedbackData));
                }
            }
            renderFeedbackList();
        }

        // 更新统计数据
        function updateStats() {
            const total = feedbackData.length;
            const resolved = feedbackData.filter(f => f.status === 'resolved').length;
            const pending = feedbackData.filter(f => f.status === 'in-progress').length;
            const newFeedbacks = feedbackData.filter(f => f.status === 'new').length;

            document.getElementById('totalFeedbacks').textContent = total;
            document.getElementById('resolvedFeedbacks').textContent = resolved;
            document.getElementById('pendingFeedbacks').textContent = pending;
            document.getElementById('newFeedbacks').textContent = newFeedbacks;
        }

        // 渲染反馈列表
        function renderFeedbackList() {
            const container = document.getElementById('feedbackList');
            const categoryFilter = document.getElementById('categoryFilter').value;

            let filteredData = feedbackData;
            if (categoryFilter) {
                filteredData = feedbackData.filter(f => f.category === categoryFilter);
            }

            const startIndex = 0;
            const endIndex = currentPage * itemsPerPage;
            const displayData = filteredData.slice(startIndex, endIndex);

            if (displayData.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #64748b;">
                        <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>暂无反馈数据</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = displayData.map(feedback => `
                <div class="feedback-item ${feedback.category}" onclick="showFeedbackDetail('${feedback.id}')">
                    <div class="feedback-header-info">
                        <div>
                            <div class="feedback-title">${feedback.title}</div>
                            <div class="feedback-meta">
                                <span class="feedback-status ${feedback.status}">${getStatusText(feedback.status)}</span>
                                <span><i class="fas fa-tag"></i> ${getCategoryText(feedback.category)}</span>
                                <span><i class="fas fa-clock"></i> ${formatDate(feedback.createdAt)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="feedback-content-preview">${feedback.content}</div>
                </div>
            `).join('');

            // 更新加载更多按钮
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            if (endIndex >= filteredData.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.style.display = 'block';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'new': '新反馈',
                'in-progress': '处理中',
                'resolved': '已解决'
            };
            return statusMap[status] || status;
        }

        // 获取分类文本
        function getCategoryText(category) {
            const categoryMap = {
                'bug': '错误报告',
                'feature': '功能建议',
                'ux': '用户体验',
                'content': '内容问题',
                'performance': '性能问题',
                'other': '其他'
            };
            return categoryMap[category] || category;
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 1) {
                return '今天';
            } else if (diffDays === 2) {
                return '昨天';
            } else if (diffDays <= 7) {
                return `${diffDays - 1}天前`;
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        }

        // 过滤反馈
        function filterFeedbacks() {
            currentPage = 1;
            renderFeedbackList();
        }

        // 加载更多反馈
        function loadMoreFeedbacks() {
            currentPage++;
            renderFeedbackList();
        }

        // 点击反馈项时调用详情查看（在上面已经重新实现）

        // 移动端导航切换
        function toggleMobileNav() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // 点击页面其他地方关闭移动端导航
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.sidebar');
            const mobileNavToggle = document.getElementById('mobileNavToggle');

            if (window.innerWidth <= 480 &&
                sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) &&
                !mobileNavToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });

        // 设置新功能事件
        function setupNewFeatureEvents() {
            // 立即评价按钮
            const rateNowBtn = document.getElementById('rateNowBtn');
            if (rateNowBtn) {
                rateNowBtn.addEventListener('click', showRatingModal);
            }

            // 快速操作按钮
            const exportBtn = document.getElementById('exportBtn');
            const clearDraftBtn = document.getElementById('clearDraftBtn');
            const templateBtn = document.getElementById('templateBtn');
            const helpBtn = document.getElementById('helpBtn');

            if (exportBtn) exportBtn.addEventListener('click', exportFeedbacks);
            if (clearDraftBtn) clearDraftBtn.addEventListener('click', clearDraft);
            if (templateBtn) templateBtn.addEventListener('click', showTemplates);
            if (helpBtn) helpBtn.addEventListener('click', showHelp);
        }

        // 显示评价模态框
        function showRatingModal() {
            document.getElementById('ratingModal').classList.add('show');
            initializeRatingModal();
        }

        // 初始化评价模态框
        function initializeRatingModal() {
            // 重置所有评分
            document.querySelectorAll('.star-rating').forEach(rating => {
                rating.querySelectorAll('.star').forEach(star => {
                    star.classList.remove('active');
                    star.querySelector('i').className = 'far fa-star';
                });
            });

            // 重置评价文本
            document.getElementById('ratingText').textContent = '请选择您的评分';

            // 重置评论
            document.getElementById('ratingComment').value = '';

            // 重置标签
            document.querySelectorAll('.rating-tag').forEach(tag => {
                tag.classList.remove('selected');
            });

            // 设置星级评价事件
            setupStarRating();

            // 设置标签选择事件
            setupTagSelection();

            // 设置模态框关闭事件
            setupRatingModalEvents();
        }

        // 设置星级评价
        function setupStarRating() {
            document.querySelectorAll('.star-rating').forEach(ratingContainer => {
                const stars = ratingContainer.querySelectorAll('.star');

                stars.forEach((star, index) => {
                    star.addEventListener('mouseenter', () => {
                        highlightStars(stars, index + 1);
                    });

                    star.addEventListener('mouseleave', () => {
                        const rating = ratingContainer.dataset.rating || 0;
                        highlightStars(stars, rating);
                    });

                    star.addEventListener('click', () => {
                        const rating = index + 1;
                        ratingContainer.dataset.rating = rating;
                        setStarRating(stars, rating);

                        // 更新整体评价文本
                        if (ratingContainer.id === 'overallRating') {
                            updateRatingText(rating);
                        }
                    });
                });
            });
        }

        // 高亮星星
        function highlightStars(stars, count) {
            stars.forEach((star, index) => {
                if (index < count) {
                    star.classList.add('active');
                    star.querySelector('i').className = 'fas fa-star';
                } else {
                    star.classList.remove('active');
                    star.querySelector('i').className = 'far fa-star';
                }
            });
        }

        // 设置星级评分
        function setStarRating(stars, rating) {
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('active');
                    star.querySelector('i').className = 'fas fa-star';
                } else {
                    star.classList.remove('active');
                    star.querySelector('i').className = 'far fa-star';
                }
            });
        }

        // 更新评价文本
        function updateRatingText(rating) {
            const texts = {
                1: '很不满意 😞',
                2: '不满意 😕',
                3: '一般 😐',
                4: '满意 😊',
                5: '非常满意 😍'
            };
            document.getElementById('ratingText').textContent = texts[rating] || '请选择您的评分';
        }

        // 设置标签选择
        function setupTagSelection() {
            document.querySelectorAll('.rating-tag').forEach(tag => {
                tag.addEventListener('click', () => {
                    tag.classList.toggle('selected');
                });
            });
        }

        // 设置评价模态框事件
        function setupRatingModalEvents() {
            // 关闭按钮
            document.getElementById('closeRatingBtn').addEventListener('click', closeRatingModal);
            document.getElementById('cancelRatingBtn').addEventListener('click', closeRatingModal);

            // 提交按钮
            document.getElementById('submitRatingBtn').addEventListener('click', submitRating);
        }

        // 关闭评价模态框
        function closeRatingModal() {
            document.getElementById('ratingModal').classList.remove('show');
        }

        // 提交评价
        function submitRating() {
            const overallRating = document.getElementById('overallRating').dataset.rating;

            if (!overallRating) {
                showToast('请先进行整体评分', 'error');
                return;
            }

            // 收集评价数据
            const ratingData = {
                overall: parseInt(overallRating),
                aspects: {
                    design: document.querySelector('[data-aspect="design"]').dataset.rating || 0,
                    functionality: document.querySelector('[data-aspect="functionality"]').dataset.rating || 0,
                    performance: document.querySelector('[data-aspect="performance"]').dataset.rating || 0,
                    usability: document.querySelector('[data-aspect="usability"]').dataset.rating || 0
                },
                comment: document.getElementById('ratingComment').value.trim(),
                tags: Array.from(document.querySelectorAll('.rating-tag.selected')).map(tag => tag.dataset.tag),
                timestamp: new Date().toISOString()
            };

            // 保存评价数据
            saveRatingData(ratingData);

            // 更新评价显示
            updateRatingDisplay(ratingData);

            // 关闭模态框
            closeRatingModal();

            // 显示成功提示
            showToast('感谢您的评价！', 'success');
        }

        // 保存评价数据
        function saveRatingData(ratingData) {
            let ratings = JSON.parse(localStorage.getItem('userRatings') || '[]');
            ratings.push(ratingData);
            localStorage.setItem('userRatings', JSON.stringify(ratings));
        }

        // 更新评价显示
        function updateRatingDisplay(newRating) {
            const ratings = JSON.parse(localStorage.getItem('userRatings') || '[]');

            // 计算平均分
            const totalRatings = ratings.length;
            const averageRating = ratings.reduce((sum, rating) => sum + rating.overall, 0) / totalRatings;

            // 计算星级分布
            const distribution = [0, 0, 0, 0, 0];
            ratings.forEach(rating => {
                distribution[rating.overall - 1]++;
            });

            // 更新显示
            document.querySelector('.rating-score').textContent = averageRating.toFixed(1);
            document.querySelector('.rating-count').textContent = `基于 ${totalRatings} 条评价`;

            // 更新分布条
            distribution.forEach((count, index) => {
                const percentage = totalRatings > 0 ? (count / totalRatings * 100) : 0;
                const fillElement = document.querySelectorAll('.rating-fill')[4 - index];
                if (fillElement) {
                    fillElement.style.width = `${percentage}%`;
                }
                const percentElement = document.querySelectorAll('.rating-percent')[4 - index];
                if (percentElement) {
                    percentElement.textContent = `${Math.round(percentage)}%`;
                }
            });
        }

        // 导出反馈数据
        function exportFeedbacks() {
            const data = JSON.stringify(feedbackData, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `feedback-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // 显示成功提示
            showToast('反馈数据已导出', 'success');
        }

        // 清空草稿
        function clearDraft() {
            if (confirm('确定要清空当前草稿吗？此操作不可撤销。')) {
                localStorage.removeItem('feedbackDraft');
                document.getElementById('feedbackForm').reset();
                document.getElementById('draftIndicator').style.display = 'none';
                showToast('草稿已清空', 'success');
            }
        }

        // 显示反馈模板
        function showTemplates() {
            const templates = [
                {
                    title: '功能建议模板',
                    content: '建议功能：[请描述您希望添加的功能]\n\n使用场景：[请描述在什么情况下会用到这个功能]\n\n预期效果：[请描述您期望的效果]\n\n其他说明：[其他补充信息]'
                },
                {
                    title: '问题报告模板',
                    content: '问题描述：[请详细描述遇到的问题]\n\n重现步骤：\n1. [第一步操作]\n2. [第二步操作]\n3. [第三步操作]\n\n预期结果：[您期望的正常结果]\n\n实际结果：[实际发生的情况]\n\n浏览器信息：[浏览器类型和版本]'
                },
                {
                    title: '用户体验改进模板',
                    content: '当前体验：[请描述当前的使用体验]\n\n存在问题：[请指出体验中的不足之处]\n\n改进建议：[请提出您的改进建议]\n\n参考案例：[如果有参考的产品或功能，请说明]'
                }
            ];

            let templateHtml = '<h3>选择反馈模板</h3><div style="margin: 20px 0;">';
            templates.forEach((template, index) => {
                templateHtml += `
                    <div style="margin-bottom: 15px; padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px; cursor: pointer;"
                         onclick="applyTemplate(${index})">
                        <strong>${template.title}</strong>
                        <p style="margin: 5px 0 0; font-size: 14px; color: #64748b;">点击应用此模板</p>
                    </div>
                `;
            });
            templateHtml += '</div>';

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                align-items: center; justify-content: center;
            `;
            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    ${templateHtml}
                    <button onclick="this.closest('.modal').remove()"
                            style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            `;
            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // 应用模板
        function applyTemplate(templateIndex) {
            const templates = [
                {
                    title: '功能建议模板',
                    content: '建议功能：[请描述您希望添加的功能]\n\n使用场景：[请描述在什么情况下会用到这个功能]\n\n预期效果：[请描述您期望的效果]\n\n其他说明：[其他补充信息]'
                },
                {
                    title: '问题反馈模板',
                    content: '问题描述：[请详细描述遇到的问题]\n\n重现步骤：[请列出重现问题的具体步骤]\n\n预期结果：[请描述您期望的正确结果]\n\n实际结果：[请描述实际发生的情况]\n\n环境信息：[浏览器版本、操作系统等]'
                },
                {
                    title: '用户体验改进模板',
                    content: '当前体验：[请描述当前的使用体验]\n\n存在问题：[请指出体验中的不足之处]\n\n改进建议：[请提出您的改进建议]\n\n参考案例：[如果有参考的产品或功能，请说明]'
                }
            ];

            if (templates[templateIndex]) {
                document.getElementById('feedbackContent').value = templates[templateIndex].content;
                document.querySelector('.modal').remove();
                showToast('模板已应用', 'success');
            }
        }

        // 显示帮助信息
        function showHelp() {
            const helpContent = `
                <h3>使用帮助</h3>
                <div style="text-align: left; line-height: 1.6;">
                    <h4>如何提交反馈？</h4>
                    <ol>
                        <li>填写反馈标题（必填）</li>
                        <li>选择反馈分类和优先级</li>
                        <li>详细描述您的反馈内容</li>
                        <li>可选择上传相关截图</li>
                        <li>填写联系方式（可选）</li>
                        <li>点击"提交反馈"按钮</li>
                    </ol>

                    <h4>反馈分类说明</h4>
                    <ul>
                        <li><strong>错误报告</strong>：系统功能异常、页面显示错误等</li>
                        <li><strong>功能建议</strong>：希望添加的新功能</li>
                        <li><strong>用户体验改进</strong>：界面优化、操作流程改进等</li>
                        <li><strong>内容问题</strong>：文字错误、信息不准确等</li>
                        <li><strong>性能问题</strong>：页面加载慢、响应延迟等</li>
                    </ul>

                    <h4>优先级说明</h4>
                    <ul>
                        <li><strong>高</strong>：严重影响使用的问题</li>
                        <li><strong>中</strong>：一般性问题或建议</li>
                        <li><strong>低</strong>：轻微问题或优化建议</li>
                    </ul>

                    <h4>其他功能</h4>
                    <ul>
                        <li><strong>草稿保存</strong>：系统会自动保存您的输入内容</li>
                        <li><strong>反馈模板</strong>：提供标准化的反馈格式</li>
                        <li><strong>数据导出</strong>：可导出所有反馈数据</li>
                        <li><strong>评论功能</strong>：可对反馈进行评论和讨论</li>
                    </ul>
                </div>
            `;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                align-items: center; justify-content: center;
            `;
            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; max-width: 700px; width: 90%; max-height: 80vh; overflow-y: auto;">
                    ${helpContent}
                    <button onclick="this.closest('.modal').remove()"
                            style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; margin-top: 20px;">
                        关闭
                    </button>
                </div>
            `;
            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // 加载评价数据
        function loadRatingData() {
            const ratings = JSON.parse(localStorage.getItem('userRatings') || '[]');

            if (ratings.length > 0) {
                // 计算平均分
                const averageRating = ratings.reduce((sum, rating) => sum + rating.overall, 0) / ratings.length;

                // 计算星级分布
                const distribution = [0, 0, 0, 0, 0];
                ratings.forEach(rating => {
                    distribution[rating.overall - 1]++;
                });

                // 更新显示
                document.querySelector('.rating-score').textContent = averageRating.toFixed(1);
                document.querySelector('.rating-count').textContent = `基于 ${ratings.length} 条评价`;

                // 更新分布条
                distribution.forEach((count, index) => {
                    const percentage = (count / ratings.length * 100);
                    const fillElement = document.querySelectorAll('.rating-fill')[4 - index];
                    if (fillElement) {
                        fillElement.style.width = `${percentage}%`;
                    }
                    const percentElement = document.querySelectorAll('.rating-percent')[4 - index];
                    if (percentElement) {
                        percentElement.textContent = `${Math.round(percentage)}%`;
                    }
                });
            }
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10001;
                padding: 15px 20px; border-radius: 8px; color: white;
                font-weight: 600; box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                transform: translateX(100%); transition: transform 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => toast.style.transform = 'translateX(0)', 100);
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // 评价评论区功能
        let comments = JSON.parse(localStorage.getItem('userComments') || '[]');
        let currentSort = 'latest';

        // 初始化评价评论区
        function initCommentsSection() {
            loadComments();

            // 绑定事件
            const sortSelect = document.getElementById('commentsSort');
            const addCommentBtn = document.getElementById('addCommentBtn');
            const commentInput = document.getElementById('newCommentText');

            if (sortSelect) {
                sortSelect.addEventListener('change', (e) => {
                    currentSort = e.target.value;
                    loadComments();
                });
            }

            if (addCommentBtn) {
                addCommentBtn.addEventListener('click', addComment);
            }

            if (commentInput) {
                commentInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && e.ctrlKey) {
                        addComment();
                    }
                });
            }
        }

        // 加载评论列表
        function loadComments() {
            const commentsList = document.getElementById('commentsList');
            if (!commentsList) return;

            // 排序评论
            let sortedComments = [...comments];
            if (currentSort === 'most-liked') {
                sortedComments.sort((a, b) => b.likes - a.likes);
            } else {
                sortedComments.sort((a, b) => new Date(b.time) - new Date(a.time));
            }

            if (sortedComments.length === 0) {
                commentsList.innerHTML = `
                    <div class="empty-comments">
                        <i class="fas fa-comment-slash"></i>
                        <p>暂无评论</p>
                        <small>成为第一个发表评论的人吧！</small>
                    </div>
                `;
                return;
            }

            commentsList.innerHTML = sortedComments.map(comment => `
                <div class="comment-item" data-id="${comment.id}">
                    <div class="comment-header">
                        <span class="comment-author">${comment.author || '匿名用户'}</span>
                        <span class="comment-time">${formatTime(comment.time)}</span>
                    </div>
                    <div class="comment-content">${comment.content}</div>
                    <div class="comment-actions">
                        <button class="like-btn ${comment.liked ? 'liked' : ''}" onclick="toggleLike('${comment.id}')">
                            <i class="fas fa-heart"></i>
                            <span>${comment.likes}</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 添加评论
        function addComment() {
            const commentInput = document.getElementById('newCommentText');
            if (!commentInput) return;

            const content = commentInput.value.trim();
            if (!content) {
                showToast('请输入评论内容', 'error');
                return;
            }

            const newComment = {
                id: Date.now().toString(),
                content: content,
                author: '用户' + Math.floor(Math.random() * 1000),
                time: new Date().toISOString(),
                likes: 0,
                liked: false
            };

            comments.unshift(newComment);
            localStorage.setItem('userComments', JSON.stringify(comments));

            commentInput.value = '';
            loadComments();
            showToast('评论发表成功！', 'success');
        }

        // 切换点赞状态 (全局函数)
        window.toggleLike = function(commentId) {
            const comment = comments.find(c => c.id === commentId);
            if (!comment) return;

            if (comment.liked) {
                comment.likes--;
                comment.liked = false;
            } else {
                comment.likes++;
                comment.liked = true;
            }

            localStorage.setItem('userComments', JSON.stringify(comments));
            loadComments();
        }

        // 格式化时间
        function formatTime(timeString) {
            const time = new Date(timeString);
            const now = new Date();
            const diff = now - time;

            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';

            return time.toLocaleDateString();
        }

        // 生成示例评论数据
        function generateSampleComments() {
            if (comments.length === 0) {
                const sampleComments = [
                    {
                        id: '1',
                        content: '这个数据分析平台真的很好用，界面简洁，功能强大！',
                        author: '数据分析师小王',
                        time: new Date(Date.now() - 3600000).toISOString(),
                        likes: 15,
                        liked: false
                    },
                    {
                        id: '2',
                        content: '希望能增加更多的图表类型，特别是3D图表。',
                        author: '产品经理李明',
                        time: new Date(Date.now() - 7200000).toISOString(),
                        likes: 8,
                        liked: false
                    },
                    {
                        id: '3',
                        content: '串口调试功能很实用，解决了我的大问题！',
                        author: '硬件工程师张三',
                        time: new Date(Date.now() - 10800000).toISOString(),
                        likes: 12,
                        liked: false
                    },
                    {
                        id: '4',
                        content: '建议优化一下移动端的显示效果。',
                        author: '前端开发者小刘',
                        time: new Date(Date.now() - 14400000).toISOString(),
                        likes: 6,
                        liked: false
                    },
                    {
                        id: '5',
                        content: '数据上传速度很快，处理效率很高！',
                        author: '测试工程师小陈',
                        time: new Date(Date.now() - 18000000).toISOString(),
                        likes: 9,
                        liked: false
                    }
                ];

                comments = sampleComments;
                localStorage.setItem('userComments', JSON.stringify(comments));
            }
        }
    </script>

    <!-- API服务脚本 -->
    <script src="js/toast-system.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/global-error-handler.js"></script>
    <script src="js/loading-manager.js"></script>
    <script src="js/auth-guard.js"></script>
    <script src="js/feedback-api.js"></script>
    <script src="js/stats-api.js"></script>
    <script src="js/websocket-client.js"></script>
</body>
</html>
