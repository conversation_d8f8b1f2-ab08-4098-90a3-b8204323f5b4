{"version": 3, "file": "hammer.min.js", "sources": ["hammer.js"], "names": ["window", "document", "exportName", "undefined", "setTimeoutContext", "fn", "timeout", "context", "setTimeout", "bindFn", "invokeArrayArg", "arg", "Array", "isArray", "each", "obj", "iterator", "i", "for<PERSON>ach", "length", "call", "hasOwnProperty", "deprecate", "method", "name", "message", "deprecationMessage", "e", "Error", "stack", "replace", "log", "console", "warn", "apply", "this", "arguments", "inherit", "child", "base", "properties", "childP", "baseP", "prototype", "Object", "create", "constructor", "_super", "assign", "boolOrFn", "val", "args", "TYPE_FUNCTION", "ifUndefined", "val1", "val2", "addEventListeners", "target", "types", "handler", "splitStr", "type", "addEventListener", "removeEventListeners", "removeEventListener", "hasParent", "node", "parent", "parentNode", "inStr", "str", "find", "indexOf", "trim", "split", "inArray", "src", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toArray", "slice", "uniqueArray", "key", "sort", "results", "values", "push", "a", "b", "prefixed", "property", "prefix", "prop", "camelProp", "toUpperCase", "VENDOR_PREFIXES", "uniqueId", "_uniqueId", "getWindowForElement", "element", "doc", "ownerDocument", "defaultView", "parentWindow", "Input", "manager", "callback", "self", "options", "inputTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ev", "enable", "init", "createInputInstance", "Type", "inputClass", "SUPPORT_POINTER_EVENTS", "PointerEventInput", "SUPPORT_ONLY_TOUCH", "TouchInput", "SUPPORT_TOUCH", "TouchMouseInput", "MouseInput", "inputHandler", "eventType", "input", "pointersLen", "pointers", "changedPointersLen", "changedPointers", "<PERSON><PERSON><PERSON><PERSON>", "INPUT_START", "isFinal", "INPUT_END", "INPUT_CANCEL", "session", "computeInputData", "emit", "recognize", "prevInput", "pointers<PERSON><PERSON><PERSON>", "firstInput", "simpleCloneInputData", "firstMultiple", "offsetCenter", "center", "getCenter", "timeStamp", "now", "deltaTime", "angle", "getAngle", "distance", "getDistance", "computeDeltaXY", "offsetDirection", "getDirection", "deltaX", "deltaY", "overallVelocity", "getVelocity", "overallVelocityX", "x", "overallVelocityY", "y", "abs", "scale", "getScale", "rotation", "getRotation", "maxPointers", "computeIntervalInputData", "srcEvent", "offset", "offsetDelta", "prevDel<PERSON>", "velocity", "velocityX", "velocityY", "direction", "last", "lastInterval", "COMPUTE_INTERVAL", "v", "clientX", "round", "clientY", "DIRECTION_NONE", "DIRECTION_LEFT", "DIRECTION_RIGHT", "DIRECTION_UP", "DIRECTION_DOWN", "p1", "p2", "props", "PROPS_XY", "Math", "sqrt", "atan2", "PI", "start", "end", "PROPS_CLIENT_XY", "evEl", "MOUSE_ELEMENT_EVENTS", "evWin", "MOUSE_WINDOW_EVENTS", "allow", "pressed", "POINTER_ELEMENT_EVENTS", "POINTER_WINDOW_EVENTS", "store", "pointerEvents", "SingleTouchInput", "ev<PERSON><PERSON><PERSON>", "SINGLE_TOUCH_TARGET_EVENTS", "SINGLE_TOUCH_WINDOW_EVENTS", "started", "normalizeSingleTouches", "all", "touches", "changed", "changedTouches", "concat", "TOUCH_TARGET_EVENTS", "targetIds", "getTouches", "allTouches", "INPUT_MOVE", "identifier", "targetTouches", "changedTargetTouches", "filter", "touch", "mouse", "TouchAction", "value", "set", "cleanTouchActions", "actions", "TOUCH_ACTION_NONE", "hasPanX", "TOUCH_ACTION_PAN_X", "hasPanY", "TOUCH_ACTION_PAN_Y", "TOUCH_ACTION_MANIPULATION", "TOUCH_ACTION_AUTO", "Recognizer", "defaults", "id", "state", "STATE_POSSIBLE", "simultaneous", "requireFail", "stateStr", "STATE_CANCELLED", "STATE_ENDED", "STATE_CHANGED", "STATE_BEGAN", "directionStr", "getRecognizerByNameIfManager", "otherRecognizer", "recognizer", "get", "AttrRecognizer", "PanRecognizer", "pX", "pY", "PinchRecognizer", "PressRecognizer", "_timer", "_input", "RotateRecognizer", "SwipeRecognizer", "TapRecognizer", "pTime", "pCenter", "count", "Hammer", "recognizers", "preset", "Manager", "handlers", "touchAction", "toggleCssProps", "item", "add", "recognizeWith", "requireFailure", "style", "cssProps", "triggerDomEvent", "event", "data", "gestureEvent", "createEvent", "initEvent", "gesture", "dispatchEvent", "TEST_ELEMENT", "createElement", "Date", "TypeError", "output", "index", "source", "<PERSON><PERSON><PERSON>", "extend", "dest", "merge", "keys", "MOBILE_REGEX", "test", "navigator", "userAgent", "INPUT_TYPE_TOUCH", "INPUT_TYPE_PEN", "INPUT_TYPE_MOUSE", "INPUT_TYPE_KINECT", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_ALL", "destroy", "MOUSE_INPUT_MAP", "mousedown", "mousemove", "mouseup", "button", "which", "pointerType", "POINTER_INPUT_MAP", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerout", "IE10_POINTER_TYPE_ENUM", 2, 3, 4, 5, "MSPointerEvent", "PointerEvent", "removePointer", "eventTypeNormalized", "toLowerCase", "is<PERSON><PERSON>ch", "storeIndex", "pointerId", "splice", "SINGLE_TOUCH_INPUT_MAP", "touchstart", "touchmove", "touchend", "touchcancel", "TOUCH_INPUT_MAP", "inputEvent", "inputData", "isMouse", "PREFIXED_TOUCH_ACTION", "NATIVE_TOUCH_ACTION", "TOUCH_ACTION_COMPUTE", "compute", "update", "getTouchAction", "join", "preventDefaults", "prevented", "preventDefault", "hasNone", "isTapPointer", "isTapMovement", "isTapTouchTime", "preventSrc", "STATE_RECOGNIZED", "STATE_FAILED", "dropRecognizeWith", "dropRequireFailure", "hasRequireFailures", "canRecognizeWith", "additionalEvent", "tryEmit", "canEmit", "inputDataClone", "process", "reset", "attrTest", "optionPointers", "isRecognized", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "directionTest", "hasMoved", "inOut", "time", "validPointers", "validMovement", "validTime", "clearTimeout", "taps", "interval", "pos<PERSON><PERSON><PERSON><PERSON>", "validTouchTime", "failTimeout", "validInterval", "validMultiTap", "tapCount", "VERSION", "domEvents", "userSelect", "touchSelect", "touchCallout", "contentZooming", "userDrag", "tapHighlightColor", "STOP", "FORCED_STOP", "stop", "force", "stopped", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing", "remove", "on", "events", "off", "Tap", "Pan", "Swipe", "Pinch", "Rotate", "Press", "freeGlobal", "define", "amd", "module", "exports"], "mappings": "CAAA,SAAUA,EAAQC,EAAUC,EAAYC,GACtC,YAkBF,SAASC,GAAkBC,EAAIC,EAASC,GACpC,MAAOC,YAAWC,EAAOJ,EAAIE,GAAUD,GAY3C,QAASI,GAAeC,EAAKN,EAAIE,GAC7B,MAAIK,OAAMC,QAAQF,IACdG,EAAKH,EAAKJ,EAAQF,GAAKE,IAChB,IAEJ,EASX,QAASO,GAAKC,EAAKC,EAAUT,GACzB,GAAIU,EAEJ,IAAKF,EAIL,GAAIA,EAAIG,QACJH,EAAIG,QAAQF,EAAUT,OACnB,IAAIQ,EAAII,SAAWhB,EAEtB,IADAc,EAAI,EACGA,EAAIF,EAAII,QACXH,EAASI,KAAKb,EAASQ,EAAIE,GAAIA,EAAGF,GAClCE,QAGJ,KAAKA,IAAKF,GACNA,EAAIM,eAAeJ,IAAMD,EAASI,KAAKb,EAASQ,EAAIE,GAAIA,EAAGF,GAYvE,QAASO,GAAUC,EAAQC,EAAMC,GAC7B,GAAIC,GAAqB,sBAAwBF,EAAO,KAAOC,EAAU,QACzE,OAAO,YACH,GAAIE,GAAI,GAAIC,OAAM,mBACdC,EAAQF,GAAKA,EAAEE,MAAQF,EAAEE,MAAMC,QAAQ,kBAAmB,IACzDA,QAAQ,cAAe,IACvBA,QAAQ,6BAA8B,kBAAoB,sBAE3DC,EAAM/B,EAAOgC,UAAYhC,EAAOgC,QAAQC,MAAQjC,EAAOgC,QAAQD,IAInE,OAHIA,IACAA,EAAIX,KAAKpB,EAAOgC,QAASN,EAAoBG,GAE1CN,EAAOW,MAAMC,KAAMC,YAwElC,QAASC,GAAQC,EAAOC,EAAMC,GAC1B,GACIC,GADAC,EAAQH,EAAKI,SAGjBF,GAASH,EAAMK,UAAYC,OAAOC,OAAOH,GACzCD,EAAOK,YAAcR,EACrBG,EAAOM,OAASL,EAEZF,GACAQ,GAAOP,EAAQD,GAUvB,QAAS/B,GAAOJ,EAAIE,GAChB,MAAO,YACH,MAAOF,GAAG6B,MAAM3B,EAAS6B,YAWjC,QAASa,GAASC,EAAKC,GACnB,aAAWD,IAAOE,GACPF,EAAIhB,MAAMiB,EAAOA,EAAK,IAAMhD,EAAYA,EAAWgD,GAEvDD,EASX,QAASG,GAAYC,EAAMC,GACvB,MAAQD,KAASnD,EAAaoD,EAAOD,EASzC,QAASE,GAAkBC,EAAQC,EAAOC,GACtC7C,EAAK8C,EAASF,GAAQ,SAASG,GAC3BJ,EAAOK,iBAAiBD,EAAMF,GAAS,KAU/C,QAASI,GAAqBN,EAAQC,EAAOC,GACzC7C,EAAK8C,EAASF,GAAQ,SAASG,GAC3BJ,EAAOO,oBAAoBH,EAAMF,GAAS,KAWlD,QAASM,GAAUC,EAAMC,GACrB,KAAOD,GAAM,CACT,GAAIA,GAAQC,EACR,OAAO,CAEXD,GAAOA,EAAKE,WAEhB,OAAO,EASX,QAASC,GAAMC,EAAKC,GAChB,MAAOD,GAAIE,QAAQD,GAAQ,GAQ/B,QAASX,GAASU,GACd,MAAOA,GAAIG,OAAOC,MAAM,QAU5B,QAASC,GAAQC,EAAKL,EAAMM,GACxB,GAAID,EAAIJ,UAAYK,EAChB,MAAOD,GAAIJ,QAAQD,EAGnB,KADA,GAAItD,GAAI,EACDA,EAAI2D,EAAIzD,QAAQ,CACnB,GAAK0D,GAAaD,EAAI3D,GAAG4D,IAAcN,IAAWM,GAAaD,EAAI3D,KAAOsD,EACtE,MAAOtD,EAEXA,KAEJ,MAAO,GASf,QAAS6D,GAAQ/D,GACb,MAAOH,OAAM+B,UAAUoC,MAAM3D,KAAKL,EAAK,GAU3C,QAASiE,GAAYJ,EAAKK,EAAKC,GAK3B,IAJA,GAAIC,MACAC,KACAnE,EAAI,EAEDA,EAAI2D,EAAIzD,QAAQ,CACnB,GAAI+B,GAAM+B,EAAML,EAAI3D,GAAGgE,GAAOL,EAAI3D,EAC9B0D,GAAQS,EAAQlC,GAAO,GACvBiC,EAAQE,KAAKT,EAAI3D,IAErBmE,EAAOnE,GAAKiC,EACZjC,IAaJ,MAVIiE,KAIIC,EAHCF,EAGSE,EAAQD,KAAK,SAAyBI,EAAGC,GAC/C,MAAOD,GAAEL,GAAOM,EAAEN,KAHZE,EAAQD,QAQnBC,EASX,QAASK,GAASzE,EAAK0E,GAKnB,IAJA,GAAIC,GAAQC,EACRC,EAAYH,EAAS,GAAGI,cAAgBJ,EAASV,MAAM,GAEvD9D,EAAI,EACDA,EAAI6E,GAAgB3E,QAAQ,CAI/B,GAHAuE,EAASI,GAAgB7E,GACzB0E,EAAO,EAAWD,EAASE,EAAYH,EAEnCE,IAAQ5E,GACR,MAAO4E,EAEX1E,KAEJ,MAAOd,GAQX,QAAS4F,KACL,MAAOC,MAQX,QAASC,GAAoBC,GACzB,GAAIC,GAAMD,EAAQE,eAAiBF,CACnC,OAAQC,GAAIE,aAAeF,EAAIG,cAAgBtG,EAyCnD,QAASuG,GAAMC,EAASC,GACpB,GAAIC,GAAOvE,IACXA,MAAKqE,QAAUA,EACfrE,KAAKsE,SAAWA,EAChBtE,KAAK+D,QAAUM,EAAQN,QACvB/D,KAAKsB,OAAS+C,EAAQG,QAAQC,YAI9BzE,KAAK0E,WAAa,SAASC,GACnB7D,EAASuD,EAAQG,QAAQI,QAASP,KAClCE,EAAK/C,QAAQmD,IAIrB3E,KAAK6E,OAoCT,QAASC,GAAoBT,GACzB,GAAIU,GACAC,EAAaX,EAAQG,QAAQQ,UAajC,OAAO,KAVHD,EADAC,EACOA,EACAC,GACAC,EACAC,GACAC,EACCC,GAGDC,EAFAC,GAIOlB,EAASmB,GAS/B,QAASA,GAAanB,EAASoB,EAAWC,GACtC,GAAIC,GAAcD,EAAME,SAAS5G,OAC7B6G,EAAqBH,EAAMI,gBAAgB9G,OAC3C+G,EAAWN,EAAYO,IAAgBL,EAAcE,IAAuB,EAC5EI,EAAWR,GAAaS,GAAYC,KAAkBR,EAAcE,IAAuB,CAE/FH,GAAMK,UAAYA,EAClBL,EAAMO,UAAYA,EAEdF,IACA1B,EAAQ+B,YAKZV,EAAMD,UAAYA,EAGlBY,EAAiBhC,EAASqB,GAG1BrB,EAAQiC,KAAK,eAAgBZ,GAE7BrB,EAAQkC,UAAUb,GAClBrB,EAAQ+B,QAAQI,UAAYd,EAQhC,QAASW,GAAiBhC,EAASqB,GAC/B,GAAIU,GAAU/B,EAAQ+B,QAClBR,EAAWF,EAAME,SACjBa,EAAiBb,EAAS5G,MAGzBoH,GAAQM,aACTN,EAAQM,WAAaC,EAAqBjB,IAI1Ce,EAAiB,IAAML,EAAQQ,cAC/BR,EAAQQ,cAAgBD,EAAqBjB,GACnB,IAAnBe,IACPL,EAAQQ,eAAgB,EAG5B,IAAIF,GAAaN,EAAQM,WACrBE,EAAgBR,EAAQQ,cACxBC,EAAeD,EAAgBA,EAAcE,OAASJ,EAAWI,OAEjEA,EAASpB,EAAMoB,OAASC,EAAUnB,EACtCF,GAAMsB,UAAYC,KAClBvB,EAAMwB,UAAYxB,EAAMsB,UAAYN,EAAWM,UAE/CtB,EAAMyB,MAAQC,EAASP,EAAcC,GACrCpB,EAAM2B,SAAWC,EAAYT,EAAcC,GAE3CS,EAAenB,EAASV,GACxBA,EAAM8B,gBAAkBC,EAAa/B,EAAMgC,OAAQhC,EAAMiC,OAEzD,IAAIC,GAAkBC,EAAYnC,EAAMwB,UAAWxB,EAAMgC,OAAQhC,EAAMiC,OACvEjC,GAAMoC,iBAAmBF,EAAgBG,EACzCrC,EAAMsC,iBAAmBJ,EAAgBK,EACzCvC,EAAMkC,gBAAmBM,GAAIN,EAAgBG,GAAKG,GAAIN,EAAgBK,GAAML,EAAgBG,EAAIH,EAAgBK,EAEhHvC,EAAMyC,MAAQvB,EAAgBwB,EAASxB,EAAchB,SAAUA,GAAY,EAC3EF,EAAM2C,SAAWzB,EAAgB0B,EAAY1B,EAAchB,SAAUA,GAAY,EAEjFF,EAAM6C,YAAenC,EAAQI,UAAsCd,EAAME,SAAS5G,OAC9EoH,EAAQI,UAAU+B,YAAe7C,EAAME,SAAS5G,OAASoH,EAAQI,UAAU+B,YADtC7C,EAAME,SAAS5G,OAGxDwJ,EAAyBpC,EAASV,EAGlC,IAAIpE,GAAS+C,EAAQN,OACjBjC,GAAU4D,EAAM+C,SAASnH,OAAQA,KACjCA,EAASoE,EAAM+C,SAASnH,QAE5BoE,EAAMpE,OAASA,EAGnB,QAASiG,GAAenB,EAASV,GAC7B,GAAIoB,GAASpB,EAAMoB,OACf4B,EAAStC,EAAQuC,gBACjBC,EAAYxC,EAAQwC,cACpBpC,EAAYJ,EAAQI,eAEpBd,EAAMD,YAAcO,IAAeQ,EAAUf,YAAcS,MAC3D0C,EAAYxC,EAAQwC,WAChBb,EAAGvB,EAAUkB,QAAU,EACvBO,EAAGzB,EAAUmB,QAAU,GAG3Be,EAAStC,EAAQuC,aACbZ,EAAGjB,EAAOiB,EACVE,EAAGnB,EAAOmB,IAIlBvC,EAAMgC,OAASkB,EAAUb,GAAKjB,EAAOiB,EAAIW,EAAOX,GAChDrC,EAAMiC,OAASiB,EAAUX,GAAKnB,EAAOmB,EAAIS,EAAOT,GAQpD,QAASO,GAAyBpC,EAASV,GACvC,GAEImD,GAAUC,EAAWC,EAAWC,EAFhCC,EAAO7C,EAAQ8C,cAAgBxD,EAC/BwB,EAAYxB,EAAMsB,UAAYiC,EAAKjC,SAGvC,IAAItB,EAAMD,WAAaU,KAAiBe,EAAYiC,IAAoBF,EAAKJ,WAAa7K,GAAY,CAClG,GAAI0J,GAAShC,EAAMgC,OAASuB,EAAKvB,OAC7BC,EAASjC,EAAMiC,OAASsB,EAAKtB,OAE7ByB,EAAIvB,EAAYX,EAAWQ,EAAQC,EACvCmB,GAAYM,EAAErB,EACdgB,EAAYK,EAAEnB,EACdY,EAAYX,GAAIkB,EAAErB,GAAKG,GAAIkB,EAAEnB,GAAMmB,EAAErB,EAAIqB,EAAEnB,EAC3Ce,EAAYvB,EAAaC,EAAQC,GAEjCvB,EAAQ8C,aAAexD,MAGvBmD,GAAWI,EAAKJ,SAChBC,EAAYG,EAAKH,UACjBC,EAAYE,EAAKF,UACjBC,EAAYC,EAAKD,SAGrBtD,GAAMmD,SAAWA,EACjBnD,EAAMoD,UAAYA,EAClBpD,EAAMqD,UAAYA,EAClBrD,EAAMsD,UAAYA,EAQtB,QAASrC,GAAqBjB,GAK1B,IAFA,GAAIE,MACA9G,EAAI,EACDA,EAAI4G,EAAME,SAAS5G,QACtB4G,EAAS9G,IACLuK,QAASC,GAAM5D,EAAME,SAAS9G,GAAGuK,SACjCE,QAASD,GAAM5D,EAAME,SAAS9G,GAAGyK,UAErCzK,GAGJ,QACIkI,UAAWC,KACXrB,SAAUA,EACVkB,OAAQC,EAAUnB,GAClB8B,OAAQhC,EAAMgC,OACdC,OAAQjC,EAAMiC,QAStB,QAASZ,GAAUnB,GACf,GAAIa,GAAiBb,EAAS5G,MAG9B,IAAuB,IAAnByH,EACA,OACIsB,EAAGuB,GAAM1D,EAAS,GAAGyD,SACrBpB,EAAGqB,GAAM1D,EAAS,GAAG2D,SAK7B,KADA,GAAIxB,GAAI,EAAGE,EAAI,EAAGnJ,EAAI,EACX2H,EAAJ3H,GACHiJ,GAAKnC,EAAS9G,GAAGuK,QACjBpB,GAAKrC,EAAS9G,GAAGyK,QACjBzK,GAGJ,QACIiJ,EAAGuB,GAAMvB,EAAItB,GACbwB,EAAGqB,GAAMrB,EAAIxB,IAWrB,QAASoB,GAAYX,EAAWa,EAAGE,GAC/B,OACIF,EAAGA,EAAIb,GAAa,EACpBe,EAAGA,EAAIf,GAAa,GAU5B,QAASO,GAAaM,EAAGE,GACrB,MAAIF,KAAME,EACCuB,GAGPtB,GAAIH,IAAMG,GAAID,GACH,EAAJF,EAAQ0B,GAAiBC,GAEzB,EAAJzB,EAAQ0B,GAAeC,GAUlC,QAAStC,GAAYuC,EAAIC,EAAIC,GACpBA,IACDA,EAAQC,GAEZ,IAAIjC,GAAI+B,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5B9B,EAAI6B,EAAGC,EAAM,IAAMF,EAAGE,EAAM,GAEhC,OAAOE,MAAKC,KAAMnC,EAAIA,EAAME,EAAIA,GAUpC,QAASb,GAASyC,EAAIC,EAAIC,GACjBA,IACDA,EAAQC,GAEZ,IAAIjC,GAAI+B,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5B9B,EAAI6B,EAAGC,EAAM,IAAMF,EAAGE,EAAM,GAChC,OAA0B,KAAnBE,KAAKE,MAAMlC,EAAGF,GAAWkC,KAAKG,GASzC,QAAS9B,GAAY+B,EAAOC,GACxB,MAAOlD,GAASkD,EAAI,GAAIA,EAAI,GAAIC,IAAmBnD,EAASiD,EAAM,GAAIA,EAAM,GAAIE,IAUpF,QAASnC,GAASiC,EAAOC,GACrB,MAAOhD,GAAYgD,EAAI,GAAIA,EAAI,GAAIC,IAAmBjD,EAAY+C,EAAM,GAAIA,EAAM,GAAIE,IAiB1F,QAAShF,KACLvF,KAAKwK,KAAOC,GACZzK,KAAK0K,MAAQC,GAEb3K,KAAK4K,OAAQ,EACb5K,KAAK6K,SAAU,EAEfzG,EAAMrE,MAAMC,KAAMC,WAoEtB,QAASiF,KACLlF,KAAKwK,KAAOM,GACZ9K,KAAK0K,MAAQK,GAEb3G,EAAMrE,MAAMC,KAAMC,WAElBD,KAAKgL,MAAShL,KAAKqE,QAAQ+B,QAAQ6E,iBAoEvC,QAASC,KACLlL,KAAKmL,SAAWC,GAChBpL,KAAK0K,MAAQW,GACbrL,KAAKsL,SAAU,EAEflH,EAAMrE,MAAMC,KAAMC,WAsCtB,QAASsL,GAAuB5G,EAAIjD,GAChC,GAAI8J,GAAM7I,EAAQgC,EAAG8G,SACjBC,EAAU/I,EAAQgC,EAAGgH,eAMzB,OAJIjK,IAAQwE,GAAYC,MACpBqF,EAAM3I,EAAY2I,EAAII,OAAOF,GAAU,cAAc,KAGjDF,EAAKE,GAiBjB,QAAStG,KACLpF,KAAKmL,SAAWU,GAChB7L,KAAK8L,aAEL1H,EAAMrE,MAAMC,KAAMC,WA0BtB,QAAS8L,GAAWpH,EAAIjD,GACpB,GAAIsK,GAAarJ,EAAQgC,EAAG8G,SACxBK,EAAY9L,KAAK8L,SAGrB,IAAIpK,GAAQsE,GAAciG,KAAqC,IAAtBD,EAAWhN,OAEhD,MADA8M,GAAUE,EAAW,GAAGE,aAAc,GAC9BF,EAAYA,EAGxB,IAAIlN,GACAqN,EACAR,EAAiBhJ,EAAQgC,EAAGgH,gBAC5BS,KACA9K,EAAStB,KAAKsB,MAQlB,IALA6K,EAAgBH,EAAWK,OAAO,SAASC,GACvC,MAAOxK,GAAUwK,EAAMhL,OAAQA,KAI/BI,IAASsE,GAET,IADAlH,EAAI,EACGA,EAAIqN,EAAcnN,QACrB8M,EAAUK,EAAcrN,GAAGoN,aAAc,EACzCpN,GAMR,KADAA,EAAI,EACGA,EAAI6M,EAAe3M,QAClB8M,EAAUH,EAAe7M,GAAGoN,aAC5BE,EAAqBlJ,KAAKyI,EAAe7M,IAIzC4C,GAAQwE,GAAYC,WACb2F,GAAUH,EAAe7M,GAAGoN,YAEvCpN,GAGJ,OAAKsN,GAAqBpN,QAMtB6D,EAAYsJ,EAAcP,OAAOQ,GAAuB,cAAc,GACtEA,GAPJ,OAoBJ,QAAS9G,KACLlB,EAAMrE,MAAMC,KAAMC,UAElB,IAAIuB,GAAUlD,EAAO0B,KAAKwB,QAASxB,KACnCA,MAAKsM,MAAQ,GAAIlH,GAAWpF,KAAKqE,QAAS7C,GAC1CxB,KAAKuM,MAAQ,GAAIhH,GAAWvF,KAAKqE,QAAS7C,GAyD9C,QAASgL,GAAYnI,EAASoI,GAC1BzM,KAAKqE,QAAUA,EACfrE,KAAK0M,IAAID,GAwGb,QAASE,GAAkBC,GAEvB,GAAI1K,EAAM0K,EAASC,IACf,MAAOA,GAGX,IAAIC,GAAU5K,EAAM0K,EAASG,IACzBC,EAAU9K,EAAM0K,EAASK,GAM7B,OAAIH,IAAWE,EACJH,GAIPC,GAAWE,EACJF,EAAUC,GAAqBE,GAItC/K,EAAM0K,EAASM,IACRA,GAGJC,GA4CX,QAASC,GAAW5I,GAChBxE,KAAKwE,QAAU3D,MAAWb,KAAKqN,SAAU7I,OAEzCxE,KAAKsN,GAAK1J,IAEV5D,KAAKqE,QAAU,KAGfrE,KAAKwE,QAAQI,OAAS1D,EAAYlB,KAAKwE,QAAQI,QAAQ,GAEvD5E,KAAKuN,MAAQC,GAEbxN,KAAKyN,gBACLzN,KAAK0N,eAqOT,QAASC,GAASJ,GACd,MAAIA,GAAQK,GACD,SACAL,EAAQM,GACR,MACAN,EAAQO,GACR,OACAP,EAAQQ,GACR,QAEJ,GAQX,QAASC,GAAahF,GAClB,MAAIA,IAAaY,GACN,OACAZ,GAAaW,GACb,KACAX,GAAaS,GACb,OACAT,GAAaU,GACb,QAEJ,GASX,QAASuE,GAA6BC,EAAiBC,GACnD,GAAI9J,GAAU8J,EAAW9J,OACzB,OAAIA,GACOA,EAAQ+J,IAAIF,GAEhBA,EAQX,QAASG,KACLjB,EAAWrN,MAAMC,KAAMC,WA6D3B,QAASqO,KACLD,EAAetO,MAAMC,KAAMC,WAE3BD,KAAKuO,GAAK,KACVvO,KAAKwO,GAAK,KA4Ed,QAASC,KACLJ,EAAetO,MAAMC,KAAMC,WAsC/B,QAASyO,KACLtB,EAAWrN,MAAMC,KAAMC,WAEvBD,KAAK2O,OAAS,KACd3O,KAAK4O,OAAS,KAmElB,QAASC,MACLR,EAAetO,MAAMC,KAAMC,WA8B/B,QAAS6O,MACLT,EAAetO,MAAMC,KAAMC,WA2D/B,QAAS8O,MACL3B,EAAWrN,MAAMC,KAAMC,WAIvBD,KAAKgP,OAAQ,EACbhP,KAAKiP,SAAU,EAEfjP,KAAK2O,OAAS,KACd3O,KAAK4O,OAAS,KACd5O,KAAKkP,MAAQ,EAqGjB,QAASC,IAAOpL,EAASS,GAGrB,MAFAA,GAAUA,MACVA,EAAQ4K,YAAclO,EAAYsD,EAAQ4K,YAAaD,GAAO9B,SAASgC,QAChE,GAAIC,IAAQvL,EAASS,GAiIhC,QAAS8K,IAAQvL,EAASS,GACtBxE,KAAKwE,QAAU3D,MAAWsO,GAAO9B,SAAU7I,OAE3CxE,KAAKwE,QAAQC,YAAczE,KAAKwE,QAAQC,aAAeV,EAEvD/D,KAAKuP,YACLvP,KAAKoG,WACLpG,KAAKoP,eAELpP,KAAK+D,QAAUA,EACf/D,KAAK0F,MAAQZ,EAAoB9E,MACjCA,KAAKwP,YAAc,GAAIhD,GAAYxM,KAAMA,KAAKwE,QAAQgL,aAEtDC,GAAezP,MAAM,GAErBrB,EAAKqB,KAAKwE,QAAQ4K,YAAa,SAASM,GACpC,GAAIvB,GAAanO,KAAK2P,IAAI,GAAKD,GAAK,GAAIA,EAAK,IAC7CA,GAAK,IAAMvB,EAAWyB,cAAcF,EAAK,IACzCA,EAAK,IAAMvB,EAAW0B,eAAeH,EAAK,KAC3C1P,MAiPP,QAASyP,IAAepL,EAASsL,GAC7B,GAAI5L,GAAUM,EAAQN,OACjBA,GAAQ+L,OAGbnR,EAAK0F,EAAQG,QAAQuL,SAAU,SAAStD,EAAOpN,GAC3C0E,EAAQ+L,MAAMzM,EAASU,EAAQ+L,MAAOzQ,IAASsQ,EAAMlD,EAAQ,KASrE,QAASuD,IAAgBC,EAAOC,GAC5B,GAAIC,GAAerS,EAASsS,YAAY,QACxCD,GAAaE,UAAUJ,GAAO,GAAM,GACpCE,EAAaG,QAAUJ,EACvBA,EAAK5O,OAAOiP,cAAcJ,GAx7E9B,GA+FItP,IA/FA8C,IAAmB,GAAI,SAAU,MAAO,KAAM,KAAM,KACpD6M,GAAe1S,EAAS2S,cAAc,OAEtCxP,GAAgB,WAEhBqI,GAAQW,KAAKX,MACbpB,GAAM+B,KAAK/B,IACXjB,GAAMyJ,KAAKzJ,GA0FXpG,IADyB,kBAAlBJ,QAAOI,OACL,SAAgBS,GACrB,GAAIA,IAAWtD,GAAwB,OAAXsD,EACxB,KAAM,IAAIqP,WAAU,6CAIxB,KAAK,GADDC,GAASnQ,OAAOa,GACXuP,EAAQ,EAAGA,EAAQ5Q,UAAUjB,OAAQ6R,IAAS,CACnD,GAAIC,GAAS7Q,UAAU4Q,EACvB,IAAIC,IAAW9S,GAAwB,OAAX8S,EACxB,IAAK,GAAIC,KAAWD,GACZA,EAAO5R,eAAe6R,KACtBH,EAAOG,GAAWD,EAAOC,IAKzC,MAAOH,IAGFnQ,OAAOI,MAWpB,IAAImQ,IAAS7R,EAAU,SAAgB8R,EAAMxO,EAAKyO,GAG9C,IAFA,GAAIC,GAAO1Q,OAAO0Q,KAAK1O,GACnB3D,EAAI,EACDA,EAAIqS,EAAKnS,UACPkS,GAAUA,GAASD,EAAKE,EAAKrS,MAAQd,KACtCiT,EAAKE,EAAKrS,IAAM2D,EAAI0O,EAAKrS,KAE7BA,GAEJ,OAAOmS,IACR,SAAU,iBASTC,GAAQ/R,EAAU,SAAe8R,EAAMxO,GACvC,MAAOuO,IAAOC,EAAMxO,GAAK,IAC1B,QAAS,iBAiNRoB,GAAY,EAeZuN,GAAe,wCAEf/L,GAAiB,gBAAkBxH,GACnCoH,GAAyB5B,EAASxF,EAAQ,kBAAoBG,EAC9DmH,GAAqBE,IAAiB+L,GAAaC,KAAKC,UAAUC,WAElEC,GAAmB,QACnBC,GAAiB,MACjBC,GAAmB,QACnBC,GAAoB,SAEpBxI,GAAmB,GAEnBnD,GAAc,EACdiG,GAAa,EACb/F,GAAY,EACZC,GAAe,EAEfqD,GAAiB,EACjBC,GAAiB,EACjBC,GAAkB,EAClBC,GAAe,EACfC,GAAiB,GAEjBgI,GAAuBnI,GAAiBC,GACxCmI,GAAqBlI,GAAeC,GACpCkI,GAAgBF,GAAuBC,GAEvC7H,IAAY,IAAK,KACjBO,IAAmB,UAAW,UA4BlCnG,GAAM5D,WAKFgB,QAAS,aAKTqD,KAAM,WACF7E,KAAKwK,MAAQnJ,EAAkBrB,KAAK+D,QAAS/D,KAAKwK,KAAMxK,KAAK0E,YAC7D1E,KAAKmL,UAAY9J,EAAkBrB,KAAKsB,OAAQtB,KAAKmL,SAAUnL,KAAK0E,YACpE1E,KAAK0K,OAASrJ,EAAkByC,EAAoB9D,KAAK+D,SAAU/D,KAAK0K,MAAO1K,KAAK0E,aAMxFqN,QAAS,WACL/R,KAAKwK,MAAQ5I,EAAqB5B,KAAK+D,QAAS/D,KAAKwK,KAAMxK,KAAK0E,YAChE1E,KAAKmL,UAAYvJ,EAAqB5B,KAAKsB,OAAQtB,KAAKmL,SAAUnL,KAAK0E,YACvE1E,KAAK0K,OAAS9I,EAAqBkC,EAAoB9D,KAAK+D,SAAU/D,KAAK0K,MAAO1K,KAAK0E,aA4T/F,IAAIsN,KACAC,UAAWjM,GACXkM,UAAWjG,GACXkG,QAASjM,IAGTuE,GAAuB,YACvBE,GAAsB,mBAiB1BzK,GAAQqF,EAAYnB,GAKhB5C,QAAS,SAAmBmD,GACxB,GAAIc,GAAYuM,GAAgBrN,EAAGjD,KAG/B+D,GAAYO,IAA6B,IAAdrB,EAAGyN,SAC9BpS,KAAK6K,SAAU,GAGfpF,EAAYwG,IAA2B,IAAbtH,EAAG0N,QAC7B5M,EAAYS,IAIXlG,KAAK6K,SAAY7K,KAAK4K,QAIvBnF,EAAYS,KACZlG,KAAK6K,SAAU,GAGnB7K,KAAKsE,SAAStE,KAAKqE,QAASoB,GACxBG,UAAWjB,GACXmB,iBAAkBnB,GAClB2N,YAAaZ,GACbjJ,SAAU9D,OAKtB,IAAI4N,KACAC,YAAaxM,GACbyM,YAAaxG,GACbyG,UAAWxM,GACXyM,cAAexM,GACfyM,WAAYzM,IAIZ0M,IACAC,EAAGtB,GACHuB,EAAGtB,GACHuB,EAAGtB,GACHuB,EAAGtB,IAGH7G,GAAyB,cACzBC,GAAwB,qCAGxBlN,GAAOqV,iBAAmBrV,EAAOsV,eACjCrI,GAAyB,gBACzBC,GAAwB,6CAiB5B7K,EAAQgF,EAAmBd,GAKvB5C,QAAS,SAAmBmD,GACxB,GAAIqG,GAAQhL,KAAKgL,MACboI,GAAgB,EAEhBC,EAAsB1O,EAAGjD,KAAK4R,cAAc3T,QAAQ,KAAM,IAC1D8F,EAAY8M,GAAkBc,GAC9Bf,EAAcO,GAAuBlO,EAAG2N,cAAgB3N,EAAG2N,YAE3DiB,EAAWjB,GAAed,GAG1BgC,EAAahR,EAAQwI,EAAOrG,EAAG8O,UAAW,YAG1ChO,GAAYO,KAA8B,IAAdrB,EAAGyN,QAAgBmB,GAC9B,EAAbC,IACAxI,EAAM9H,KAAKyB,GACX6O,EAAaxI,EAAMhM,OAAS,GAEzByG,GAAaS,GAAYC,MAChCiN,GAAgB,GAIH,EAAbI,IAKJxI,EAAMwI,GAAc7O,EAEpB3E,KAAKsE,SAAStE,KAAKqE,QAASoB,GACxBG,SAAUoF,EACVlF,iBAAkBnB,GAClB2N,YAAaA,EACb7J,SAAU9D,IAGVyO,GAEApI,EAAM0I,OAAOF,EAAY,MAKrC,IAAIG,KACAC,WAAY5N,GACZ6N,UAAW5H,GACX6H,SAAU5N,GACV6N,YAAa5N,IAGbiF,GAA6B,aAC7BC,GAA6B,2CAejCnL,GAAQgL,EAAkB9G,GACtB5C,QAAS,SAAmBmD,GACxB,GAAIjD,GAAOiS,GAAuBhP,EAAGjD,KAOrC,IAJIA,IAASsE,KACThG,KAAKsL,SAAU,GAGdtL,KAAKsL,QAAV,CAIA,GAAIG,GAAUF,EAAuBtM,KAAKe,KAAM2E,EAAIjD,EAGhDA,IAAQwE,GAAYC,KAAiBsF,EAAQ,GAAGzM,OAASyM,EAAQ,GAAGzM,SAAW,IAC/EgB,KAAKsL,SAAU,GAGnBtL,KAAKsE,SAAStE,KAAKqE,QAAS3C,GACxBkE,SAAU6F,EAAQ,GAClB3F,gBAAiB2F,EAAQ,GACzB6G,YAAad,GACb/I,SAAU9D,OAsBtB,IAAIqP,KACAJ,WAAY5N,GACZ6N,UAAW5H,GACX6H,SAAU5N,GACV6N,YAAa5N,IAGb0F,GAAsB,2CAc1B3L,GAAQkF,EAAYhB,GAChB5C,QAAS,SAAoBmD,GACzB,GAAIjD,GAAOsS,GAAgBrP,EAAGjD,MAC1B+J,EAAUM,EAAW9M,KAAKe,KAAM2E,EAAIjD,EACnC+J,IAILzL,KAAKsE,SAAStE,KAAKqE,QAAS3C,GACxBkE,SAAU6F,EAAQ,GAClB3F,gBAAiB2F,EAAQ,GACzB6G,YAAad,GACb/I,SAAU9D,OAmFtBzE,EAAQoF,EAAiBlB,GAOrB5C,QAAS,SAAoB6C,EAAS4P,EAAYC,GAC9C,GAAIX,GAAWW,EAAU5B,aAAed,GACpC2C,EAAWD,EAAU5B,aAAeZ,EAIxC,IAAI6B,EACAvT,KAAKuM,MAAM3B,OAAQ,MAChB,IAAIuJ,IAAYnU,KAAKuM,MAAM3B,MAC9B,MAIAqJ,IAAc/N,GAAYC,MAC1BnG,KAAKuM,MAAM3B,OAAQ,GAGvB5K,KAAKsE,SAASD,EAAS4P,EAAYC,IAMvCnC,QAAS,WACL/R,KAAKsM,MAAMyF,UACX/R,KAAKuM,MAAMwF,YAInB,IAAIqC,IAAwB/Q,EAASmN,GAAaV,MAAO,eACrDuE,GAAsBD,KAA0BpW,EAGhDsW,GAAuB,UACvBnH,GAAoB,OACpBD,GAA4B,eAC5BL,GAAoB,OACpBE,GAAqB,QACrBE,GAAqB,OAczBT,GAAYhM,WAKRkM,IAAK,SAASD,GAENA,GAAS6H,KACT7H,EAAQzM,KAAKuU,WAGbF,IAAuBrU,KAAKqE,QAAQN,QAAQ+L,QAC5C9P,KAAKqE,QAAQN,QAAQ+L,MAAMsE,IAAyB3H,GAExDzM,KAAK4M,QAAUH,EAAM6G,cAAchR,QAMvCkS,OAAQ,WACJxU,KAAK0M,IAAI1M,KAAKqE,QAAQG,QAAQgL,cAOlC+E,QAAS,WACL,GAAI3H,KAMJ,OALAjO,GAAKqB,KAAKqE,QAAQ+K,YAAa,SAASjB,GAChCrN,EAASqN,EAAW3J,QAAQI,QAASuJ,MACrCvB,EAAUA,EAAQhB,OAAOuC,EAAWsG,qBAGrC9H,EAAkBC,EAAQ8H,KAAK,OAO1CC,gBAAiB,SAASjP,GAEtB,IAAI2O,GAAJ,CAIA,GAAI5L,GAAW/C,EAAM+C,SACjBO,EAAYtD,EAAM8B,eAGtB,IAAIxH,KAAKqE,QAAQ+B,QAAQwO,UAErB,WADAnM,GAASoM,gBAIb,IAAIjI,GAAU5M,KAAK4M,QACfkI,EAAU5S,EAAM0K,EAASC,IACzBG,EAAU9K,EAAM0K,EAASK,IACzBH,EAAU5K,EAAM0K,EAASG,GAE7B,IAAI+H,EAAS,CAGT,GAAIC,GAAyC,IAA1BrP,EAAME,SAAS5G,OAC9BgW,EAAgBtP,EAAM2B,SAAW,EACjC4N,EAAiBvP,EAAMwB,UAAY,GAEvC,IAAI6N,GAAgBC,GAAiBC,EACjC,OAIR,IAAInI,IAAWE,EAKf,MAAI8H,IACC9H,GAAWhE,EAAY4I,IACvB9E,GAAW9D,EAAY6I,GACjB7R,KAAKkV,WAAWzM,GAH3B,SAWJyM,WAAY,SAASzM,GACjBzI,KAAKqE,QAAQ+B,QAAQwO,WAAY,EACjCnM,EAASoM,kBAkEjB,IAAIrH,IAAiB,EACjBO,GAAc,EACdD,GAAgB,EAChBD,GAAc,EACdsH,GAAmBtH,GACnBD,GAAkB,GAClBwH,GAAe,EAwBnBhI,GAAW5M,WAKP6M,YAOAX,IAAK,SAASlI,GAKV,MAJA3D,IAAOb,KAAKwE,QAASA,GAGrBxE,KAAKqE,SAAWrE,KAAKqE,QAAQmL,YAAYgF,SAClCxU,MAQX4P,cAAe,SAAS1B,GACpB,GAAI3P,EAAe2P,EAAiB,gBAAiBlO,MACjD,MAAOA,KAGX,IAAIyN,GAAezN,KAAKyN,YAMxB,OALAS,GAAkBD,EAA6BC,EAAiBlO,MAC3DyN,EAAaS,EAAgBZ,MAC9BG,EAAaS,EAAgBZ,IAAMY,EACnCA,EAAgB0B,cAAc5P,OAE3BA,MAQXqV,kBAAmB,SAASnH,GACxB,MAAI3P,GAAe2P,EAAiB,oBAAqBlO,MAC9CA,MAGXkO,EAAkBD,EAA6BC,EAAiBlO,YACzDA,MAAKyN,aAAaS,EAAgBZ,IAClCtN,OAQX6P,eAAgB,SAAS3B,GACrB,GAAI3P,EAAe2P,EAAiB,iBAAkBlO,MAClD,MAAOA,KAGX,IAAI0N,GAAc1N,KAAK0N,WAMvB,OALAQ,GAAkBD,EAA6BC,EAAiBlO,MAClB,KAA1CwC,EAAQkL,EAAaQ,KACrBR,EAAYxK,KAAKgL,GACjBA,EAAgB2B,eAAe7P,OAE5BA,MAQXsV,mBAAoB,SAASpH,GACzB,GAAI3P,EAAe2P,EAAiB,qBAAsBlO,MACtD,MAAOA,KAGXkO,GAAkBD,EAA6BC,EAAiBlO,KAChE,IAAI6Q,GAAQrO,EAAQxC,KAAK0N,YAAaQ,EAItC,OAHI2C,GAAQ,IACR7Q,KAAK0N,YAAYgG,OAAO7C,EAAO,GAE5B7Q,MAOXuV,mBAAoB,WAChB,MAAOvV,MAAK0N,YAAY1O,OAAS,GAQrCwW,iBAAkB,SAAStH,GACvB,QAASlO,KAAKyN,aAAaS,EAAgBZ,KAQ/ChH,KAAM,SAASZ,GAIX,QAASY,GAAK2J,GACV1L,EAAKF,QAAQiC,KAAK2J,EAAOvK,GAJ7B,GAAInB,GAAOvE,KACPuN,EAAQvN,KAAKuN,KAOLM,IAARN,GACAjH,EAAK/B,EAAKC,QAAQyL,MAAQtC,EAASJ,IAGvCjH,EAAK/B,EAAKC,QAAQyL,OAEdvK,EAAM+P,iBACNnP,EAAKZ,EAAM+P,iBAIXlI,GAASM,IACTvH,EAAK/B,EAAKC,QAAQyL,MAAQtC,EAASJ,KAU3CmI,QAAS,SAAShQ,GACd,MAAI1F,MAAK2V,UACE3V,KAAKsG,KAAKZ,QAGrB1F,KAAKuN,MAAQ6H,KAOjBO,QAAS,WAEL,IADA,GAAI7W,GAAI,EACDA,EAAIkB,KAAK0N,YAAY1O,QAAQ,CAChC,KAAMgB,KAAK0N,YAAY5O,GAAGyO,OAAS6H,GAAe5H,KAC9C,OAAO,CAEX1O,KAEJ,OAAO,GAOXyH,UAAW,SAAS2N,GAGhB,GAAI0B,GAAiB/U,MAAWqT,EAGhC,OAAKpT,GAASd,KAAKwE,QAAQI,QAAS5E,KAAM4V,KAOtC5V,KAAKuN,OAAS4H,GAAmBvH,GAAkBwH,MACnDpV,KAAKuN,MAAQC,IAGjBxN,KAAKuN,MAAQvN,KAAK6V,QAAQD,QAItB5V,KAAKuN,OAASQ,GAAcD,GAAgBD,GAAcD,KAC1D5N,KAAK0V,QAAQE,MAfb5V,KAAK8V,aACL9V,KAAKuN,MAAQ6H,MAyBrBS,QAAS,aAOTpB,eAAgB,aAOhBqB,MAAO,cA8DX5V,EAAQmO,EAAgBjB,GAKpBC,UAKIzH,SAAU,GASdmQ,SAAU,SAASrQ,GACf,GAAIsQ,GAAiBhW,KAAKwE,QAAQoB,QAClC,OAA0B,KAAnBoQ,GAAwBtQ,EAAME,SAAS5G,SAAWgX,GAS7DH,QAAS,SAASnQ,GACd,GAAI6H,GAAQvN,KAAKuN,MACb9H,EAAYC,EAAMD,UAElBwQ,EAAe1I,GAASQ,GAAcD,IACtCoI,EAAUlW,KAAK+V,SAASrQ,EAG5B,OAAIuQ,KAAiBxQ,EAAYU,KAAiB+P,GACvC3I,EAAQK,GACRqI,GAAgBC,EACnBzQ,EAAYS,GACLqH,EAAQM,GACNN,EAAQQ,GAGdR,EAAQO,GAFJC,GAIRqH,MAiBflV,EAAQoO,EAAeD,GAKnBhB,UACI4C,MAAO,MACPkG,UAAW,GACXvQ,SAAU,EACVoD,UAAW8I,IAGf2C,eAAgB,WACZ,GAAIzL,GAAYhJ,KAAKwE,QAAQwE,UACzB4D,IAOJ,OANI5D,GAAY4I,IACZhF,EAAQ1J,KAAK+J,IAEbjE,EAAY6I,IACZjF,EAAQ1J,KAAK6J,IAEVH,GAGXwJ,cAAe,SAAS1Q,GACpB,GAAIlB,GAAUxE,KAAKwE,QACf6R,GAAW,EACXhP,EAAW3B,EAAM2B,SACjB2B,EAAYtD,EAAMsD,UAClBjB,EAAIrC,EAAMgC,OACVO,EAAIvC,EAAMiC,MAed,OAZMqB,GAAYxE,EAAQwE,YAClBxE,EAAQwE,UAAY4I,IACpB5I,EAAmB,IAANjB,EAAWyB,GAAsB,EAAJzB,EAAS0B,GAAiBC,GACpE2M,EAAWtO,GAAK/H,KAAKuO,GACrBlH,EAAW4C,KAAK/B,IAAIxC,EAAMgC,UAE1BsB,EAAmB,IAANf,EAAWuB,GAAsB,EAAJvB,EAAS0B,GAAeC,GAClEyM,EAAWpO,GAAKjI,KAAKwO,GACrBnH,EAAW4C,KAAK/B,IAAIxC,EAAMiC,UAGlCjC,EAAMsD,UAAYA,EACXqN,GAAYhP,EAAW7C,EAAQ2R,WAAanN,EAAYxE,EAAQwE,WAG3E+M,SAAU,SAASrQ,GACf,MAAO2I,GAAe7N,UAAUuV,SAAS9W,KAAKe,KAAM0F,KAC/C1F,KAAKuN,MAAQQ,MAAkB/N,KAAKuN,MAAQQ,KAAgB/N,KAAKoW,cAAc1Q,KAGxFY,KAAM,SAASZ,GAEX1F,KAAKuO,GAAK7I,EAAMgC,OAChB1H,KAAKwO,GAAK9I,EAAMiC,MAEhB,IAAIqB,GAAYgF,EAAatI,EAAMsD,UAE/BA,KACAtD,EAAM+P,gBAAkBzV,KAAKwE,QAAQyL,MAAQjH,GAEjDhJ,KAAKY,OAAO0F,KAAKrH,KAAKe,KAAM0F,MAcpCxF,EAAQuO,EAAiBJ,GAKrBhB,UACI4C,MAAO,QACPkG,UAAW,EACXvQ,SAAU,GAGd6O,eAAgB,WACZ,OAAQ5H,KAGZkJ,SAAU,SAASrQ,GACf,MAAO1F,MAAKY,OAAOmV,SAAS9W,KAAKe,KAAM0F,KAClCuE,KAAK/B,IAAIxC,EAAMyC,MAAQ,GAAKnI,KAAKwE,QAAQ2R,WAAanW,KAAKuN,MAAQQ,KAG5EzH,KAAM,SAASZ,GACX,GAAoB,IAAhBA,EAAMyC,MAAa,CACnB,GAAImO,GAAQ5Q,EAAMyC,MAAQ,EAAI,KAAO,KACrCzC,GAAM+P,gBAAkBzV,KAAKwE,QAAQyL,MAAQqG,EAEjDtW,KAAKY,OAAO0F,KAAKrH,KAAKe,KAAM0F,MAiBpCxF,EAAQwO,EAAiBtB,GAKrBC,UACI4C,MAAO,QACPrK,SAAU,EACV2Q,KAAM,IACNJ,UAAW,GAGf1B,eAAgB,WACZ,OAAQtH,KAGZ0I,QAAS,SAASnQ,GACd,GAAIlB,GAAUxE,KAAKwE,QACfgS,EAAgB9Q,EAAME,SAAS5G,SAAWwF,EAAQoB,SAClD6Q,EAAgB/Q,EAAM2B,SAAW7C,EAAQ2R,UACzCO,EAAYhR,EAAMwB,UAAY1C,EAAQ+R,IAM1C,IAJAvW,KAAK4O,OAASlJ,GAIT+Q,IAAkBD,GAAkB9Q,EAAMD,WAAaS,GAAYC,MAAkBuQ,EACtF1W,KAAK8V,YACF,IAAIpQ,EAAMD,UAAYO,GACzBhG,KAAK8V,QACL9V,KAAK2O,OAAS1Q,EAAkB,WAC5B+B,KAAKuN,MAAQ4H,GACbnV,KAAK0V,WACNlR,EAAQ+R,KAAMvW,UACd,IAAI0F,EAAMD,UAAYS,GACzB,MAAOiP,GAEX,OAAOC,KAGXU,MAAO,WACHa,aAAa3W,KAAK2O,SAGtBrI,KAAM,SAASZ,GACP1F,KAAKuN,QAAU4H,KAIfzP,GAAUA,EAAMD,UAAYS,GAC5BlG,KAAKqE,QAAQiC,KAAKtG,KAAKwE,QAAQyL,MAAQ,KAAMvK,IAE7C1F,KAAK4O,OAAO5H,UAAYC,KACxBjH,KAAKqE,QAAQiC,KAAKtG,KAAKwE,QAAQyL,MAAOjQ,KAAK4O,aAevD1O,EAAQ2O,GAAkBR,GAKtBhB,UACI4C,MAAO,SACPkG,UAAW,EACXvQ,SAAU,GAGd6O,eAAgB,WACZ,OAAQ5H,KAGZkJ,SAAU,SAASrQ,GACf,MAAO1F,MAAKY,OAAOmV,SAAS9W,KAAKe,KAAM0F,KAClCuE,KAAK/B,IAAIxC,EAAM2C,UAAYrI,KAAKwE,QAAQ2R,WAAanW,KAAKuN,MAAQQ,OAc/E7N,EAAQ4O,GAAiBT,GAKrBhB,UACI4C,MAAO,QACPkG,UAAW,GACXtN,SAAU,GACVG,UAAW4I,GAAuBC,GAClCjM,SAAU,GAGd6O,eAAgB,WACZ,MAAOnG,GAAc9N,UAAUiU,eAAexV,KAAKe,OAGvD+V,SAAU,SAASrQ,GACf,GACImD,GADAG,EAAYhJ,KAAKwE,QAAQwE,SAW7B,OARIA,IAAa4I,GAAuBC,IACpChJ,EAAWnD,EAAMkC,gBACVoB,EAAY4I,GACnB/I,EAAWnD,EAAMoC,iBACVkB,EAAY6I,KACnBhJ,EAAWnD,EAAMsC,kBAGdhI,KAAKY,OAAOmV,SAAS9W,KAAKe,KAAM0F,IACnCsD,EAAYtD,EAAM8B,iBAClB9B,EAAM2B,SAAWrH,KAAKwE,QAAQ2R,WAC9BzQ,EAAM6C,aAAevI,KAAKwE,QAAQoB,UAClCsC,GAAIW,GAAY7I,KAAKwE,QAAQqE,UAAYnD,EAAMD,UAAYS,IAGnEI,KAAM,SAASZ,GACX,GAAIsD,GAAYgF,EAAatI,EAAM8B,gBAC/BwB,IACAhJ,KAAKqE,QAAQiC,KAAKtG,KAAKwE,QAAQyL,MAAQjH,EAAWtD,GAGtD1F,KAAKqE,QAAQiC,KAAKtG,KAAKwE,QAAQyL,MAAOvK,MA2B9CxF,EAAQ6O,GAAe3B,GAKnBC,UACI4C,MAAO,MACPrK,SAAU,EACVgR,KAAM,EACNC,SAAU,IACVN,KAAM,IACNJ,UAAW,EACXW,aAAc,IAGlBrC,eAAgB,WACZ,OAAQvH,KAGZ2I,QAAS,SAASnQ,GACd,GAAIlB,GAAUxE,KAAKwE,QAEfgS,EAAgB9Q,EAAME,SAAS5G,SAAWwF,EAAQoB,SAClD6Q,EAAgB/Q,EAAM2B,SAAW7C,EAAQ2R,UACzCY,EAAiBrR,EAAMwB,UAAY1C,EAAQ+R,IAI/C,IAFAvW,KAAK8V,QAEApQ,EAAMD,UAAYO,IAAgC,IAAfhG,KAAKkP,MACzC,MAAOlP,MAAKgX,aAKhB,IAAIP,GAAiBM,GAAkBP,EAAe,CAClD,GAAI9Q,EAAMD,WAAaS,GACnB,MAAOlG,MAAKgX,aAGhB,IAAIC,GAAgBjX,KAAKgP,MAAStJ,EAAMsB,UAAYhH,KAAKgP,MAAQxK,EAAQqS,UAAY,EACjFK,GAAiBlX,KAAKiP,SAAW3H,EAAYtH,KAAKiP,QAASvJ,EAAMoB,QAAUtC,EAAQsS,YAEvF9W,MAAKgP,MAAQtJ,EAAMsB,UACnBhH,KAAKiP,QAAUvJ,EAAMoB,OAEhBoQ,GAAkBD,EAGnBjX,KAAKkP,OAAS,EAFdlP,KAAKkP,MAAQ,EAKjBlP,KAAK4O,OAASlJ,CAId,IAAIyR,GAAWnX,KAAKkP,MAAQ1K,EAAQoS,IACpC,IAAiB,IAAbO,EAGA,MAAKnX,MAAKuV,sBAGNvV,KAAK2O,OAAS1Q,EAAkB,WAC5B+B,KAAKuN,MAAQ4H,GACbnV,KAAK0V,WACNlR,EAAQqS,SAAU7W,MACd+N,IANAoH,GAUnB,MAAOC,KAGX4B,YAAa,WAIT,MAHAhX,MAAK2O,OAAS1Q,EAAkB,WAC5B+B,KAAKuN,MAAQ6H,IACdpV,KAAKwE,QAAQqS,SAAU7W,MACnBoV,IAGXU,MAAO,WACHa,aAAa3W,KAAK2O,SAGtBrI,KAAM,WACEtG,KAAKuN,OAAS4H,KACdnV,KAAK4O,OAAOuI,SAAWnX,KAAKkP,MAC5BlP,KAAKqE,QAAQiC,KAAKtG,KAAKwE,QAAQyL,MAAOjQ,KAAK4O,YAoBvDO,GAAOiI,QAAU,QAMjBjI,GAAO9B,UAOHgK,WAAW,EAQX7H,YAAa8E,GAMb1P,QAAQ,EASRH,YAAa,KAObO,WAAY,KAOZqK,SAEKR,IAAmBjK,QAAQ,KAC3B6J,GAAkB7J,QAAQ,IAAS,YACnCkK,IAAkB9F,UAAW4I,MAC7BtD,GAAgBtF,UAAW4I,KAAwB,WACnD7C,KACAA,IAAgBkB,MAAO,YAAa2G,KAAM,IAAK,SAC/ClI,IAQLqB,UAMIuH,WAAY,OAOZC,YAAa,OASbC,aAAc,OAOdC,eAAgB,OAOhBC,SAAU,OAQVC,kBAAmB,iBAI3B,IAAIC,IAAO,EACPC,GAAc,CA8BlBvI,IAAQ9O,WAMJkM,IAAK,SAASlI,GAaV,MAZA3D,IAAOb,KAAKwE,QAASA,GAGjBA,EAAQgL,aACRxP,KAAKwP,YAAYgF,SAEjBhQ,EAAQC,cAERzE,KAAK0F,MAAMqM,UACX/R,KAAK0F,MAAMpE,OAASkD,EAAQC,YAC5BzE,KAAK0F,MAAMb,QAER7E,MASX8X,KAAM,SAASC,GACX/X,KAAKoG,QAAQ4R,QAAUD,EAAQF,GAAcD,IASjDrR,UAAW,SAAS2N,GAChB,GAAI9N,GAAUpG,KAAKoG,OACnB,KAAIA,EAAQ4R,QAAZ,CAKAhY,KAAKwP,YAAYmF,gBAAgBT,EAEjC,IAAI/F,GACAiB,EAAcpP,KAAKoP,YAKnB6I,EAAgB7R,EAAQ6R,gBAIvBA,GAAkBA,GAAiBA,EAAc1K,MAAQ4H,MAC1D8C,EAAgB7R,EAAQ6R,cAAgB,KAI5C,KADA,GAAInZ,GAAI,EACDA,EAAIsQ,EAAYpQ,QACnBmP,EAAaiB,EAAYtQ,GAQrBsH,EAAQ4R,UAAYH,IACfI,GAAiB9J,GAAc8J,IAChC9J,EAAWqH,iBAAiByC,GAGhC9J,EAAW2H,QAFX3H,EAAW5H,UAAU2N,IAOpB+D,GAAiB9J,EAAWZ,OAASQ,GAAcD,GAAgBD,MACpEoK,EAAgB7R,EAAQ6R,cAAgB9J,GAE5CrP,MASRsP,IAAK,SAASD,GACV,GAAIA,YAAsBf,GACtB,MAAOe,EAIX,KAAK,GADDiB,GAAcpP,KAAKoP,YACdtQ,EAAI,EAAGA,EAAIsQ,EAAYpQ,OAAQF,IACpC,GAAIsQ,EAAYtQ,GAAG0F,QAAQyL,OAAS9B,EAChC,MAAOiB,GAAYtQ,EAG3B,OAAO,OASX6Q,IAAK,SAASxB,GACV,GAAI5P,EAAe4P,EAAY,MAAOnO,MAClC,MAAOA,KAIX,IAAIkY,GAAWlY,KAAKoO,IAAID,EAAW3J,QAAQyL,MAS3C,OARIiI,IACAlY,KAAKmY,OAAOD,GAGhBlY,KAAKoP,YAAYlM,KAAKiL,GACtBA,EAAW9J,QAAUrE,KAErBA,KAAKwP,YAAYgF,SACVrG,GAQXgK,OAAQ,SAAShK,GACb,GAAI5P,EAAe4P,EAAY,SAAUnO,MACrC,MAAOA,KAMX,IAHAmO,EAAanO,KAAKoO,IAAID,GAGN,CACZ,GAAIiB,GAAcpP,KAAKoP,YACnByB,EAAQrO,EAAQ4M,EAAajB,EAEnB,MAAV0C,IACAzB,EAAYsE,OAAO7C,EAAO,GAC1B7Q,KAAKwP,YAAYgF,UAIzB,MAAOxU,OASXoY,GAAI,SAASC,EAAQ7W,GACjB,GAAI+N,GAAWvP,KAAKuP,QAKpB,OAJA5Q,GAAK8C,EAAS4W,GAAS,SAASpI,GAC5BV,EAASU,GAASV,EAASU,OAC3BV,EAASU,GAAO/M,KAAK1B,KAElBxB,MASXsY,IAAK,SAASD,EAAQ7W,GAClB,GAAI+N,GAAWvP,KAAKuP,QAQpB,OAPA5Q,GAAK8C,EAAS4W,GAAS,SAASpI,GACvBzO,EAGD+N,EAASU,IAAUV,EAASU,GAAOyD,OAAOlR,EAAQ+M,EAASU,GAAQzO,GAAU,SAFtE+N,GAASU,KAKjBjQ,MAQXsG,KAAM,SAAS2J,EAAOC,GAEdlQ,KAAKwE,QAAQ6S,WACbrH,GAAgBC,EAAOC,EAI3B,IAAIX,GAAWvP,KAAKuP,SAASU,IAAUjQ,KAAKuP,SAASU,GAAOrN,OAC5D,IAAK2M,GAAaA,EAASvQ,OAA3B,CAIAkR,EAAKxO,KAAOuO,EACZC,EAAK2E,eAAiB,WAClB3E,EAAKzH,SAASoM,iBAIlB,KADA,GAAI/V,GAAI,EACDA,EAAIyQ,EAASvQ,QAChBuQ,EAASzQ,GAAGoR,GACZpR,MAQRiT,QAAS,WACL/R,KAAK+D,SAAW0L,GAAezP,MAAM,GAErCA,KAAKuP,YACLvP,KAAKoG,WACLpG,KAAK0F,MAAMqM,UACX/R,KAAK+D,QAAU,OA+BvBlD,GAAOsO,IACHnJ,YAAaA,GACbiG,WAAYA,GACZ/F,UAAWA,GACXC,aAAcA,GAEdqH,eAAgBA,GAChBO,YAAaA,GACbD,cAAeA,GACfD,YAAaA,GACbsH,iBAAkBA,GAClBvH,gBAAiBA,GACjBwH,aAAcA,GAEd5L,eAAgBA,GAChBC,eAAgBA,GAChBC,gBAAiBA,GACjBC,aAAcA,GACdC,eAAgBA,GAChBgI,qBAAsBA,GACtBC,mBAAoBA,GACpBC,cAAeA,GAEfxC,QAASA,GACTlL,MAAOA,EACPoI,YAAaA,EAEbpH,WAAYA,EACZG,WAAYA,EACZL,kBAAmBA,EACnBI,gBAAiBA,EACjB4F,iBAAkBA,EAElBkC,WAAYA,EACZiB,eAAgBA,EAChBkK,IAAKxJ,GACLyJ,IAAKlK,EACLmK,MAAO3J,GACP4J,MAAOjK,EACPkK,OAAQ9J,GACR+J,MAAOlK,EAEP0J,GAAI/W,EACJiX,IAAK1W,EACLjD,KAAMA,EACNuS,MAAOA,GACPF,OAAQA,GACRnQ,OAAQA,GACRX,QAASA,EACT5B,OAAQA,EACR+E,SAAUA,GAKd,IAAIwV,IAAgC,mBAAXhb,GAAyBA,EAA0B,mBAAT0G,MAAuBA,OAC1FsU,IAAW1J,OAASA,GAEE,kBAAX2J,SAAyBA,OAAOC,IACvCD,OAAO,WACH,MAAO3J,MAEa,mBAAV6J,SAAyBA,OAAOC,QAC9CD,OAAOC,QAAU9J,GAEjBtR,EAAOE,GAAcoR,IAGtBtR,OAAQC,SAAU"}