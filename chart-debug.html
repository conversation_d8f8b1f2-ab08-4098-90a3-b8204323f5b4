<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-ok { border-left: 4px solid #10b981; }
        .status-error { border-left: 4px solid #ef4444; }
        .status-warning { border-left: 4px solid #f59e0b; }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        #testChart {
            width: 100%;
            height: 300px;
            margin: 20px 0;
        }
        .log {
            background: #1f2937;
            color: #10b981;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>📊 Chart.js 调试页面</h1>
    
    <div id="statusContainer">
        <div class="status-card status-warning">
            <h3>🔄 正在检测 Chart.js...</h3>
            <p>请等待检测完成</p>
        </div>
    </div>
    
    <div class="status-card">
        <h3>🧪 测试功能</h3>
        <button class="test-button" onclick="testBasicChart()">测试基础图表</button>
        <button class="test-button" onclick="testMultipleCDN()">测试多CDN加载</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="status-card">
        <h3>📈 测试图表</h3>
        <canvas id="testChart"></canvas>
    </div>
    
    <div class="status-card">
        <h3>📝 调试日志</h3>
        <div id="debugLog" class="log">正在初始化...\n</div>
    </div>

    <!-- Chart.js 多CDN加载 -->
    <script>
        let debugLog = '';
        
        function log(message) {
            debugLog += new Date().toLocaleTimeString() + ' - ' + message + '\n';
            document.getElementById('debugLog').textContent = debugLog;
        }
        
        function clearLog() {
            debugLog = '';
            document.getElementById('debugLog').textContent = '';
        }
        
        // Chart.js多CDN备用加载机制
        (function loadChartJS() {
            log('🔄 开始加载 Chart.js...');
            
            const cdnSources = [
                'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js',
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js',
                'https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js'
            ];
            
            let currentIndex = 0;
            
            function tryLoadChart() {
                if (currentIndex >= cdnSources.length) {
                    log('❌ 所有Chart.js CDN源都加载失败');
                    updateStatus('error', '❌ Chart.js 加载失败', '所有CDN源都无法访问，请检查网络连接');
                    return;
                }
                
                log(`🔄 尝试加载: ${cdnSources[currentIndex]}`);
                
                const script = document.createElement('script');
                script.src = cdnSources[currentIndex];
                script.onload = function() {
                    log(`✅ Chart.js加载成功: ${cdnSources[currentIndex]}`);
                    checkChartStatus();
                };
                script.onerror = function() {
                    log(`❌ CDN失败: ${cdnSources[currentIndex]}`);
                    currentIndex++;
                    tryLoadChart();
                };
                document.head.appendChild(script);
            }
            
            tryLoadChart();
        })();
        
        function updateStatus(type, title, message) {
            const container = document.getElementById('statusContainer');
            const statusClass = type === 'ok' ? 'status-ok' : type === 'error' ? 'status-error' : 'status-warning';
            
            container.innerHTML = `
                <div class="status-card ${statusClass}">
                    <h3>${title}</h3>
                    <p>${message}</p>
                </div>
            `;
        }
        
        function checkChartStatus() {
            if (typeof Chart !== 'undefined') {
                log('✅ Chart.js 可用');
                log(`📊 Chart.js 版本: ${Chart.version || '未知'}`);
                updateStatus('ok', '✅ Chart.js 正常', `版本: ${Chart.version || '未知'}`);
            } else {
                log('❌ Chart.js 不可用');
                updateStatus('error', '❌ Chart.js 不可用', '请检查网络连接或CDN访问');
            }
        }
        
        function testBasicChart() {
            if (typeof Chart === 'undefined') {
                log('❌ 无法测试：Chart.js 未加载');
                return;
            }
            
            log('🧪 开始测试基础图表...');
            
            const canvas = document.getElementById('testChart');
            const ctx = canvas.getContext('2d');
            
            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1', '2', '3', '4', '5'],
                        datasets: [{
                            label: '测试数据',
                            data: [12, 19, 3, 5, 2],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                log('✅ 基础图表创建成功');
            } catch (error) {
                log(`❌ 图表创建失败: ${error.message}`);
            }
        }
        
        function testMultipleCDN() {
            log('🔄 测试多CDN访问...');
            
            const cdnSources = [
                'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js',
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js',
                'https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js'
            ];
            
            cdnSources.forEach((url, index) => {
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            log(`✅ CDN ${index + 1} 可访问: ${url}`);
                        } else {
                            log(`❌ CDN ${index + 1} 不可访问: ${url} (${response.status})`);
                        }
                    })
                    .catch(error => {
                        log(`❌ CDN ${index + 1} 连接失败: ${url}`);
                    });
            });
        }
        
        // 页面加载完成后的初始检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof Chart === 'undefined') {
                    log('⚠️ 页面加载完成，但Chart.js仍未可用');
                    updateStatus('error', '❌ Chart.js 加载超时', '请检查网络连接或尝试刷新页面');
                }
            }, 3000);
        });
    </script>
</body>
</html>
