/**
 * 数据管理相关API接口 - 优化版本
 * 支持本地文件处理作为备选方案
 */

/**
 * 数据管理API服务类
 */
class DataAPI {
    constructor(httpClient) {
        this.client = httpClient;
        this.localData = new Map(); // 本地数据存储
        this.dataHistory = []; // 数据历史记录
    }

    /**
     * 上传数据文件（支持本地处理作为备选方案）
     * @param {File|File[]} files - 要上传的文件（单个文件或文件数组）
     * @param {Object} options - 上传选项
     * @returns {Promise<Object>} 上传结果
     */
    async uploadFile(files, options = {}) {
        const fileArray = Array.isArray(files) ? files : [files];

        try {
            // 首先尝试快速API上传（500ms超时）
            const formData = new FormData();

            // 添加文件到FormData
            fileArray.forEach(file => {
                formData.append('files', file);
            });

            // 添加额外的参数
            if (options.description) {
                formData.append('description', options.description);
            }
            if (options.tags) {
                formData.append('tags', JSON.stringify(options.tags));
            }

            // 使用快速超时的上传请求
            const response = await this.client.fastRequest('/data/upload', {
                method: 'POST',
                body: formData,
                headers: {}
            });

            if (response.success && response.data) {
                // 更新用户活动时间
                if (window.AuthAPI) {
                    window.AuthAPI.updateActivity('upload_file', 'data');
                }

                // 处理单文件和多文件响应
                if (Array.isArray(response.data.files)) {
                    return {
                        success: true,
                        message: `${response.data.files.length}个文件上传成功`,
                        data: {
                            uploadId: response.data.uploadId,
                            files: response.data.files.map(file => ({
                                fileId: file.fileId,
                                fileName: file.fileName,
                                fileSize: file.fileSize,
                                uploadTime: file.uploadTime,
                                parseResult: file.parseResult,
                                source: 'api'
                            }))
                        }
                    };
                } else {
                    return {
                        success: true,
                        message: '文件上传成功',
                        data: {
                            fileId: response.data.fileId,
                            fileName: response.data.fileName,
                            fileSize: response.data.fileSize,
                            uploadTime: response.data.uploadTime,
                            parseResult: response.data.parseResult,
                            source: 'api'
                        }
                    };
                }
            }

            throw new APIError('文件上传失败', 400);
        } catch (error) {
            // 检查是否是快速超时错误
            if (error.code === 'FAST_TIMEOUT' || error.code === 'TIMEOUT_ERROR') {
                console.warn('API响应超时(>500ms)，使用本地处理:', error.message);
            } else {
                console.warn('API上传失败，尝试本地处理:', error);
            }

            // API失败或超时时，使用本地处理
            return this.processFilesLocally(fileArray, options);
        }
    }

    /**
     * 本地文件处理（API失败时的备选方案）
     * @param {File[]} files - 文件数组
     * @param {Object} options - 处理选项
     * @returns {Promise<Object>} 处理结果
     */
    async processFilesLocally(files, options = {}) {
        try {
            const results = [];

            for (const file of files) {
                try {
                    const fileId = this.generateFileId();
                    const parseResult = await this.parseFileLocally(file);

                    // 存储到本地
                    this.localData.set(fileId, {
                        fileId: fileId,
                        fileName: file.name,
                        fileSize: file.size,
                        uploadTime: new Date().toISOString(),
                        parseResult: parseResult,
                        source: 'local',
                        description: options.description || '',
                        tags: options.tags || []
                    });

                    // 添加到历史记录
                    this.dataHistory.push({
                        fileId: fileId,
                        fileName: file.name,
                        action: 'local_upload',
                        timestamp: new Date().toISOString()
                    });

                    results.push({
                        fileId: fileId,
                        fileName: file.name,
                        fileSize: file.size,
                        uploadTime: new Date().toISOString(),
                        parseResult: parseResult,
                        source: 'local'
                    });
                } catch (error) {
                    console.error(`本地处理文件失败: ${file.name}`, error);
                    results.push({
                        fileName: file.name,
                        error: error.message,
                        source: 'local'
                    });
                }
            }

            // 更新用户活动时间
            if (window.AuthAPI) {
                window.AuthAPI.updateActivity('local_upload', 'data');
            }

            return {
                success: true,
                message: `本地处理完成，成功处理 ${results.filter(r => !r.error).length}/${files.length} 个文件`,
                data: {
                    uploadId: 'local_' + Date.now(),
                    files: results,
                    source: 'local'
                }
            };
        } catch (error) {
            console.error('本地文件处理失败:', error);
            throw new APIError('本地文件处理失败: ' + error.message, 0);
        }
    }

    /**
     * 批量上传多个文件（分批处理）
     * @param {File[]} files - 文件数组
     * @param {Object} options - 上传选项
     * @returns {Promise<Object>} 上传结果
     */
    async uploadMultipleFiles(files, options = {}) {
        if (!Array.isArray(files) || files.length === 0) {
            throw new APIError('请选择要上传的文件', 400);
        }

        const batchSize = 3; // 最多同时上传3个文件
        const results = [];
        const errors = [];

        // 分批处理文件
        for (let i = 0; i < files.length; i += batchSize) {
            const batch = files.slice(i, i + batchSize);

            try {
                console.log(`处理第 ${Math.floor(i / batchSize) + 1} 批文件 (${batch.length} 个文件)`);
                const batchResult = await this.uploadFile(batch, options);

                if (batchResult.success) {
                    if (Array.isArray(batchResult.data.files)) {
                        results.push(...batchResult.data.files);
                    } else {
                        results.push(batchResult.data);
                    }
                }
            } catch (error) {
                console.error(`第 ${Math.floor(i / batchSize) + 1} 批文件处理失败:`, error);
                errors.push({
                    batch: Math.floor(i / batchSize) + 1,
                    files: batch.map(f => f.name),
                    error: error.message
                });
            }

            // 批次间延迟，避免服务器压力
            if (i + batchSize < files.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        return {
            success: true,
            message: `批量处理完成，成功处理 ${results.length}/${files.length} 个文件`,
            data: {
                uploadId: 'batch_' + Date.now(),
                files: results,
                errors: errors,
                totalFiles: files.length,
                successCount: results.length,
                errorCount: errors.length
            }
        };
    }

    /**
     * 解析数据文件（支持本地数据）
     * @param {string} fileId - 文件ID
     * @param {Object} parseOptions - 解析选项
     * @returns {Promise<Object>} 解析结果
     */
    async parseFile(fileId, parseOptions = {}) {
        // 首先检查是否是本地数据
        if (this.localData.has(fileId)) {
            const localFile = this.localData.get(fileId);
            return {
                success: true,
                message: '本地数据解析成功',
                data: localFile.parseResult
            };
        }

        try {
            const response = await this.client.post('/data/parse', {
                fileId: fileId,
                options: parseOptions
            });

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '数据解析成功',
                    data: response.data
                };
            }

            throw new APIError('数据解析失败', 400);
        } catch (error) {
            console.error('数据解析失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError('数据解析失败', 0);
        }
    }

    /**
     * 获取历史数据列表（包含本地数据）
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 历史数据列表
     */
    async getHistoryData(params = {}) {
        try {
            // 获取本地数据
            const localItems = Array.from(this.localData.values()).map(item => ({
                fileId: item.fileId,
                fileName: item.fileName,
                fileSize: item.fileSize,
                uploadTime: item.uploadTime,
                source: 'local',
                description: item.description,
                tags: item.tags
            }));

            // 尝试获取API数据
            let apiItems = [];
            try {
                const queryParams = {
                    page: params.page || 1,
                    limit: params.limit || 20,
                    sortBy: params.sortBy || 'uploadTime',
                    sortOrder: params.sortOrder || 'desc'
                };

                if (params.startDate) {
                    queryParams.startDate = params.startDate;
                }
                if (params.endDate) {
                    queryParams.endDate = params.endDate;
                }
                if (params.fileType) {
                    queryParams.fileType = params.fileType;
                }

                // 使用快速超时请求
                const response = await this.client.fastRequest('/data/history', {
                    method: 'GET'
                });

                if (response.success && response.data) {
                    apiItems = response.data.items.map(item => ({
                        ...item,
                        source: 'api'
                    }));
                }
            } catch (error) {
                if (error.code === 'FAST_TIMEOUT') {
                    console.warn('API响应超时(>500ms)，仅显示本地数据:', error.message);
                } else {
                    console.warn('获取API历史数据失败，仅显示本地数据:', error);
                }
            }

            // 合并本地和API数据
            const allItems = [...localItems, ...apiItems];

            // 排序
            allItems.sort((a, b) => {
                const dateA = new Date(a.uploadTime);
                const dateB = new Date(b.uploadTime);
                return params.sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
            });

            // 分页
            const page = params.page || 1;
            const limit = params.limit || 20;
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedItems = allItems.slice(startIndex, endIndex);

            return {
                success: true,
                data: {
                    items: paginatedItems,
                    total: allItems.length,
                    page: page,
                    limit: limit,
                    totalPages: Math.ceil(allItems.length / limit),
                    localCount: localItems.length,
                    apiCount: apiItems.length
                }
            };
        } catch (error) {
            console.error('获取历史数据失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError('获取历史数据失败', 0);
        }
    }

    /**
     * 获取特定数据文件的详细信息
     * @param {string} fileId - 文件ID
     * @returns {Promise<Object>} 文件详细信息
     */
    async getFileDetails(fileId) {
        // 首先检查本地数据
        const localFile = this.localData.get(fileId);
        if (localFile) {
            return {
                success: true,
                data: localFile,
                source: 'local'
            };
        }

        try {
            // 使用快速超时请求API
            const response = await this.client.fastRequest(`/data/files/${fileId}`, {
                method: 'GET'
            });

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data,
                    source: 'api'
                };
            }

            throw new APIError('获取文件详情失败', 404);
        } catch (error) {
            if (error.code === 'FAST_TIMEOUT') {
                console.warn('API响应超时(>500ms)，文件详情不可用:', error.message);
                return {
                    success: false,
                    useLocal: true,
                    message: '文件详情获取超时，请稍后重试'
                };
            }

            console.error('获取文件详情失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError('获取文件详情失败', 0);
        }
    }

    /**
     * 删除数据文件
     * @param {string} fileId - 文件ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteFile(fileId) {
        try {
            const response = await this.client.delete(`/data/files/${fileId}`);

            if (response.success) {
                return {
                    success: true,
                    message: '文件删除成功'
                };
            }

            throw new APIError('文件删除失败', 400);
        } catch (error) {
            console.error('文件删除失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('文件删除失败', 0);
        }
    }

    /**
     * 更新文件信息
     * @param {string} fileId - 文件ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateFile(fileId, updateData) {
        try {
            const response = await this.client.put(`/data/files/${fileId}`, updateData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '文件信息更新成功',
                    data: response.data
                };
            }

            throw new APIError('文件信息更新失败', 400);
        } catch (error) {
            console.error('文件信息更新失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('文件信息更新失败', 0);
        }
    }

    /**
     * 获取数据统计信息
     * @param {Object} params - 统计参数
     * @returns {Promise<Object>} 统计信息
     */
    async getDataStatistics(params = {}) {
        try {
            // 使用快速超时请求
            const response = await this.client.fastRequest('/data/statistics', {
                method: 'GET'
            });

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data,
                    source: 'api'
                };
            }

            throw new APIError('获取数据统计失败', 400);
        } catch (error) {
            if (error.code === 'FAST_TIMEOUT') {
                console.warn('API响应超时(>500ms)，使用本地统计:', error.message);
                // 返回本地统计数据
                const localStats = this.getLocalStatistics();
                return {
                    success: true,
                    data: localStats,
                    source: 'local',
                    message: '使用本地统计数据'
                };
            }
            console.error('获取数据统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取数据统计失败', 0);
        }
    }

    /**
     * 批量操作数据文件
     * @param {string} action - 操作类型 (delete, export, etc.)
     * @param {Array} fileIds - 文件ID列表
     * @param {Object} options - 操作选项
     * @returns {Promise<Object>} 操作结果
     */
    async batchOperation(action, fileIds, options = {}) {
        try {
            const response = await this.client.post('/data/batch', {
                action: action,
                fileIds: fileIds,
                options: options
            });

            if (response.success) {
                return {
                    success: true,
                    message: `批量${action}操作成功`,
                    data: response.data
                };
            }

            throw new APIError(`批量${action}操作失败`, 400);
        } catch (error) {
            console.error(`批量${action}操作失败:`, error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError(`批量${action}操作失败`, 0);
        }
    }

    /**
     * 验证文件格式
     * @param {File} file - 要验证的文件
     * @returns {Object} 验证结果
     */
    validateFileFormat(file) {
        const supportedTypes = [
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/json',
            'text/plain'
        ];

        const supportedExtensions = ['.csv', '.xls', '.xlsx', '.json', '.txt'];
        
        const fileName = file.name.toLowerCase();
        const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
        
        const isValidType = supportedTypes.includes(file.type);
        const isValidExtension = supportedExtensions.includes(fileExtension);
        
        return {
            isValid: isValidType || isValidExtension,
            type: file.type,
            extension: fileExtension,
            size: file.size,
            name: file.name
        };
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 本地文件解析（备选方案）
     * @param {File} file - 文件对象
     * @returns {Promise<Object>} 解析结果
     */
    async parseFileLocally(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const content = e.target.result;
                    const ext = this.getFileExtension(file.name);

                    let parsedData = [];

                    if (ext === 'csv' || ext === 'txt') {
                        parsedData = this.parseCSVContent(content);
                    } else if (ext === 'json') {
                        parsedData = JSON.parse(content);
                    } else {
                        throw new Error('不支持的文件格式');
                    }

                    resolve({
                        parsedData: parsedData,
                        metadata: {
                            totalPoints: parsedData.length,
                            fileName: file.name,
                            fileSize: file.size,
                            columns: parsedData.length > 0 ? Object.keys(parsedData[0]) : []
                        }
                    });
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    /**
     * CSV内容解析
     * @param {string} content - CSV内容
     * @returns {Array} 解析后的数据
     */
    parseCSVContent(content) {
        const lines = content.trim().split('\n');
        if (lines.length === 0) return [];

        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);

            if (values.length === headers.length) {
                const row = {};
                headers.forEach((header, index) => {
                    const value = values[index];
                    // 尝试转换为数字
                    row[header] = isNaN(value) || value === '' ? value : parseFloat(value);
                });
                data.push(row);
            }
        }

        return data;
    }

    /**
     * 解析CSV行（处理引号和逗号）
     * @param {string} line - CSV行
     * @returns {Array} 解析后的值数组
     */
    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }

        result.push(current.trim());
        return result;
    }

    /**
     * 生成文件ID
     * @returns {string} 文件ID
     */
    generateFileId() {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 检测文件格式
     * @param {string} fileName - 文件名
     * @returns {string} 文件格式
     */
    detectFileFormat(fileName) {
        const ext = this.getFileExtension(fileName);
        const formatMap = {
            'csv': 'csv',
            'xlsx': 'excel',
            'xls': 'excel',
            'json': 'json',
            'txt': 'txt'
        };
        return formatMap[ext] || 'csv';
    }

    /**
     * 获取本地数据
     * @param {string} fileId - 文件ID
     * @returns {Object|null} 本地数据
     */
    getLocalData(fileId) {
        return this.localData.get(fileId) || null;
    }

    /**
     * 删除本地数据
     * @param {string} fileId - 文件ID
     * @returns {boolean} 删除结果
     */
    deleteLocalData(fileId) {
        return this.localData.delete(fileId);
    }

    /**
     * 清空所有本地数据
     */
    clearLocalData() {
        this.localData.clear();
        this.dataHistory = [];
    }

    /**
     * 获取本地统计信息（用于API超时时的降级）
     * @returns {Object} 统计信息
     */
    getLocalStatistics() {
        const totalFiles = this.localData.size;
        let totalSize = 0;
        const fileTypes = {};
        const uploadsByDate = {};

        for (const data of this.localData.values()) {
            totalSize += data.fileSize;

            // 统计文件类型
            const ext = data.fileName.split('.').pop()?.toLowerCase() || 'unknown';
            fileTypes[ext] = (fileTypes[ext] || 0) + 1;

            // 按日期统计上传
            const date = new Date(data.uploadTime).toDateString();
            uploadsByDate[date] = (uploadsByDate[date] || 0) + 1;
        }

        return {
            totalFiles,
            totalSize,
            fileTypes,
            uploadsByDate,
            lastUpdate: new Date().toISOString(),
            source: 'local'
        };
    }

    /**
     * 获取本地数据统计（保持向后兼容）
     * @returns {Object} 统计信息
     */
    getLocalDataStats() {
        const totalFiles = this.localData.size;
        let totalSize = 0;

        for (const data of this.localData.values()) {
            totalSize += data.fileSize;
        }

        return {
            totalFiles: totalFiles,
            totalSize: totalSize,
            formattedSize: this.formatFileSize(totalSize)
        };
    }
}

// 创建数据API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.DataAPI = new DataAPI(window.APIService.client);
}
