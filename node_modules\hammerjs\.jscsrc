{"excludeFiles": ["*.js", "tests/**/assets", "node_modules/**"], "requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireOperatorBeforeLineBreak": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "maximumLineLength": {"value": 120, "allowComments": true, "allowRegex": true}, "validateIndentation": 4, "validateQuoteMarks": "'", "disallowMultipleLineStrings": true, "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "disallowSpaceAfterPrefixUnaryOperators": true, "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpaceAfterBinaryOperators": true, "requireSpacesInConditionalExpression": true, "requireSpaceBeforeBlockStatements": true, "requireLineFeedAtFileEnd": true, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInsideObjectBrackets": "all", "disallowSpacesInsideArrayBrackets": "all", "disallowSpacesInsideParentheses": true, "validateJSDoc": {"checkParamNames": true, "requireParamTypes": true}, "disallowMultipleLineBreaks": true, "disallowNewlineBeforeBlockStatements": true}