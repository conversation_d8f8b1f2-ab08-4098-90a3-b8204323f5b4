<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析平台 - 信号去噪</title>
    <!-- 图标库 -->
    <link rel="stylesheet" href="libs/css/font-awesome.min.css">
    
    <!-- Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Toast通知系统 -->
    <link rel="stylesheet" href="css/toast-system.css">
    
    <!-- Chart.js库 -->
    <script src="libs/js/chart.umd.js"></script>
    <script src="libs/js/chartjs-plugin-zoom.min.js"></script>

    <!-- SheetJS库用于处理Excel文件 -->
    <script src="libs/js/xlsx.full.min.js"></script>
    <script src="libs/js/chartjs-plugin-zoom.min.js"></script>

    <!-- SheetJS库用于处理Excel文件 -->
    <script src="libs/js/xlsx.full.min.js"></script>

    <!-- API服务模块 -->
    <script src="js/api-service.js"></script>
    <script src="js/auth-api.js"></script>
    <script src="js/auth-guard.js"></script>
    <script src="js/signal-api.js"></script>
    <script src="js/export-api.js"></script>
    <script src="js/stats-api.js"></script>
    <script src="js/websocket-client.js"></script>
    <script src="js/global-error-handler.js"></script>
    <script src="js/loading-manager.js"></script>
    <script src="js/toast-system.js"></script>

    <!-- 数学计算库 - 移除可能导致加载问题的库 -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/ml-matrix@6.10.4/lib/index.min.js"></script> -->
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            overflow-x: auto;
            overflow-y: auto;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(147, 197, 253, 0.3) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.25) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(167, 243, 208, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 60% 80%, rgba(254, 202, 202, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 90% 60%, rgba(253, 230, 138, 0.15) 0%, transparent 60%),
                linear-gradient(135deg, #f8fafc 0%, #f1f5f9 20%, #e2e8f0 40%, #f0f9ff 60%, #fef3c7 80%, #fce7f3 100%);
            background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
            color: #1e293b;
            overflow-x: auto;
            overflow-y: auto;
            position: relative;
            animation: backgroundShift 30s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background-position: 0% 0%, 100% 100%, 50% 50%, 25% 75%, 90% 60%, 0% 0%;
            }
            16% {
                background-position: 10% 20%, 90% 80%, 30% 70%, 45% 55%, 80% 40%, 16% 16%;
            }
            33% {
                background-position: 30% 40%, 70% 60%, 60% 30%, 65% 35%, 70% 80%, 33% 33%;
            }
            50% {
                background-position: 60% 80%, 40% 20%, 80% 60%, 25% 75%, 60% 20%, 50% 50%;
            }
            66% {
                background-position: 80% 60%, 20% 40%, 40% 80%, 75% 25%, 50% 60%, 66% 66%;
            }
            83% {
                background-position: 40% 20%, 60% 80%, 20% 40%, 55% 65%, 40% 80%, 83% 83%;
            }
        }

        /* 增强动态背景元素 */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .animated-shape {
            position: absolute;
            border-radius: 50%;
            animation: float 25s ease-in-out infinite;
        }

        .shape1 {
            width: 400px;
            height: 400px;
            top: 5%;
            left: 5%;
            background: radial-gradient(circle, rgba(147, 197, 253, 0.4) 0%, rgba(191, 219, 254, 0.15) 100%);
            animation-delay: 0s;
        }

        .shape2 {
            width: 300px;
            height: 300px;
            top: 50%;
            right: 5%;
            background: radial-gradient(circle, rgba(196, 181, 253, 0.35) 0%, rgba(221, 214, 254, 0.15) 100%);
            animation-delay: 8s;
        }

        .shape3 {
            width: 250px;
            height: 250px;
            bottom: 10%;
            left: 15%;
            background: radial-gradient(circle, rgba(167, 243, 208, 0.4) 0%, rgba(209, 250, 229, 0.15) 100%);
            animation-delay: 16s;
        }

        .shape4 {
            width: 350px;
            height: 350px;
            top: 25%;
            right: 25%;
            background: radial-gradient(circle, rgba(254, 202, 202, 0.35) 0%, rgba(254, 226, 226, 0.15) 100%);
            animation-delay: 12s;
        }

        .shape5 {
            width: 200px;
            height: 200px;
            top: 70%;
            left: 60%;
            background: radial-gradient(circle, rgba(253, 230, 138, 0.4) 0%, rgba(254, 240, 138, 0.15) 100%);
            animation-delay: 4s;
        }

        .shape6 {
            width: 180px;
            height: 180px;
            top: 15%;
            left: 70%;
            background: radial-gradient(circle, rgba(252, 231, 243, 0.35) 0%, rgba(253, 242, 248, 0.15) 100%);
            animation-delay: 20s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.4;
            }
            25% {
                transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.1);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-60px) translateX(-10px) rotate(180deg) scale(0.9);
                opacity: 0.6;
            }
            75% {
                transform: translateY(-20px) translateX(-30px) rotate(270deg) scale(1.05);
                opacity: 0.3;
            }
        }

        /* 粒子效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particleFloat 15s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* 光效 */
        .light-effects {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .light-beam {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 100%);
            animation: lightSweep 12s ease-in-out infinite;
        }

        .light-beam:nth-child(1) { left: 10%; animation-delay: 0s; }
        .light-beam:nth-child(2) { left: 30%; animation-delay: 4s; }
        .light-beam:nth-child(3) { left: 60%; animation-delay: 8s; }
        .light-beam:nth-child(4) { left: 85%; animation-delay: 2s; }

        @keyframes lightSweep {
            0%, 100% {
                opacity: 0;
                transform: scaleY(0);
            }
            50% {
                opacity: 1;
                transform: scaleY(1);
            }
        }

        /* 页面加载动画 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #7dd3fc 75%, #38bdf8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.6s ease, visibility 0.6s ease;
        }

        .page-loader.loaded {
            opacity: 0;
            visibility: hidden;
        }

        .loader-content {
            text-align: center;
            color: #0369a1;
        }

        .spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 4px solid transparent;
            border-top-color: #0369a1;
            animation: spin 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
        }

        .spinner-ring:nth-child(1) {
            animation-delay: 0s;
        }

        .spinner-ring:nth-child(2) {
            width: 80%;
            height: 80%;
            border-top-color: #0284c7;
            animation-delay: 0.15s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 60%;
            height: 60%;
            border-top-color: #0ea5e9;
            animation-delay: 0.3s;
        }

        .spinner-dot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #0369a1;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(3, 105, 161, 0.5);
            animation: spinnerDot 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes spinnerDot {
            0%, 100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .loading-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(3, 105, 161, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #0369a1, #0ea5e9);
            border-radius: 2px;
            animation: loadingProgress 2s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% {
                width: 0%;
                transform: translateX(-100%);
            }
            50% {
                width: 100%;
                transform: translateX(0%);
            }
            100% {
                width: 100%;
                transform: translateX(100%);
            }
        }

        /* 主容器布局 */
        .main-container {
            display: grid;
            grid-template-columns: 260px 1fr;
            min-height: 100vh;
            height: auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            min-width: calc(100vw + 500px); /* 与内容布局宽度匹配，确保完整滚动 */
        }

        /* 固定水平滚动条 */
        .horizontal-scrollbar {
            position: fixed !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 25px !important;
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-top: 2px solid rgba(59, 130, 246, 0.3) !important;
            z-index: 9999 !important;
            overflow-x: auto !important;
            overflow-y: hidden !important;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
            display: block !important;
            visibility: visible !important;
            width: 100% !important;
        }

        .horizontal-scrollbar-content {
            height: 20px;
            width: calc(100vw + 500px); /* 与内容布局宽度匹配 */
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
            min-width: calc(100vw + 500px);
        }

        /* 确保滚动条可见 */
        .horizontal-scrollbar::-webkit-scrollbar {
            height: 12px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.6);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.8);
        }

        /* 左侧导航栏 */
        .sidebar {
            background: rgba(255, 255, 255, 0.92);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                4px 0 25px rgba(0, 0, 0, 0.06),
                2px 0 15px rgba(59, 130, 246, 0.08);
            overflow-y: auto;
            position: relative;
            padding: 15px 12px;
        }

        .sidebar-header {
            padding: 20px 15px;
            text-align: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-radius: 16px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 导航菜单 */
        .nav-section {
            padding: 25px 20px;
        }

        .nav-category {
            margin-bottom: 25px;
        }

        .category-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .category-header:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .category-icon {
            font-size: 18px;
            color: #3b82f6;
            margin-right: 12px;
            width: 20px;
        }

        .category-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .category-arrow {
            font-size: 12px;
            color: #3b82f6;
            transition: transform 0.3s ease;
        }

        .category-header.expanded .category-arrow {
            transform: rotate(180deg);
        }

        .nav-submenu {
            list-style: none;
            margin-top: 10px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .nav-category.expanded .nav-submenu {
            max-height: 300px;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 45px;
            color: #64748b;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), transparent);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link:hover {
            color: #3b82f6;
            transform: translateX(8px);
        }

        .nav-icon {
            font-size: 16px;
            margin-right: 10px;
            width: 18px;
            color: #94a3b8;
            transition: color 0.3s ease;
        }

        .nav-link:hover .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .nav-link.active .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active::before {
            width: 100%;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* 意见反馈样式 */
        .feedback-section {
            position: absolute;
            bottom: 20px;
            left: 12px;
            right: 12px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 20px;
            color: #3b82f6;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link:hover {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .feedback-icon {
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .feedback-link:hover .feedback-icon {
            transform: scale(1.1);
        }

        .feedback-text {
            font-size: 14px;
            font-weight: 600;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 15px 20px 30px 20px;
            background: rgba(255, 255, 255, 0.02);
            min-height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            overflow-x: auto;
            overflow-y: auto;
            /* 确保水平滚动条始终可见 */
            scrollbar-width: thin;
            scrollbar-color: rgba(59, 130, 246, 0.6) rgba(255, 255, 255, 0.1);
        }

        /* 主内容区域滚动条样式 */
        .main-content::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        .main-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            margin: 5px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(14, 165, 233, 0.6));
            border-radius: 6px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(14, 165, 233, 0.8));
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .main-content::-webkit-scrollbar-corner {
            background: rgba(255, 255, 255, 0.1);
        }

        /* 内容布局 */
        .content-layout {
            display: grid;
            grid-template-columns: 400px 1fr; /* 稍微增大控制面板宽度 */
            gap: 30px; /* 保持间距 */
            min-width: calc(100vw + 500px); /* 进一步增加最小宽度，确保完整内容可见 */
            max-width: none; /* 移除最大宽度限制 */
            width: calc(100vw + 500px); /* 设置宽度，超出视口 */
            align-items: stretch;
            padding: 15px 80px 15px 30px; /* 减少顶部内边距，增加可用高度 */
            flex: 1;
            min-height: calc(100vh - 60px); /* 增加最小高度 */
            height: calc(100vh - 60px); /* 增加高度，减少顶部间距 */
            box-sizing: border-box;
        }

        /* 左侧控制面板 */
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow-y: auto;
            overflow-x: hidden;
            padding-bottom: 30px; /* 增加底部内边距 */
            padding-right: 15px; /* 增加右侧内边距为滚动条留空间 */
            flex-shrink: 0;
            min-height: calc(100vh - 100px); /* 增加最小高度 */
            height: 100%; /* 确保与右侧可视化区域高度一致 */
            max-height: calc(100vh - 80px); /* 增加最大高度 */
            justify-content: flex-start;
            /* 自定义滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: rgba(59, 130, 246, 0.6) rgba(255, 255, 255, 0.1);
        }

        /* WebKit浏览器滚动条样式 */
        .control-panel::-webkit-scrollbar {
            width: 8px;
        }

        .control-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin: 5px 0;
        }

        .control-panel::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(14, 165, 233, 0.6));
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-panel::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(14, 165, 233, 0.8));
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .control-panel::-webkit-scrollbar-thumb:active {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(2, 132, 199, 0.9));
        }

        /* 确保导出卡片在底部 */
        .control-panel .export-card {
            margin-top: auto;
            flex-shrink: 0;
        }

        /* 滚动指示器 */
        .control-panel::before {
            content: '';
            position: sticky;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                rgba(59, 130, 246, 0.3) 0%,
                rgba(14, 165, 233, 0.3) 50%,
                rgba(59, 130, 246, 0.3) 100%);
            z-index: 10;
            border-radius: 0 0 3px 3px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .control-panel:hover::before {
            opacity: 1;
        }

        .control-panel::after {
            content: '';
            position: sticky;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                rgba(59, 130, 246, 0.3) 0%,
                rgba(14, 165, 233, 0.3) 50%,
                rgba(59, 130, 246, 0.3) 100%);
            z-index: 10;
            border-radius: 3px 3px 0 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .control-panel:hover::after {
            opacity: 1;
        }

        .control-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 25px; /* 增加内边距 */
            box-shadow:
                0 10px 40px rgba(0, 0, 0, 0.06),
                0 4px 20px rgba(59, 130, 246, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: visible;
            flex-shrink: 0;
            width: 100%;
            box-sizing: border-box;
            min-width: 340px; /* 设置最小宽度 */
        }

        .control-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
        }

        .control-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 25px 70px rgba(0, 0, 0, 0.12),
                0 8px 30px rgba(59, 130, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .control-card:active {
            transform: translateY(-4px) scale(0.98);
            transition: all 0.1s ease;
        }

        .control-card h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-card h3 i {
            color: #3b82f6;
            font-size: 20px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            width: 100%;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(59, 130, 246, 0.5);
            background: linear-gradient(135deg, #2563eb, #0284c7);
        }

        .btn-primary:active {
            transform: translateY(-1px) scale(0.98);
            transition: all 0.1s ease;
        }

        .btn-outline {
            background: transparent;
            color: #3b82f6;
            border: 2px solid #3b82f6;
            position: relative;
            overflow: hidden;
        }

        .btn-outline::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-outline:hover::before {
            left: 100%;
        }

        .btn-outline:hover {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-outline:active {
            transform: translateY(0px) scale(0.98);
            transition: all 0.1s ease;
        }

        /* 导入选项样式 */
        .import-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .import-option {
            padding: 20px;
            border: 2px dashed rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
        }

        .import-option:hover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .import-option i {
            font-size: 32px;
            color: #3b82f6;
            margin-bottom: 12px;
            display: block;
        }

        .import-option h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 8px 0;
        }

        .import-option p {
            font-size: 14px;
            color: #64748b;
            margin: 0;
        }

        /* 信号生成器样式 */
        .signal-type-selector {
            margin-bottom: 15px;
        }

        .signal-type-selector label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 8px;
        }

        .signal-type-selector select {
            width: 100%;
            padding: 10px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            background: white;
            font-size: 14px;
            color: #1e293b;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .signal-type-selector select:focus {
            border-color: #3b82f6;
        }

        .signal-params {
            margin-bottom: 20px;
        }

        .param-group {
            margin-bottom: 15px;
        }

        .param-group label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 8px;
        }

        .param-group input[type="range"] {
            width: 100%;
            height: 4px;
            background: rgba(59, 130, 246, 0.2);
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .param-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .param-group input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.5);
        }

        .param-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #3b82f6;
            margin-right: 8px;
        }

        .noise-config {
            margin-top: 15px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(14, 165, 233, 0.08) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        /* 示例数据卡片样式 */
        .sample-data-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .sample-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .sample-item {
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .sample-item:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .sample-item.active {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .sample-item i {
            font-size: 18px;
            color: #3b82f6;
        }

        .sample-item.active i {
            color: white;
        }

        .sample-item span {
            font-size: 12px;
            font-weight: 500;
        }

        .sample-controls {
            margin-bottom: 20px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 8px;
        }

        /* 控制组滑动条样式 - 与图片样式一致 */
        .control-group input[type="range"] {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
            margin: 8px 0;
            position: relative;
        }

        .control-group input[type="range"]::-webkit-slider-track {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
        }

        .control-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #3b82f6;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .control-group input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.5);
        }

        /* Firefox滑动条样式 */
        .control-group input[type="range"]::-moz-range-track {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            border: none;
        }

        .control-group input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #3b82f6;
            border-radius: 50%;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .freq-scale {
            margin-top: 5px;
            text-align: center;
        }

        .freq-scale small {
            color: #94a3b8;
            font-size: 11px;
        }

        /* 噪声开关样式 */
        .noise-toggle-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(14, 165, 233, 0.08) 100%);
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .noise-toggle-text-section {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            color: #1e293b;
        }

        .noise-toggle-text-section i {
            color: #3b82f6;
            font-size: 16px;
        }

        .noise-toggle-button-section {
            display: flex;
            align-items: center;
        }

        .noise-toggle-checkbox {
            display: none;
        }

        .noise-toggle-label {
            cursor: pointer;
            display: block;
        }

        .noise-toggle-switch {
            width: 50px;
            height: 24px;
            background: #cbd5e1;
            border-radius: 12px;
            position: relative;
            transition: all 0.3s ease;
        }

        .noise-toggle-slider {
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .noise-toggle-checkbox:checked + .noise-toggle-label .noise-toggle-switch {
            background: #3b82f6;
        }

        .noise-toggle-checkbox:checked + .noise-toggle-label .noise-toggle-slider {
            transform: translateX(26px);
        }

        /* 噪声配置面板样式 */
        .noise-config-panel {
            margin-top: 15px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(14, 165, 233, 0.08) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
        }

        .noise-types-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 15px;
        }

        .noise-type-item {
            position: relative;
        }

        .noise-type-item input[type="checkbox"] {
            display: none;
        }

        .noise-type-item label {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 12px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
            margin: 0;
        }

        .noise-type-item label:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .noise-type-item input[type="checkbox"]:checked + label {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-color: #3b82f6;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .noise-type-item label i {
            font-size: 14px;
            width: 16px;
            text-align: center;
        }

        .noise-type-item input[type="checkbox"]:checked + label i {
            color: white;
        }

        .noise-amplitude-control {
            margin-top: 15px;
        }

        .amplitude-scale {
            margin-top: 5px;
            text-align: center;
        }

        .amplitude-scale small {
            color: #94a3b8;
            font-size: 11px;
        }

        /* 算法选择器样式 */
        .algorithm-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr; /* 改为3列布局 */
            gap: 12px; /* 增加间距 */
        }

        /* 在较窄的控制面板中回退到2列 */
        @media (max-width: 1600px) {
            .algorithm-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        .algorithm-item {
            position: relative;
        }

        .algorithm-item input[type="checkbox"] {
            display: none;
        }

        .algorithm-item label {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
            margin: 0;
            white-space: nowrap; /* 防止文字换行 */
            min-width: fit-content; /* 确保有足够宽度 */
        }

        .algorithm-item label:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .algorithm-item input[type="checkbox"]:checked + label {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-color: #3b82f6;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .algorithm-item label i {
            font-size: 14px;
            width: 16px;
            text-align: center;
        }

        .algorithm-item input[type="checkbox"]:checked + label i {
            color: white;
        }

        /* 参数面板样式 */
        .parameter-panels {
            max-height: 350px; /* 增加最大高度 */
            overflow-y: auto;
            padding-right: 5px; /* 为滚动条留空间 */
        }

        /* 参数面板滚动条样式 */
        .parameter-panels::-webkit-scrollbar {
            width: 6px;
        }

        .parameter-panels::-webkit-scrollbar-track {
            background: rgba(59, 130, 246, 0.1);
            border-radius: 3px;
        }

        .parameter-panels::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.4);
            border-radius: 3px;
        }

        .parameter-panels::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.6);
        }

        .param-panel {
            margin-bottom: 15px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .param-panel h4 {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 可视化区域样式 */
        .visualization-area {
            display: flex;
            flex-direction: column;
            gap: 25px;
            min-height: calc(100vh - 100px); /* 增加最小高度，与控制面板同步 */
            overflow-y: auto;
            overflow-x: visible; /* 允许水平溢出 */
            height: 100%;
            max-height: calc(100vh - 80px); /* 增加最大高度，与控制面板同步 */
            padding: 0 50px 30px 0; /* 增加底部内边距，右侧内边距50px */
            margin-right: 0; /* 移除右侧外边距 */
            max-width: none; /* 移除最大宽度限制，充分利用空间 */
            width: calc(100% - 50px); /* 减去右侧内边距，确保内容不会超出 */
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            /* 平滑滚动 */
            scroll-behavior: smooth;
        }

        /* 隐藏WebKit浏览器的滚动条 */
        .visualization-area::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
        }

        .visualization-area::-webkit-scrollbar-track {
            display: none;
        }

        .visualization-area::-webkit-scrollbar-thumb {
            display: none;
        }

        /* 可视化区域滚动条样式 */
        .visualization-area::-webkit-scrollbar {
            width: 8px;
        }

        .visualization-area::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin: 5px 0;
        }

        .visualization-area::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(14, 165, 233, 0.6));
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .visualization-area::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(14, 165, 233, 0.8));
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(30px);
            border-radius: 24px;
            padding: 35px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.08),
                0 8px 30px rgba(59, 130, 246, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: visible; /* 改为visible，让内容正常显示 */
            max-width: calc(100vw - 500px); /* 限制最大宽度，确保不会过宽 */
            width: 100%;
            margin-right: 30px; /* 添加右侧外边距，确保与页面右端有间距 */
            /* 优化图表比例 */
            min-height: 500px; /* 进一步增加最小高度 */
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

        /* 专门为评估容器设置样式 */
        .chart-container:has(.metrics-grid) {
            min-height: auto; /* 评估容器自适应高度 */
            height: auto;
        }

        /* 最后一个图表容器增加额外右侧间距 */
        .chart-container:last-child {
            margin-right: 60px; /* 最后一个容器增加更多右侧间距 */
        }

        /* 确保可视化区域内容完整可见 */
        .visualization-area::after {
            content: '';
            display: block;
            width: 1px;
            height: 1px;
            margin-right: 50px; /* 额外的右侧空间 */
        }

        /* 图表标题和操作按钮样式 */
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(59, 130, 246, 0.1);
        }

        .chart-container h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-container h3 i {
            color: #3b82f6;
            font-size: 20px;
        }

        .chart-actions {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .chart-btn {
            width: 28px;
            height: 28px;
            border: none;
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 11px;
        }

        .chart-btn:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: scale(1.1);
        }

        /* 图表内容区域 */
        .chart-content {
            flex: 1;
            position: relative;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 确保图表容器内的元素正常显示 */
        .chart-container canvas {
            max-width: 100%;
            max-height: 100%;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
        }

        .chart-container h3 i {
            color: #3b82f6;
            font-size: 20px;
        }

        .chart-wrapper {
            position: relative;
            height: 450px; /* 进一步增加高度 */
            width: 100%;
            flex: 1;
            display: block; /* 改为block，让canvas正常显示 */
            min-height: 400px; /* 增加最小高度 */
            overflow: visible;
        }

        /* 确保canvas能正确显示 */
        .chart-wrapper canvas {
            width: 100% !important;
            height: 100% !important;
            max-width: 100%;
            max-height: 100%;
        }



        .chart-wrapper canvas {
            border-radius: 12px;
        }

        /* 评估指标样式 */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
            padding: 20px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 16px;
            border: 1px solid rgba(59, 130, 246, 0.1);
            min-height: 100px;
            width: 100%;
            box-sizing: border-box;
        }

        /* 当metrics-grid为空时隐藏 */
        .metrics-grid:empty {
            display: none;
        }

        .metric-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            border: 2px solid rgba(59, 130, 246, 0.15);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
            backdrop-filter: blur(10px);
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .metric-card h4 {
            font-size: 14px;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 8px;
        }

        .metric-card .value {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            font-family: 'Courier New', monospace;
        }

        .metric-card .unit {
            font-size: 12px;
            color: #94a3b8;
            margin-left: 4px;
        }

        /* 结果导出样式 */
        .export-card {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%) !important;
            border: 1px solid rgba(34, 197, 94, 0.3) !important;
            margin-top: auto; /* 推到底部 */
            position: relative;
            overflow: visible;
        }

        .export-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.4), transparent);
        }

        .export-card h3 {
            color: #1e293b !important;
        }

        .export-card h3 i {
            color: #22c55e !important;
        }

        .export-card:hover {
            transform: translateY(-8px) scale(1.01);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 6px 25px rgba(34, 197, 94, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(34, 197, 94, 0.5);
        }

        .export-section {
            margin-top: 20px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 12px;
        }

        .export-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .export-section h4 i {
            color: #22c55e;
        }

        .export-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr; /* 保持2列，但按钮会更宽 */
            gap: 12px; /* 增加间距 */
        }

        /* 在更宽的控制面板中可以使用4列布局 */
        @media (min-width: 1600px) {
            .export-buttons {
                grid-template-columns: 1fr 1fr 1fr 1fr;
                gap: 10px;
            }
        }

        .btn-export {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            border: none;
            padding: 12px 15px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            width: 100%;
            box-sizing: border-box;
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
            background: linear-gradient(135deg, #16a34a, #15803d);
        }

        .btn-export:active {
            transform: translateY(0px);
            transition: all 0.1s ease;
        }

        /* 响应式设计 */
        @media (max-width: 1800px) {
            .content-layout {
                grid-template-columns: 360px 1fr;
                min-width: 1800px;
            }
        }

        @media (max-width: 1400px) {
            .content-layout {
                grid-template-columns: 320px 1fr;
                min-width: 1400px;
            }
        }

        @media (max-width: 1200px) {
            .content-layout {
                grid-template-columns: 300px 1fr;
                min-width: 1200px;
            }
        }

        @media (max-width: 1000px) {
            .content-layout {
                grid-template-columns: 1fr;
                min-width: 800px;
            }

            .control-panel {
                max-height: none;
                overflow-y: visible;
            }
        }
    </style>
</head>
<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-dot"></div>
            </div>
            <div class="loading-text">正在加载信号去噪平台...</div>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- 增强动态背景元素 -->
    <div class="animated-bg">
        <div class="animated-shape shape1"></div>
        <div class="animated-shape shape2"></div>
        <div class="animated-shape shape3"></div>
        <div class="animated-shape shape4"></div>
        <div class="animated-shape shape5"></div>
        <div class="animated-shape shape6"></div>
    </div>

    <!-- 粒子效果 -->
    <div class="particles" id="particles"></div>

    <!-- 光效 -->
    <div class="light-effects">
        <div class="light-beam"></div>
        <div class="light-beam"></div>
        <div class="light-beam"></div>
        <div class="light-beam"></div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1 class="sidebar-title">数据分析平台</h1>
            </div>

            <nav class="nav-section">
                <!-- 信号可视化分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-visualization">
                        <i class="fas fa-chart-line category-icon"></i>
                        <span class="category-title">信号可视化</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="data.html" class="nav-link" data-page="data">
                                <i class="fas fa-upload nav-icon"></i>
                                <span class="nav-text">数据上传</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="serial-debug.html" class="nav-link" data-page="serial-debug">
                                <i class="fas fa-terminal nav-icon"></i>
                                <span class="nav-text">串口调试</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号处理分类 -->
                <div class="nav-category expanded">
                    <div class="category-header expanded" data-category="signal-processing">
                        <i class="fas fa-wave-square category-icon"></i>
                        <span class="category-title">信号处理</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="signal-denoise.html" class="nav-link active" data-page="signal-denoise">
                                <i class="fas fa-magic nav-icon"></i>
                                <span class="nav-text">信号去噪</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号分析分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-analysis">
                        <i class="fas fa-chart-area category-icon"></i>
                        <span class="category-title">信号分析</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="fft-analysis.html" class="nav-link" data-page="fft-analysis">
                                <i class="fas fa-chart-bar nav-icon"></i>
                                <span class="nav-text">频谱分析</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 意见反馈 - 底部固定位置 -->
            <div class="feedback-section">
                <a href="feedback.html" class="feedback-link">
                    <i class="fas fa-comment-dots feedback-icon"></i>
                    <span class="feedback-text">意见反馈</span>
                </a>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="content-layout">
                <!-- 左侧控制面板 -->
                <div class="control-panel">
                    <!-- 数据导入卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-upload"></i>
                            数据导入
                        </h3>
                        <div class="import-options">
                            <div class="import-option" id="fileImport">
                                <i class="fas fa-file-upload"></i>
                                <h4>Excel/CSV文件</h4>
                                <p>支持.xlsx, .xls, .csv格式</p>
                                <input type="file" id="fileInput" accept=".csv,.txt,.json,.xlsx,.xls" style="display: none;">
                            </div>
                            <div class="import-option" id="clipboardImport">
                                <i class="fas fa-clipboard"></i>
                                <h4>粘贴板数据</h4>
                                <p>从剪贴板导入数据</p>
                            </div>
                        </div>
                    </div>

                    <!-- 信号生成卡片 -->
                    <div class="control-card sample-data-card">
                        <h3>
                            <i class="fas fa-database"></i>
                            信号生成
                        </h3>
                        <div class="sample-grid">
                            <div class="sample-item active" data-type="sine">
                                <i class="fas fa-wave-square"></i>
                                <span>正弦波</span>
                            </div>
                            <div class="sample-item" data-type="cosine">
                                <i class="fas fa-chart-line"></i>
                                <span>余弦波</span>
                            </div>
                            <div class="sample-item" data-type="square">
                                <i class="fas fa-square"></i>
                                <span>方波</span>
                            </div>
                            <div class="sample-item" data-type="triangle">
                                <i class="fas fa-play"></i>
                                <span>三角波</span>
                            </div>
                            <div class="sample-item" data-type="sawtooth">
                                <i class="fas fa-chart-area"></i>
                                <span>锯齿波</span>
                            </div>
                            <div class="sample-item" data-type="pulse">
                                <i class="fas fa-signal"></i>
                                <span>脉冲波</span>
                            </div>
                        </div>

                        <div class="sample-controls">
                            <div class="control-group">
                                <label>频率: <span id="freqValue">1000Hz</span></label>
                                <input type="range" id="freqSlider" min="0" max="6" step="0.1" value="3">
                                <div class="freq-scale">
                                    <small>1Hz - 1MHz (对数刻度)</small>
                                </div>
                            </div>
                            <div class="control-group">
                                <label>幅度: <span id="ampValue">1.0</span></label>
                                <input type="range" id="ampSlider" min="0.1" max="3" step="0.1" value="1">
                            </div>
                            <div class="control-group">
                                <label>采样点数: <span id="pointsValue">512</span></label>
                                <input type="range" id="pointsSlider" min="100" max="2048" step="1" value="512">
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary" id="generateSignal" style="flex: 1;">
                                <i class="fas fa-play"></i>
                                生成信号
                            </button>
                        </div>
                    </div>

                    <!-- 噪声干扰卡片 -->
                    <div class="control-card" id="noiseCard">
                        <h3>
                            <i class="fas fa-wave-square"></i>
                            噪声干扰
                        </h3>

                        <div class="control-group">
                            <div class="noise-toggle-container">
                                <div class="noise-toggle-text-section">
                                    <i class="fas fa-wave-square"></i>
                                    <span>添加噪声干扰</span>
                                </div>
                                <div class="noise-toggle-button-section">
                                    <input type="checkbox" id="noiseCheck" class="noise-toggle-checkbox">
                                    <label for="noiseCheck" class="noise-toggle-label">
                                        <div class="noise-toggle-switch">
                                            <div class="noise-toggle-slider"></div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 噪声配置面板 -->
                        <div id="noiseConfigPanel" class="noise-config-panel" style="display: none;">
                            <div class="noise-types-grid">
                                <div class="noise-type-item" data-type="gaussian">
                                    <input type="checkbox" id="gaussianNoise" class="noise-checkbox">
                                    <label for="gaussianNoise">
                                        <i class="fas fa-wave-square"></i>
                                        <span>高斯噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="impulse">
                                    <input type="checkbox" id="impulseNoise" class="noise-checkbox">
                                    <label for="impulseNoise">
                                        <i class="fas fa-bolt"></i>
                                        <span>脉冲噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="white">
                                    <input type="checkbox" id="whiteNoise" class="noise-checkbox">
                                    <label for="whiteNoise">
                                        <i class="fas fa-snowflake"></i>
                                        <span>白噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="flicker">
                                    <input type="checkbox" id="flickerNoise" class="noise-checkbox">
                                    <label for="flickerNoise">
                                        <i class="fas fa-fire"></i>
                                        <span>闪烁噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="thermal">
                                    <input type="checkbox" id="thermalNoise" class="noise-checkbox">
                                    <label for="thermalNoise">
                                        <i class="fas fa-thermometer-half"></i>
                                        <span>热噪声</span>
                                    </label>
                                </div>
                                <div class="noise-type-item" data-type="colored">
                                    <input type="checkbox" id="coloredNoise" class="noise-checkbox">
                                    <label for="coloredNoise">
                                        <i class="fas fa-palette"></i>
                                        <span>色噪声</span>
                                    </label>
                                </div>
                            </div>

                            <div class="noise-amplitude-control">
                                <label>噪声幅度: <span id="noiseAmpValue">0.2</span></label>
                                <input type="range" id="noiseAmpSlider" min="0.01" max="1.0" step="0.01" value="0.2">
                                <div class="amplitude-scale">
                                    <small>0.01 - 1.0 (相对于信号幅度)</small>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" id="applyNoiseBtn">
                                <i class="fas fa-plus"></i>
                                应用噪声
                            </button>
                        </div>
                    </div>

                    <!-- 去噪算法选择卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-cogs"></i>
                            去噪算法
                        </h3>
                        <div class="algorithm-selector">
                            <div class="algorithm-grid">
                                <div class="algorithm-item">
                                    <input type="checkbox" id="movingAverage" checked>
                                    <label for="movingAverage">
                                        <i class="fas fa-chart-line"></i>
                                        平均滑动
                                    </label>
                                </div>
                                <div class="algorithm-item">
                                    <input type="checkbox" id="sgFilter">
                                    <label for="sgFilter">
                                        <i class="fas fa-filter"></i>
                                        SG滤波
                                    </label>
                                </div>
                                <div class="algorithm-item">
                                    <input type="checkbox" id="svdDenoise">
                                    <label for="svdDenoise">
                                        <i class="fas fa-project-diagram"></i>
                                        SVD降噪
                                    </label>
                                </div>
                                <div class="algorithm-item">
                                    <input type="checkbox" id="convolution">
                                    <label for="convolution">
                                        <i class="fas fa-wave-square"></i>
                                        卷积滑动
                                    </label>
                                </div>
                                <div class="algorithm-item">
                                    <input type="checkbox" id="emdDenoise">
                                    <label for="emdDenoise">
                                        <i class="fas fa-layer-group"></i>
                                        EMD分解
                                    </label>
                                </div>
                                <div class="algorithm-item">
                                    <input type="checkbox" id="waveletDenoise">
                                    <label for="waveletDenoise">
                                        <i class="fas fa-water"></i>
                                        小波去噪
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 算法参数调节卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-sliders-h"></i>
                            参数调节
                        </h3>
                        <div class="parameter-panels">
                            <!-- 平均滑动参数 -->
                            <div class="param-panel" id="movingAverageParams">
                                <h4>平均滑动参数</h4>
                                <div class="param-group">
                                    <label>窗口长度: <span id="windowSizeValue">5</span></label>
                                    <input type="range" id="windowSize" min="3" max="50" value="5">
                                </div>
                            </div>

                            <!-- SG滤波参数 -->
                            <div class="param-panel" id="sgFilterParams" style="display: none;">
                                <h4>SG滤波参数</h4>
                                <div class="param-group">
                                    <label>窗口长度: <span id="sgWindowValue">11</span></label>
                                    <input type="range" id="sgWindow" min="5" max="51" step="2" value="11">
                                </div>
                                <div class="param-group">
                                    <label>多项式阶次: <span id="sgOrderValue">3</span></label>
                                    <input type="range" id="sgOrder" min="1" max="10" value="3">
                                </div>
                            </div>

                            <!-- SVD参数 -->
                            <div class="param-panel" id="svdDenoiseParams" style="display: none;">
                                <h4>SVD降噪参数</h4>
                                <div class="param-group">
                                    <label>保留比例: <span id="svdRatioValue">0.8</span></label>
                                    <input type="range" id="svdRatio" min="0.1" max="1" step="0.1" value="0.8">
                                </div>
                            </div>

                            <!-- 卷积参数 -->
                            <div class="param-panel" id="convolutionParams" style="display: none;">
                                <h4>卷积滑动参数</h4>
                                <div class="param-group">
                                    <label>卷积核大小: <span id="kernelSizeValue">5</span></label>
                                    <input type="range" id="kernelSize" min="3" max="21" step="2" value="5">
                                </div>
                            </div>

                            <!-- EMD参数 -->
                            <div class="param-panel" id="emdDenoiseParams" style="display: none;">
                                <h4>EMD分解参数</h4>
                                <div class="param-group">
                                    <label>分解深度: <span id="emdDepthValue">5</span></label>
                                    <input type="range" id="emdDepth" min="2" max="10" value="5">
                                </div>
                            </div>

                            <!-- 小波去噪参数 -->
                            <div class="param-panel" id="waveletDenoiseParams" style="display: none;">
                                <h4>小波去噪参数</h4>
                                <div class="param-group">
                                    <label>小波类型:</label>
                                    <select id="waveletType">
                                        <option value="db4">Daubechies 4</option>
                                        <option value="db8">Daubechies 8</option>
                                        <option value="haar">Haar</option>
                                        <option value="biorthogonal">双正交</option>
                                        <option value="coiflets">Coiflets</option>
                                    </select>
                                </div>
                                <div class="param-group">
                                    <label>分解层数: <span id="waveletLevelsValue">4</span></label>
                                    <input type="range" id="waveletLevels" min="1" max="8" value="4">
                                </div>
                                <div class="param-group">
                                    <label>阈值方法:</label>
                                    <select id="thresholdMethod">
                                        <option value="soft">软阈值</option>
                                        <option value="hard">硬阈值</option>
                                    </select>
                                </div>
                                <div class="param-group">
                                    <label>阈值系数: <span id="thresholdFactorValue">0.1</span></label>
                                    <input type="range" id="thresholdFactor" min="0.01" max="0.5" step="0.01" value="0.1">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 执行去噪按钮 -->
                    <div class="control-card">
                        <button class="btn btn-primary" id="executeDenoising">
                            <i class="fas fa-magic"></i>
                            执行去噪
                        </button>
                        <button class="btn btn-outline" id="clearResults" style="margin-top: 10px;">
                            <i class="fas fa-trash"></i>
                            清除结果
                        </button>
                    </div>

                    <!-- 结果导出 -->
                    <div class="control-card export-card">
                        <h3>
                            <i class="fas fa-download"></i>
                            结果导出
                        </h3>
                        <div class="export-buttons">
                            <button class="btn-export" id="exportData">
                                <i class="fas fa-file-csv"></i>
                                导出数据
                            </button>
                            <button class="btn-export" id="exportChart">
                                <i class="fas fa-image"></i>
                                导出图表
                            </button>
                            <button class="btn-export" id="exportReport">
                                <i class="fas fa-file-alt"></i>
                                生成报告
                            </button>
                            <button class="btn-export" id="exportMetrics">
                                <i class="fas fa-calculator"></i>
                                导出指标
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 右侧可视化区域 -->
                <div class="visualization-area">
                    <!-- 时域波形显示 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>
                                <i class="fas fa-chart-line"></i>
                                时域波形对比
                            </h3>
                            <div class="chart-actions">
                                <button class="chart-btn" data-action="zoom-x-in" data-chart="time" title="横轴放大">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-x-out" data-chart="time" title="横轴缩小">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-y-in" data-chart="time" title="纵轴放大">
                                    <i class="fas fa-arrows-alt-v"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-y-out" data-chart="time" title="纵轴缩小">
                                    <i class="fas fa-compress-arrows-alt"></i>
                                </button>
                                <button class="chart-btn" data-action="export" data-chart="time" title="导出PNG">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button class="chart-btn" data-action="export-jpg" data-chart="time" title="导出JPG">
                                    <i class="fas fa-file-image"></i>
                                </button>
                                <button class="chart-btn" data-action="export-excel" data-chart="time" title="导出Excel">
                                    <i class="fas fa-file-excel"></i>
                                </button>
                                <button class="chart-btn" data-action="auto" data-chart="time" title="自动缩放">
                                    <i class="fas fa-magic"></i>
                                </button>
                                <button class="chart-btn" data-action="fullscreen" data-chart="time" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="timeChart"></canvas>
                        </div>
                    </div>

                    <!-- 频谱分析显示 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>
                                <i class="fas fa-chart-bar"></i>
                                频谱分析对比
                            </h3>
                            <div class="chart-actions">
                                <button class="chart-btn" data-action="zoom-x-in" data-chart="frequency" title="横轴放大">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-x-out" data-chart="frequency" title="横轴缩小">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-y-in" data-chart="frequency" title="纵轴放大">
                                    <i class="fas fa-arrows-alt-v"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-y-out" data-chart="frequency" title="纵轴缩小">
                                    <i class="fas fa-compress-arrows-alt"></i>
                                </button>
                                <button class="chart-btn" data-action="export" data-chart="frequency" title="导出PNG">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button class="chart-btn" data-action="export-jpg" data-chart="frequency" title="导出JPG">
                                    <i class="fas fa-file-image"></i>
                                </button>
                                <button class="chart-btn" data-action="export-excel" data-chart="frequency" title="导出Excel">
                                    <i class="fas fa-file-excel"></i>
                                </button>
                                <button class="chart-btn" data-action="auto" data-chart="frequency" title="自动缩放">
                                    <i class="fas fa-magic"></i>
                                </button>
                                <button class="chart-btn" data-action="fullscreen" data-chart="frequency" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="frequencyChart"></canvas>
                        </div>
                    </div>

                    <!-- 效果评估指标 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>
                                <i class="fas fa-chart-pie"></i>
                                去噪效果评估
                            </h3>
                            <div class="chart-actions">
                                <button class="chart-btn" data-action="zoom-x-in" data-chart="comparison" title="横轴放大">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-x-out" data-chart="comparison" title="横轴缩小">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-y-in" data-chart="comparison" title="纵轴放大">
                                    <i class="fas fa-arrows-alt-v"></i>
                                </button>
                                <button class="chart-btn" data-action="zoom-y-out" data-chart="comparison" title="纵轴缩小">
                                    <i class="fas fa-compress-arrows-alt"></i>
                                </button>
                                <button class="chart-btn" data-action="export" data-chart="comparison" title="导出PNG">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button class="chart-btn" data-action="export-jpg" data-chart="comparison" title="导出JPG">
                                    <i class="fas fa-file-image"></i>
                                </button>
                                <button class="chart-btn" data-action="export-excel" data-chart="comparison" title="导出Excel">
                                    <i class="fas fa-file-excel"></i>
                                </button>
                                <button class="chart-btn" data-action="auto" data-chart="comparison" title="自动缩放">
                                    <i class="fas fa-magic"></i>
                                </button>
                                <button class="chart-btn" data-action="fullscreen" data-chart="comparison" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        <div class="metrics-grid" id="metricsGrid">
                            <!-- 动态生成评估指标 -->
                        </div>

                        <!-- 算法对比图表 -->
                        <div class="chart-wrapper" style="margin-top: 20px;">
                            <canvas id="comparisonChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 固定水平滚动条 -->
    <div class="horizontal-scrollbar">
        <div class="horizontal-scrollbar-content"></div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/signal-denoise.js"></script>

    <!-- 备用加载隐藏机制 -->
    <script>
        // 如果5秒后页面还在加载，强制隐藏加载动画
        setTimeout(() => {
            const pageLoader = document.getElementById('pageLoader');
            if (pageLoader && !pageLoader.classList.contains('loaded')) {
                console.warn('强制隐藏页面加载动画');
                pageLoader.classList.add('loaded');
            }
        }, 5000);

        // 检查是否有JavaScript错误
        window.addEventListener('error', (e) => {
            console.error('JavaScript错误:', e.error);
            setTimeout(() => {
                const pageLoader = document.getElementById('pageLoader');
                if (pageLoader) {
                    pageLoader.classList.add('loaded');
                }
            }, 1000);
        });

        // 同步水平滚动条和主内容区域的滚动
        document.addEventListener('DOMContentLoaded', function() {
            const horizontalScrollbar = document.querySelector('.horizontal-scrollbar');
            const mainContent = document.querySelector('.main-content');

            if (horizontalScrollbar && mainContent) {
                // 当底部滚动条滚动时，同步主内容区域
                horizontalScrollbar.addEventListener('scroll', function() {
                    mainContent.scrollLeft = horizontalScrollbar.scrollLeft;
                });

                // 当主内容区域滚动时，同步底部滚动条
                mainContent.addEventListener('scroll', function() {
                    horizontalScrollbar.scrollLeft = mainContent.scrollLeft;
                });

                console.log('水平滚动条同步已初始化');
            }
        });
    </script>
</body>
</html>
