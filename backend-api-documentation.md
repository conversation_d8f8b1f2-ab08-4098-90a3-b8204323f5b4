# 数据分析平台后端API接口文档

## 1. 项目概述

### 1.1 系统架构
- **前端**: HTML5 + JavaScript + Chart.js
- **后端**: RESTful API (建议使用 Node.js/Express 或 Python/FastAPI)
- **数据库**: MongoDB/PostgreSQL + Redis(缓存)
- **文件存储**: 本地存储/云存储(OSS/S3)

### 1.2 基础配置
```
Base URL: https://api.dataplatform.com/v1
Content-Type: application/json
Authentication: Bearer Token
```

## 2. 认证与用户管理

### 2.1 用户注册
```http
POST /auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string", 
  "password": "string",
  "confirmPassword": "string"
}

Response:
{
  "success": true,
  "message": "注册成功",
  "data": {
    "userId": "string",
    "username": "string",
    "email": "string",
    "token": "string"
  }
}
```

### 2.2 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "email": "string",
  "password": "string"
}

Response:
{
  "success": true,
  "data": {
    "token": "string",
    "user": {
      "id": "string",
      "username": "string",
      "email": "string"
    }
  }
}
```

## 3. 数据管理模块

### 3.1 文件上传
```http
POST /data/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

FormData:
- files: File[] (最多3个文件)
- description: string (可选)

Response:
{
  "success": true,
  "data": {
    "uploadId": "string",
    "files": [
      {
        "fileId": "string",
        "fileName": "string",
        "fileSize": number,
        "fileType": "string",
        "uploadTime": "ISO8601",
        "downloadUrl": "string"
      }
    ]
  }
}
```

### 3.2 数据解析
```http
POST /data/parse
Content-Type: application/json
Authorization: Bearer {token}

{
  "fileId": "string",
  "parseOptions": {
    "format": "csv|excel|json|txt",
    "delimiter": "string", // CSV分隔符
    "encoding": "utf-8|gbk",
    "hasHeader": boolean
  }
}

Response:
{
  "success": true,
  "data": {
    "dataId": "string",
    "parsedData": number[],
    "metadata": {
      "totalPoints": number,
      "sampleRate": number,
      "duration": number,
      "statistics": {
        "mean": number,
        "std": number,
        "min": number,
        "max": number
      }
    }
  }
}
```

### 3.3 获取历史数据
```http
GET /data/history
Authorization: Bearer {token}
Query Parameters:
- page: number (默认1)
- limit: number (默认20)
- type: "upload|serial|sample"

Response:
{
  "success": true,
  "data": {
    "items": [
      {
        "dataId": "string",
        "fileName": "string",
        "uploadTime": "ISO8601",
        "dataType": "string",
        "fileSize": number,
        "metadata": object
      }
    ],
    "pagination": {
      "page": number,
      "limit": number,
      "total": number,
      "totalPages": number
    }
  }
}
```

## 4. 串口调试模块

### 4.1 串口配置保存
```http
POST /serial/config
Content-Type: application/json
Authorization: Bearer {token}

{
  "configName": "string",
  "baudRate": number,
  "dataBits": number,
  "stopBits": number,
  "parity": "none|even|odd",
  "flowControl": "none|hardware|software"
}

Response:
{
  "success": true,
  "data": {
    "configId": "string",
    "configName": "string"
  }
}
```

### 4.2 串口数据记录
```http
POST /serial/data
Content-Type: application/json
Authorization: Bearer {token}

{
  "sessionId": "string",
  "data": {
    "timestamp": "ISO8601",
    "direction": "sent|received",
    "content": "string", // 十六进制字符串
    "format": "hex|ascii|decimal"
  }
}

Response:
{
  "success": true,
  "data": {
    "recordId": "string"
  }
}
```

### 4.3 获取串口会话历史
```http
GET /serial/sessions
Authorization: Bearer {token}
Query Parameters:
- page: number
- limit: number
- startDate: ISO8601
- endDate: ISO8601

Response:
{
  "success": true,
  "data": {
    "sessions": [
      {
        "sessionId": "string",
        "startTime": "ISO8601",
        "endTime": "ISO8601",
        "config": object,
        "statistics": {
          "sentBytes": number,
          "receivedBytes": number,
          "packets": number,
          "errors": number
        }
      }
    ]
  }
}
```

## 5. 信号处理模块

### 5.1 FFT分析
```http
POST /signal/fft
Content-Type: application/json
Authorization: Bearer {token}

{
  "dataId": "string",
  "options": {
    "windowType": "hanning|hamming|blackman",
    "windowSize": number,
    "overlap": number, // 0-1
    "zeroPadding": boolean
  }
}

Response:
{
  "success": true,
  "data": {
    "analysisId": "string",
    "frequencyData": number[],
    "phaseData": number[],
    "powerSpectralDensity": number[],
    "dominantFrequencies": [
      {
        "frequency": number,
        "amplitude": number,
        "phase": number
      }
    ]
  }
}
```

### 5.2 信号降噪
```http
POST /signal/denoise
Content-Type: application/json
Authorization: Bearer {token}

{
  "dataId": "string",
  "method": "wavelet|butterworth|median|gaussian",
  "parameters": {
    "cutoffFrequency": number, // 截止频率
    "order": number, // 滤波器阶数
    "waveletType": "db4|db8|haar", // 小波类型
    "threshold": number // 阈值
  }
}

Response:
{
  "success": true,
  "data": {
    "denoisedDataId": "string",
    "denoisedData": number[],
    "metrics": {
      "snrImprovement": number,
      "mseReduction": number,
      "correlationCoefficient": number
    }
  }
}
```

### 5.3 包络分析
```http
POST /signal/envelope
Content-Type: application/json
Authorization: Bearer {token}

{
  "dataId": "string",
  "method": "hilbert|peak|rms",
  "windowSize": number
}

Response:
{
  "success": true,
  "data": {
    "envelopeData": number[],
    "instantaneousPhase": number[],
    "instantaneousFrequency": number[]
  }
}
```

## 6. 数据导出模块

### 6.1 导出数据
```http
POST /export/data
Content-Type: application/json
Authorization: Bearer {token}

{
  "dataIds": ["string"],
  "format": "csv|excel|json|matlab",
  "options": {
    "includeMetadata": boolean,
    "compression": "none|zip|gzip"
  }
}

Response:
{
  "success": true,
  "data": {
    "exportId": "string",
    "downloadUrl": "string",
    "expiresAt": "ISO8601"
  }
}
```

### 6.2 导出图表
```http
POST /export/charts
Content-Type: application/json
Authorization: Bearer {token}

{
  "chartConfigs": [
    {
      "type": "time|frequency|envelope|phase",
      "dataId": "string",
      "format": "png|pdf|svg",
      "resolution": "720p|1080p|4k"
    }
  ]
}

Response:
{
  "success": true,
  "data": {
    "exportId": "string",
    "files": [
      {
        "chartType": "string",
        "downloadUrl": "string"
      }
    ]
  }
}
```

## 7. 反馈管理模块

### 7.1 提交反馈
```http
POST /feedback
Content-Type: application/json
Authorization: Bearer {token} (可选)

{
  "title": "string",
  "category": "bug|feature|improvement|question",
  "priority": "low|medium|high|urgent",
  "content": "string",
  "pageUrl": "string",
  "contactInfo": {
    "name": "string",
    "email": "string"
  },
  "attachments": ["fileId"]
}

Response:
{
  "success": true,
  "data": {
    "feedbackId": "string",
    "trackingNumber": "string"
  }
}
```

### 7.2 获取反馈列表
```http
GET /feedback
Authorization: Bearer {token}
Query Parameters:
- page: number
- limit: number
- status: "new|processing|resolved|closed"
- category: "bug|feature|improvement|question"
- priority: "low|medium|high|urgent"

Response:
{
  "success": true,
  "data": {
    "feedbacks": [
      {
        "id": "string",
        "title": "string",
        "category": "string",
        "priority": "string",
        "status": "string",
        "createdAt": "ISO8601",
        "updatedAt": "ISO8601",
        "commentsCount": number
      }
    ],
    "statistics": {
      "total": number,
      "new": number,
      "processing": number,
      "resolved": number
    }
  }
}
```

### 7.3 反馈评论
```http
POST /feedback/{feedbackId}/comments
Content-Type: application/json
Authorization: Bearer {token} (可选)

{
  "content": "string",
  "author": "string" // 匿名时使用
}

Response:
{
  "success": true,
  "data": {
    "commentId": "string",
    "content": "string",
    "author": "string",
    "createdAt": "ISO8601"
  }
}
```

## 8. 系统统计模块

### 8.1 获取系统统计
```http
GET /stats/system
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "users": {
      "total": number,
      "active": number,
      "newToday": number
    },
    "data": {
      "totalFiles": number,
      "totalSize": number,
      "uploadsToday": number
    },
    "feedback": {
      "total": number,
      "resolved": number,
      "pending": number
    },
    "performance": {
      "avgResponseTime": number,
      "uptime": number,
      "errorRate": number
    }
  }
}
```

### 8.2 用户活动统计
```http
GET /stats/user-activity
Authorization: Bearer {token}
Query Parameters:
- period: "day|week|month|year"
- startDate: ISO8601
- endDate: ISO8601

Response:
{
  "success": true,
  "data": {
    "timeline": [
      {
        "date": "ISO8601",
        "uploads": number,
        "analyses": number,
        "exports": number
      }
    ],
    "summary": {
      "totalUploads": number,
      "totalAnalyses": number,
      "totalExports": number
    }
  }
}
```

## 9. 实时通信模块 (WebSocket)

### 9.1 串口数据实时传输
```javascript
// WebSocket连接
ws://api.dataplatform.com/ws/serial/{sessionId}

// 发送数据
{
  "type": "serial_data",
  "data": {
    "content": "string",
    "format": "hex|ascii|decimal"
  }
}

// 接收数据
{
  "type": "serial_data",
  "data": {
    "timestamp": "ISO8601",
    "content": "string",
    "direction": "received"
  }
}
```

### 9.2 实时分析结果推送
```javascript
// 分析进度推送
{
  "type": "analysis_progress",
  "data": {
    "analysisId": "string",
    "progress": number, // 0-100
    "stage": "string",
    "estimatedTime": number
  }
}

// 分析完成推送
{
  "type": "analysis_complete",
  "data": {
    "analysisId": "string",
    "result": object
  }
}
```

## 10. 错误处理

### 10.1 标准错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息",
    "timestamp": "ISO8601"
  }
}
```

### 10.2 常见错误码
- `AUTH_001`: 认证失败
- `AUTH_002`: Token过期
- `DATA_001`: 文件格式不支持
- `DATA_002`: 文件大小超限
- `SIGNAL_001`: 信号处理参数错误
- `EXPORT_001`: 导出格式不支持
- `RATE_001`: 请求频率超限

## 11. 数据库设计

### 11.1 用户表 (users)
```sql
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE
);
```

### 11.2 数据文件表 (data_files)
```sql
CREATE TABLE data_files (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) REFERENCES users(id),
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  metadata JSON,
  is_deleted BOOLEAN DEFAULT FALSE
);
```

### 11.3 分析结果表 (analysis_results)
```sql
CREATE TABLE analysis_results (
  id VARCHAR(36) PRIMARY KEY,
  data_file_id VARCHAR(36) REFERENCES data_files(id),
  analysis_type VARCHAR(50) NOT NULL,
  parameters JSON,
  result_data JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  processing_time INTEGER -- 毫秒
);
```

## 12. 部署建议

### 12.1 技术栈推荐
- **Node.js + Express**: 快速开发，生态丰富
- **Python + FastAPI**: 科学计算支持好，性能优秀
- **数据库**: PostgreSQL + Redis
- **文件存储**: MinIO/AWS S3
- **消息队列**: Redis/RabbitMQ

### 12.2 性能优化
- 使用CDN加速静态资源
- 数据库连接池
- Redis缓存热点数据
- 异步处理大文件分析
- 分页查询优化

### 12.3 安全措施
- JWT Token认证
- API请求频率限制
- 文件类型和大小验证
- SQL注入防护
- XSS防护
- HTTPS强制使用
