const fs = require('fs');
const path = require('path');

// 创建libs目录结构
const libDirs = ['libs/js', 'libs/css', 'libs/fonts'];
libDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`Created directory: ${dir}`);
    }
});

// 复制文件的函数
function copyFile(src, dest) {
    try {
        if (fs.existsSync(src)) {
            fs.copyFileSync(src, dest);
            console.log(`Copied: ${src} -> ${dest}`);
        } else {
            console.warn(`Source file not found: ${src}`);
        }
    } catch (error) {
        console.error(`Error copying ${src} to ${dest}:`, error.message);
    }
}

// 复制必要的库文件
const filesToCopy = [
    // Chart.js
    {
        src: 'node_modules/chart.js/dist/chart.umd.js',
        dest: 'libs/js/chart.min.js'
    },
    // Chart.js zoom plugin
    {
        src: 'node_modules/chartjs-plugin-zoom/dist/chartjs-plugin-zoom.min.js',
        dest: 'libs/js/chartjs-plugin-zoom.min.js'
    },
    // XLSX
    {
        src: 'node_modules/xlsx/dist/xlsx.full.min.js',
        dest: 'libs/js/xlsx.full.min.js'
    },
    // Font Awesome CSS
    {
        src: 'node_modules/font-awesome/css/font-awesome.min.css',
        dest: 'libs/css/font-awesome.min.css'
    }
];

console.log('Setting up local libraries...');
filesToCopy.forEach(file => {
    copyFile(file.src, file.dest);
});

// 复制Font Awesome字体文件
const fontSrc = 'node_modules/font-awesome/fonts';
const fontDest = 'libs/fonts';

if (fs.existsSync(fontSrc)) {
    const fontFiles = fs.readdirSync(fontSrc);
    fontFiles.forEach(file => {
        copyFile(path.join(fontSrc, file), path.join(fontDest, file));
    });
}

console.log('Local libraries setup complete!');
